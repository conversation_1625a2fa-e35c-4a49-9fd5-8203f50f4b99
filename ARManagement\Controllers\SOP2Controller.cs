﻿using ARManagement.BaseRepository.Interface;
using ARManagement.Data;
using ARManagement.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace ARManagement.Controllers
{
    public class SOP2Controller : MyBaseApiController
    {
        private readonly IBaseRepository _baseRepository;
        private readonly ResponseCodeHelper _responseCodeHelper;
        private string _savePath = string.Empty;
        private readonly ARManagementContext _context;

        public SOP2Controller(
            IBaseRepository baseRepository,
            ResponseCodeHelper responseCodeHelper,
            ARManagementContext context)
        {
            _baseRepository = baseRepository;
            _responseCodeHelper = responseCodeHelper;
            _savePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "upload");
            _context = context;
        }

        /// <summary>
        /// 依據MachineAddId取得所有SOP(包含眼鏡使用)
        /// </summary>
        /// <param name="post"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult<ApiResult<List<SOP2>>>> GetAllSOPByMachineAddId(PostAllSOP2 post)
        {
            ApiResult<List<SOP2>> apiResult = new ApiResult<List<SOP2>>(jwtToken.Token);

            try
            {
                // 設置動態 Schema
                _context.Database.ExecuteSqlRaw($"SET search_path TO '{_context.GetSchemaFromContext()}'");

                var machineAddWhere = $@"""MachineAddId"" = @MachineAddId";
                var machineAdd = await _baseRepository.GetOneAsync<MachineAdd>("MachineAdd", machineAddWhere, new { MachineAddId = post.Id });

                if (machineAdd == null || machineAdd.Deleted == (byte)DeletedDataEnum.True)
                {
                    apiResult.Code = "4004";
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }

                // 構建查詢條件，如果提供了 KnowledgeBaseId 則添加過濾
                var sopWhere = $@"""Deleted"" = 0 AND ""MachineAddId"" = @MachineAddId";
                object queryParams = new { MachineAddId = machineAdd.MachineAddId };

                if (post.KnowledgeBaseId.HasValue && post.KnowledgeBaseId.Value > 0)
                {
                    sopWhere += $@" AND ""KnowledgeBaseId"" = @KnowledgeBaseId";
                    queryParams = new { MachineAddId = machineAdd.MachineAddId, KnowledgeBaseId = post.KnowledgeBaseId.Value };
                    Console.WriteLine($"Filtering SOPs by KnowledgeBaseId: {post.KnowledgeBaseId.Value}");
                }

                var sop2s = await _baseRepository.GetAllAsync<SOP2>("SOP2", sopWhere, queryParams, "\"SOP2Step\" ASC");

                foreach (var sop2 in sop2s)
                {
                    if (!string.IsNullOrEmpty(sop2.SOP2Image))
                    {
                        sop2.SOP2Image = $"{baseURL}upload/machineAdd/{machineAdd.MachineAddId}/sop2/{sop2.SOP2Id}/{sop2.SOP2Image}";
                    }

                    if (!string.IsNullOrEmpty(sop2.SOP2RemarkImage))
                    {
                        sop2.SOP2RemarkImage = $"{baseURL}upload/machineAdd/{machineAdd.MachineAddId}/sop2/{sop2.SOP2Id}/{sop2.SOP2RemarkImage}";
                    }

                    if (!string.IsNullOrEmpty(sop2.SOPVideo))
                    {
                        sop2.SOPVideo = $"{baseURL}upload/machineAdd/{machineAdd.MachineAddId}/sop2/{sop2.SOP2Id}/{sop2.SOPVideo}";
                    }

                    if (!string.IsNullOrEmpty(sop2.T3DModels))
                    {
                        sop2.T3DModels = JsonConvert.SerializeObject(JsonConvert.DeserializeObject<List<int>>(sop2.T3DModels));
                    }

                    // 載入 SOPModel 資料（只包含該 SOP 的模型，不包含共用模型）
                    var sopModelWhere = $@"""Deleted"" = 0 AND ""SOPId"" = @SOPId AND (""IsCommon"" = 0 OR ""IsCommon"" IS NULL)";
                    var sopModels = await _baseRepository.GetAllAsync<SOP2Model>("SOPModel", sopModelWhere, new { SOPId = sop2.SOP2Id });

                    sop2.SOPModels = sopModels.Select(sm => new SOP2ModelDTO
                    {
                        SOPModelId = sm.SOPModelId,
                        Deleted = sm.Deleted, // 添加 Deleted 欄位
                        SOPModelImage = !string.IsNullOrEmpty(sm.SOPModelImage)
                            ? $"{baseURL}upload/machineAdd/{machineAdd.MachineAddId}/sop2/{sop2.SOP2Id}/model/{sm.SOPModelImage}"
                            : string.Empty,
                        SOPModelFile = !string.IsNullOrEmpty(sm.SOPModelFile)
                            ? $"{baseURL}upload/machineAdd/{machineAdd.MachineAddId}/sop2/{sop2.SOP2Id}/model/{sm.SOPModelFile}"
                            : string.Empty,
                        SOPModelPX = sm.SOPModelPX,
                        SOPModelPY = sm.SOPModelPY,
                        SOPModelPZ = sm.SOPModelPZ,
                        SOPModelRX = sm.SOPModelRX,
                        SOPModelRY = sm.SOPModelRY,
                        SOPModelRZ = sm.SOPModelRZ,
                        SOPModelSX = sm.SOPModelSX,
                        SOPModelSY = sm.SOPModelSY,
                        SOPModelSZ = sm.SOPModelSZ
                    }).ToList();
                }

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                apiResult.Result = sop2s;
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = $"未知的錯誤: {ex.Message}";
                exceptionMsg = ex.ToString();
                stackTrace = new StackTrace(ex);
            }

            return Ok(apiResult);
        }



        /// <summary>
        /// 檢查模型在當前知識庫的使用情況 (主要使用)
        /// </summary>
        /// <param name="post"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult<ApiResult<ModelUsageInfo>>> CheckModelUsage([FromBody] CheckModelUsageRequest post)
        {
            ApiResult<ModelUsageInfo> apiResult = new ApiResult<ModelUsageInfo>(jwtToken.Token);
            try
            {
                // 檢查Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }

                // 設置動態 Schema
                _context.Database.ExecuteSqlRaw($"SET search_path TO '{_context.GetSchemaFromContext()}'");

                // 查詢該模型在當前知識庫中被哪些SOP步驟使用
                // 查找所有使用該模型的SOP記錄（通過檔案名稱精確匹配）
                Console.WriteLine($"CheckModelUsage - Searching for: ImageName={post.ModelImageName}, FileName={post.ModelFileName}");

                var usageQuery = $@"
                    SELECT DISTINCT s.""SOP2Id"", s.""SOP2Step"", s.""SOP2Name"", sm.""SOPModelId""
                    FROM ""SOP2"" s
                    INNER JOIN ""SOPModel"" sm ON s.""SOP2Id"" = sm.""SOPId""
                    WHERE s.""MachineAddId"" = @MachineAddId
                    AND s.""KnowledgeBaseId"" = @KnowledgeBaseId
                    AND s.""Deleted"" = 0
                    AND sm.""Deleted"" = 0
                    AND (sm.""SOPModelImage"" LIKE '%/' || @ModelImageName OR sm.""SOPModelImage"" = @ModelImageName OR sm.""SOPModelFile"" LIKE '%/' || @ModelFileName OR sm.""SOPModelFile"" = @ModelFileName)
                    AND (sm.""IsCommon"" = 0 OR sm.""IsCommon"" IS NULL)
                    ORDER BY s.""SOP2Step""";

                var usageResults = await _baseRepository.GetAllAsync<dynamic>(usageQuery, new
                {
                    MachineAddId = post.MachineAddId,
                    KnowledgeBaseId = post.KnowledgeBaseId,
                    ModelImageName = post.ModelImageName,
                    ModelFileName = post.ModelFileName
                });

                var usageInfo = new ModelUsageInfo
                {
                    ModelImageName = post.ModelImageName,
                    ModelFileName = post.ModelFileName,
                    IsUsed = usageResults.Any(),
                    UsageCount = usageResults.Count(),
                    UsedInSteps = usageResults.Select(r => new SOPStepInfo
                    {
                        SOP2Id = (int)r.SOP2Id,
                        SOP2Step = (int)r.SOP2Step,
                        SOP2Name = r.SOP2Name?.ToString() ?? "",
                        SOPModelId = (int)r.SOPModelId
                    }).ToList()
                };

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                apiResult.Result = usageInfo;
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = $"未知的錯誤: {ex.Message}";
            }

            return Ok(apiResult);
        }

        /// <summary>
        /// 級聯刪除知識庫模型（刪除3D Model List中的模型及其在當前知識庫所有SOP中的使用） (主要使用)
        /// </summary>
        /// <param name="post"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult<ApiResult<CascadeDeleteResult>>> CascadeDeleteModel([FromBody] CascadeDeleteModelRequest post)
        {
            ApiResult<CascadeDeleteResult> apiResult = new ApiResult<CascadeDeleteResult>(jwtToken.Token);
            try
            {
                // 檢查Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }

                // 設置動態 Schema
                _context.Database.ExecuteSqlRaw($"SET search_path TO '{_context.GetSchemaFromContext()}'");

                // 1. 首先查找要刪除的知識庫模型（IsCommon = 1）
                Console.WriteLine($"CascadeDeleteModel - Searching for: ImageName={post.ModelImageName}, FileName={post.ModelFileName}");

                var commonModelWhere = $@"""Deleted"" = 0 AND ""IsCommon"" = 1 AND ""SOPId"" IN (SELECT ""SOP2Id"" FROM ""SOP2"" WHERE ""MachineAddId"" = @MachineAddId AND ""KnowledgeBaseId"" = @KnowledgeBaseId) AND (""SOPModelImage"" LIKE '%/' || @ModelImageName OR ""SOPModelImage"" = @ModelImageName) AND (""SOPModelFile"" LIKE '%/' || @ModelFileName OR ""SOPModelFile"" = @ModelFileName)";
                var commonModel = await _baseRepository.GetOneAsync<SOP2Model>("SOPModel", commonModelWhere, new
                {
                    MachineAddId = post.MachineAddId,
                    KnowledgeBaseId = post.KnowledgeBaseId,
                    ModelImageName = post.ModelImageName,
                    ModelFileName = post.ModelFileName
                });

                Console.WriteLine($"Found common model: {(commonModel != null ? commonModel.SOPModelId.ToString() : "null")}");

                if (commonModel == null)
                {
                    apiResult.Code = "4004";
                    apiResult.Message = "找不到指定的模型";
                    return Ok(apiResult);
                }

                // 2. 查找所有使用該模型的SOP模型記錄（IsCommon = 0 或 NULL）
                var usedModelsWhere = $@"""Deleted"" = 0 AND (""IsCommon"" = 0 OR ""IsCommon"" IS NULL) AND ""SOPId"" IN (SELECT ""SOP2Id"" FROM ""SOP2"" WHERE ""MachineAddId"" = @MachineAddId AND ""KnowledgeBaseId"" = @KnowledgeBaseId) AND (""SOPModelImage"" LIKE '%/' || @ModelImageName OR ""SOPModelImage"" = @ModelImageName OR ""SOPModelFile"" LIKE '%/' || @ModelFileName OR ""SOPModelFile"" = @ModelFileName)";
                var usedModels = await _baseRepository.GetAllAsync<SOP2Model>("SOPModel", usedModelsWhere, new
                {
                    MachineAddId = post.MachineAddId,
                    KnowledgeBaseId = post.KnowledgeBaseId,
                    ModelImageName = post.ModelImageName,
                    ModelFileName = post.ModelFileName
                });

                // 3. 準備刪除操作
                var deleteOperations = new List<Dictionary<string, object>>();

                // 添加知識庫模型到刪除列表
                deleteOperations.Add(new Dictionary<string, object>()
                {
                    { "SOPModelId", commonModel.SOPModelId},
                    { "@Deleted", 1},
                    { "@Updater", myUser.UserId},
                    { "@UpdateTime", DateTime.Now}
                });

                // 添加所有使用該模型的SOP模型到刪除列表
                foreach (var usedModel in usedModels)
                {
                    deleteOperations.Add(new Dictionary<string, object>()
                    {
                        { "SOPModelId", usedModel.SOPModelId},
                        { "@Deleted", 1},
                        { "@Updater", myUser.UserId},
                        { "@UpdateTime", DateTime.Now}
                    });
                }

                // 4. 執行批量軟刪除（使用現有的成熟方法）
                if (deleteOperations.Count > 0)
                {
                    await _baseRepository.UpdateMutiByCustomTable(deleteOperations, "SOPModel", "\"SOPModelId\" = @SOPModelId");
                }

                // 5. 準備返回結果
                var result = new CascadeDeleteResult
                {
                    ModelImageName = post.ModelImageName,
                    ModelFileName = post.ModelFileName,
                    DeletedCommonModelId = commonModel.SOPModelId,
                    DeletedSOPModelIds = usedModels.Select(m => m.SOPModelId).ToList(),
                    TotalDeletedCount = deleteOperations.Count,
                    AffectedSOPCount = usedModels.Select(m => m.SOPId).Distinct().Count()
                };

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                apiResult.Result = result;
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = $"未知的錯誤: {ex.Message}";
            }

            return Ok(apiResult);
        }

        /// <summary>
        /// 取得共用模型（3D Model List 的模型庫） (主要使用)
        /// </summary>
        /// <param name="post"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult<ApiResult<List<SOP2ModelDTO>>>> GetCommonModels([FromBody] PostCommonModels post)
        {
            ApiResult<List<SOP2ModelDTO>> apiResult = new ApiResult<List<SOP2ModelDTO>>(jwtToken.Token);
            try
            {
                // 設置動態 Schema
                _context.Database.ExecuteSqlRaw($"SET search_path TO '{_context.GetSchemaFromContext()}'");

                // 查詢機台資料
                var machineAdd = await _baseRepository.GetOneAsync<MachineAdd>("MachineAdd", "\"MachineAddId\" = @MachineAddId", new { MachineAddId = post.MachineAddId });
                if (machineAdd == null)
                {
                    apiResult.Code = "9999";
                    apiResult.Message = "找不到機台資料";
                    return Ok(apiResult);
                }

                // 查詢該知識記錄的模型庫（用於 3D Model List）
                // IsCommon = 1 表示模型庫，需要通過 JOIN SOP2 表來過濾 MachineAddId 和 KnowledgeBaseId
                string modelWhere = $@"""SOPModel"".""Deleted"" = 0 AND ""SOPModel"".""IsCommon"" = 1 AND ""SOP2"".""MachineAddId"" = @MachineAddId AND ""SOP2"".""KnowledgeBaseId"" = @KnowledgeBaseId";

                // 使用 JOIN 查詢來獲取模型庫
                var sql = $@"
                    SELECT ""SOPModel"".*
                    FROM ""SOPModel""
                    INNER JOIN ""SOP2"" ON ""SOPModel"".""SOPId"" = ""SOP2"".""SOP2Id""
                    WHERE {modelWhere}";

                var commonModels = await _baseRepository.GetAllAsync<SOP2Model>(sql, new { MachineAddId = post.MachineAddId, KnowledgeBaseId = post.KnowledgeBaseId });

                var commonModelDTOs = commonModels.Select(sm => new SOP2ModelDTO
                {
                    SOPModelId = sm.SOPModelId,
                    Deleted = sm.Deleted,
                    // 模型庫（IsCommon = 1）的模型使用知識庫路徑
                    SOPModelImage = !string.IsNullOrEmpty(sm.SOPModelImage)
                        ? $"{baseURL}upload/machineAdd/{machineAdd.MachineAddId}/knowledgeBase/{post.KnowledgeBaseId}/models/{sm.SOPModelImage}"
                        : string.Empty,
                    SOPModelFile = !string.IsNullOrEmpty(sm.SOPModelFile)
                        ? $"{baseURL}upload/machineAdd/{machineAdd.MachineAddId}/knowledgeBase/{post.KnowledgeBaseId}/models/{sm.SOPModelFile}"
                        : string.Empty,
                    SOPModelPX = sm.SOPModelPX,
                    SOPModelPY = sm.SOPModelPY,
                    SOPModelPZ = sm.SOPModelPZ,
                    SOPModelRX = sm.SOPModelRX,
                    SOPModelRY = sm.SOPModelRY,
                    SOPModelRZ = sm.SOPModelRZ,
                    SOPModelSX = sm.SOPModelSX,
                    SOPModelSY = sm.SOPModelSY,
                    SOPModelSZ = sm.SOPModelSZ
                }).ToList();

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                apiResult.Result = commonModelDTOs;
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = $"未知的錯誤: {ex.Message}";
            }

            return Ok(apiResult);
        }

        /// <summary>
        /// SOP2頁面儲存設定 - 保存SOP2操作流程的頁面配置和步驟設定 (主要使用)
        /// </summary>
        /// <param name="post">SOP2頁面設定資料</param>
        /// <returns>儲存結果狀態</returns>
        [RequestFormLimits(ValueLengthLimit = int.MaxValue, MultipartBodyLengthLimit = int.MaxValue)]
        [DisableRequestSizeLimit]
        [Consumes("multipart/form-data")]
        [HttpPut]
        public async Task<ActionResult<ApiResult<int>>> SaveSOP2([FromForm] PostSaveSOP2 post)
        {
            ApiResult<int> apiResult = new ApiResult<int>(jwtToken.Token);
            string step = "Initial step";
            try
            {
                #region 判斷Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 判斷帳號是否被刪除或失效
                if (myUser == null)
                {
                    apiResult.Code = "1003"; //該帳號已被刪除或失效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 判斷帳號是否為系統管理員
                if ((myUser.UserLevel & ((byte)UserLevelEnum.Admin | (byte)UserLevelEnum.Expert)) == 0)
                {
                    apiResult.Code = "3003"; //您不具有修改的權限
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                // 設置動態 Schema
                _context.Database.ExecuteSqlRaw($"SET search_path TO '{_context.GetSchemaFromContext()}'");

                FolderFunction folderFunction = new FolderFunction();
                var sopRootPath = Path.Combine(_savePath, "machineAdd", post.MachineAddId.ToString(), "sop2");

                #region 找出要新增的SOP
                var addSOPs = post.SOP2s.Where(x => x.SOP2Id == 0 ).ToList();
                foreach (var addSOP in addSOPs)
                {
                    string? sopImageName = null;
                    string? videoName = null;
                    string? remarkImageName = null;

                    if (addSOP.SOP2ImageObj != null)
                    {
                        sopImageName = folderFunction.FileProduceName(addSOP.SOP2ImageObj);
                    }

                    if (addSOP.SOPVideoObj != null)
                    {
                        videoName = folderFunction.FileProduceName(addSOP.SOPVideoObj);
                    }

                    if (addSOP.SOP2RemarkImageObj != null)
                    {
                        remarkImageName = folderFunction.FileProduceName(addSOP.SOP2RemarkImageObj);
                    }

                    var addSOP_Dict = new Dictionary<string, object>()
                    {
                        { "@MachineAddId", post.MachineAddId},
                        { "@KnowledgeBaseId", post.KnowledgeBaseId},
                        { "@SOP2Name", addSOP.SOP2Name },
                        { "@SOP2Step", addSOP.SOP2Step },
                        { "@SOP2Message", addSOP.SOP2Message },
                        { "@SOP2Image", sopImageName },
                        { "@SOPVideo", videoName },
                        { "@SOP2Remark", addSOP.SOP2Remark },
                        { "@SOP2RemarkImage", remarkImageName },
                        { "@PLC1", addSOP.PLC1 },
                        { "@PLC2", addSOP.PLC2 },
                        { "@PLC3", addSOP.PLC3 },
                        { "@PLC4", addSOP.PLC4 },
                        { "@T3DModels", addSOP.T3DModels ?? "[]" }, // 確保 T3DModels 為有效 JSON 字串
                        { "@Creator", myUser.UserId },
                    };

                    var sopId = await _baseRepository.AddOneByCustomTable(addSOP_Dict, "SOP2", "SOP2Id");
                    var sopPath = Path.Combine(sopRootPath, sopId.ToString());
                    folderFunction.CreateFolder(sopPath, 0);

                    if (!string.IsNullOrEmpty(sopImageName))
                    {
                        folderFunction.SavePathFile(addSOP.SOP2ImageObj, sopPath, sopImageName);
                    }

                    if (!string.IsNullOrEmpty(remarkImageName))
                    {
                        folderFunction.SavePathFile(addSOP.SOP2RemarkImageObj, sopPath, remarkImageName);
                    }

                    if (!string.IsNullOrEmpty(videoName))
                    {
                        folderFunction.SavePathFile(addSOP.SOPVideoObj, sopPath, videoName);
                    }

                    #region 處理 SOPModel 資料
                    if (addSOP.SOPModels != null && addSOP.SOPModels.Count > 0)
                    {
                        var addSOPModel_Dicts = new List<Dictionary<string, object>>();
                        var modelImageFiles = new List<IFormFile>();
                        var modelImageFileNames = new List<string>();
                        var modelFiles = new List<IFormFile>();
                        var modelFileNames = new List<string>();

                        foreach (var sopModel in addSOP.SOPModels)
                        {
                            if (sopModel.Deleted == 1) continue; // 跳過已刪除的模型

                            Console.WriteLine($"Processing SOPModel - Image: {sopModel.SOPModelImage}, File: {sopModel.SOPModelFile}");
                            Console.WriteLine($"SOPModel has ImageObj: {sopModel.SOPModelImageObj != null}, has FileObj: {sopModel.SOPModelFileObj != null}");

                            // 處理圖片
                            string? modelImageName = null;
                            if (sopModel.SOPModelImageObj != null)
                            {
                                // 新上傳的圖片檔案
                                modelImageName = folderFunction.FileProduceName(sopModel.SOPModelImageObj);
                                modelImageFiles.Add(sopModel.SOPModelImageObj);
                                modelImageFileNames.Add(modelImageName);
                            }
                            else if (!string.IsNullOrEmpty(sopModel.SOPModelImage))
                            {
                                // 從模型庫拖拽的圖片，暫時使用原始檔案名稱
                                if (sopModel.SOPModelImage.StartsWith("http"))
                                {
                                    // 從 URL 中提取檔案名稱
                                    var uri = new Uri(sopModel.SOPModelImage);
                                    modelImageName = Path.GetFileName(uri.LocalPath);
                                    Console.WriteLine($"Using existing image file: {modelImageName}");
                                }
                                else
                                {
                                    modelImageName = sopModel.SOPModelImage;
                                }
                            }

                            // 處理檔案
                            string? modelFileName = null;
                            if (sopModel.SOPModelFileObj != null)
                            {
                                // 新上傳的模型檔案
                                modelFileName = folderFunction.FileProduceName(sopModel.SOPModelFileObj);
                                modelFiles.Add(sopModel.SOPModelFileObj);
                                modelFileNames.Add(modelFileName);
                            }
                            else if (!string.IsNullOrEmpty(sopModel.SOPModelFile))
                            {
                                // 從模型庫拖拽的檔案，暫時使用原始檔案名稱
                                if (sopModel.SOPModelFile.StartsWith("http"))
                                {
                                    // 從 URL 中提取檔案名稱
                                    var uri = new Uri(sopModel.SOPModelFile);
                                    modelFileName = Path.GetFileName(uri.LocalPath);
                                    Console.WriteLine($"Using existing model file: {modelFileName}");
                                }
                                else
                                {
                                    modelFileName = sopModel.SOPModelFile;
                                }
                            }

                            Console.WriteLine($"Final modelImageName: {modelImageName}, modelFileName: {modelFileName}");

                            Dictionary<string, object> addSOPModel_Dict = new Dictionary<string, object>()
                            {
                                { "@SOPId", sopId},
                                { "@SOPModelImage", string.IsNullOrEmpty(modelImageName) ? DBNull.Value : modelImageName},
                                { "@SOPModelFile", string.IsNullOrEmpty(modelFileName) ? DBNull.Value : modelFileName},
                                { "@SOPModelPX", sopModel.SOPModelPX},
                                { "@SOPModelPY", sopModel.SOPModelPY},
                                { "@SOPModelPZ", sopModel.SOPModelPZ},
                                { "@SOPModelRX", sopModel.SOPModelRX},
                                { "@SOPModelRY", sopModel.SOPModelRY},
                                { "@SOPModelRZ", sopModel.SOPModelRZ},
                                { "@SOPModelSX", sopModel.SOPModelSX},
                                { "@SOPModelSY", sopModel.SOPModelSY},
                                { "@SOPModelSZ", sopModel.SOPModelSZ},
                                { "@IsCommon", 0},
                                { "@Creator", myUser.UserId},
                            };

                            addSOPModel_Dicts.Add(addSOPModel_Dict);
                        }

                        if (addSOPModel_Dicts.Count > 0)
                        {
                            await _baseRepository.AddMutiByCustomTable(addSOPModel_Dicts, "SOPModel");
                        }

                        // 保存模型檔案
                        var modelPath = Path.Combine(sopPath, "model");
                        if (modelImageFiles.Count > 0)
                        {
                            folderFunction.SavePathFile(modelImageFiles, modelPath, modelImageFileNames);
                        }
                        if (modelFiles.Count > 0)
                        {
                            folderFunction.SavePathFile(modelFiles, modelPath, modelFileNames);
                        }
                    }
                    #endregion
                }
                #endregion

                #region 找出要刪除的SOP
                var deleteSOPs = post.SOP2s.Where(x => x.Deleted == 1).ToList();

                var deleteSOP_Dicts = new List<Dictionary<string, object>>();

                foreach (var deleteSOP in deleteSOPs)
                {
                    var deleteSOP_Dict = new Dictionary<string, object>()
                    {
                        { "SOP2Id", deleteSOP.SOP2Id},
                        { "@Deleted", 1},
                        { "@Updater", myUser.UserId},
                        { "@UpdateTime", DateTime.Now}
                    };

                    deleteSOP_Dicts.Add(deleteSOP_Dict);
                }
                if (deleteSOP_Dicts.Count > 0)
                {
                    await _baseRepository.UpdateMutiByCustomTable(deleteSOP_Dicts, "SOP2", "\"SOP2Id\" = @SOP2Id");
                }

                foreach (var deleteSOP in deleteSOPs)
                {
                    var tempSOPPath = Path.Combine(sopRootPath, deleteSOP.SOP2Id.ToString());

                    DirectoryInfo directoryInfo = new DirectoryInfo(tempSOPPath);
                    if (directoryInfo.Exists)
                    {
                        directoryInfo.Delete(true);
                    }
                }
                #endregion

                #region 找出要修改的SOP
                var updateSOPs = post.SOP2s.Where(x => x.SOP2Id != 0 && x.Deleted == 0).ToList();

                var sopWhere = $@"""Deleted"" = 0 AND ""SOP2Id"" = ANY (@SOP2Ids)";
                var tempSops = await _baseRepository.GetAllAsync<SOP2>("SOP2", sopWhere, new { SOP2Ids = updateSOPs.Select(x => x.SOP2Id).ToList() });
                
                foreach (var updateSOP in updateSOPs)
                {
                    string? sopImageName = null;
                    string? remarkImageName = null;
                    string? videoName = updateSOP.SOPVideoObj != null ? folderFunction.FileProduceName(updateSOP.SOPVideoObj) : null;

                    // 處理圖片檔案
                    if (updateSOP.SOP2ImageObj != null)
                    {
                        sopImageName = folderFunction.FileProduceName(updateSOP.SOP2ImageObj);
                    }

                    // 處理備註圖片檔案
                    if (updateSOP.SOP2RemarkImageObj != null)
                    {
                        remarkImageName = folderFunction.FileProduceName(updateSOP.SOP2RemarkImageObj);
                    }

                    // 處理影片檔案
                    if (updateSOP.SOPVideoObj != null)
                    {
                        videoName = folderFunction.FileProduceName(updateSOP.SOPVideoObj);
                    }

                    var updateSOP_Dict = new Dictionary<string, object>()
                    {
                        { "SOP2Id", updateSOP.SOP2Id },
                        { "@SOP2Name", updateSOP.SOP2Name },
                        { "@SOP2Step", updateSOP.SOP2Step },
                        { "@SOP2Message", updateSOP.SOP2Message },
                        { "@SOP2Remark", updateSOP.SOP2Remark },
                        { "@T3DModels", updateSOP.T3DModels },
                        { "@Updater", myUser.UserId},
                        { "@UpdateTime", DateTime.Now}
                    };

                    // 處理圖片檔案
                    if (!string.IsNullOrEmpty(sopImageName))
                    {
                        updateSOP_Dict.Add("@SOP2Image", sopImageName);
                    }
                    else if (updateSOP.IsDeletedSOP2Image)
                    {
                        updateSOP_Dict.Add("@SOP2Image", null);
                    }

                    // 處理備註圖片檔案
                    if (!string.IsNullOrEmpty(remarkImageName))
                    {
                        updateSOP_Dict.Add("@SOP2RemarkImage", remarkImageName);
                    }
                    else if (updateSOP.IsDeletedSOP2RemarkImage)
                    {
                        updateSOP_Dict.Add("@SOP2RemarkImage", null);
                    }

                    // 處理影片檔案
                    if (!string.IsNullOrEmpty(videoName))
                    {
                        updateSOP_Dict.Add("@SOPVideo", videoName);
                    }
                    else if (updateSOP.IsDeletedSOPVideo)
                    {
                        if (updateSOP.IsDeletedSOPVideo)
                        {
                            updateSOP_Dict.Add("@SOPVideo", null);
                        }
                    }

                    // 更新語句中指定僅更新非ID欄位
                    await _baseRepository.UpdateOneByCustomTable(updateSOP_Dict, "SOP2", "\"SOP2Id\" = @SOP2Id");

                    var tempSelectSOP = tempSops.FirstOrDefault(x => x.SOP2Id == updateSOP.SOP2Id);

                    if (tempSelectSOP != null)
                    {
                        if (updateSOP.IsDeletedSOP2Image && !string.IsNullOrEmpty(tempSelectSOP.SOP2Image))
                        {
                            folderFunction.DeleteFile(Path.Combine(sopRootPath, tempSelectSOP.SOP2Id.ToString(), tempSelectSOP.SOP2Image));
                        }

                        if (updateSOP.IsDeletedSOP2RemarkImage && !string.IsNullOrEmpty(tempSelectSOP.SOP2RemarkImage))
                        {
                            folderFunction.DeleteFile(Path.Combine(sopRootPath, tempSelectSOP.SOP2Id.ToString(), tempSelectSOP.SOP2RemarkImage));
                        }

                        if (updateSOP.IsDeletedSOPVideo && !string.IsNullOrEmpty(tempSelectSOP.SOPVideo))
                        {
                            folderFunction.DeleteFile(Path.Combine(sopRootPath, tempSelectSOP.SOP2Id.ToString(), tempSelectSOP.SOPVideo));
                        }
                    }

                    if (tempSelectSOP != null)
                    {
                        if (!string.IsNullOrEmpty(sopImageName))
                        {
                            folderFunction.SavePathFile(updateSOP.SOP2ImageObj, Path.Combine(sopRootPath, tempSelectSOP.SOP2Id.ToString()), sopImageName);
                        }

                        if (!string.IsNullOrEmpty(remarkImageName))
                        {
                            folderFunction.SavePathFile(updateSOP.SOP2RemarkImageObj, Path.Combine(sopRootPath, tempSelectSOP.SOP2Id.ToString()), remarkImageName);
                        }

                        if (!string.IsNullOrEmpty(videoName))
                        {
                            folderFunction.SavePathFile(updateSOP.SOPVideoObj, Path.Combine(sopRootPath, tempSelectSOP.SOP2Id.ToString()), videoName);
                        }
                    }

                    #region 處理更新 SOP 的 SOPModel 資料
                    if (tempSelectSOP != null && updateSOP.SOPModels != null && updateSOP.SOPModels.Count > 0)
                    {
                        var addSOPModel_Dicts = new List<Dictionary<string, object>>();
                        var updateSOPModel_Dicts = new List<Dictionary<string, object>>();
                        var deleteSOPModel_Dicts = new List<Dictionary<string, object>>();
                        var modelImageFiles = new List<IFormFile>();
                        var modelImageFileNames = new List<string>();
                        var modelFiles = new List<IFormFile>();
                        var modelFileNames = new List<string>();

                        foreach (var sopModel in updateSOP.SOPModels)
                        {
                            if (sopModel.Deleted == 1)
                            {
                                // 標記刪除
                                var deleteSOPModel_Dict = new Dictionary<string, object>()
                                {
                                    { "SOPModelId", sopModel.SOPModelId},
                                    { "@Deleted", 1},
                                    { "@Updater", myUser.UserId},
                                    { "@UpdateTime", DateTime.Now}
                                };
                                deleteSOPModel_Dicts.Add(deleteSOPModel_Dict);
                                continue;
                            }

                            // 處理圖片
                            string? modelImageName = null;
                            if (sopModel.SOPModelImageObj != null)
                            {
                                // 有檔案物件，直接保存新上傳的檔案
                                modelImageName = folderFunction.FileProduceName(sopModel.SOPModelImageObj);
                                modelImageFiles.Add(sopModel.SOPModelImageObj);
                                modelImageFileNames.Add(modelImageName);
                                Console.WriteLine($"[NEW UPLOAD] Processing new uploaded image file: {modelImageName}");
                            }
                            else if (!string.IsNullOrEmpty(sopModel.SOPModelImage))
                            {
                                // 如果沒有檔案物件但有圖片路徑，需要複製檔案到目標位置
                                if (sopModel.SOPModelImage.StartsWith("http"))
                                {
                                    // 從 URL 中提取檔案名稱
                                    var uri = new Uri(sopModel.SOPModelImage);
                                    var originalFileName = Path.GetFileName(uri.LocalPath);

                                    // 檢查原始檔案是否存在
                                    // 從 URL 中提取相對路徑，例如：/upload/machineAdd/1/knowledgeBase/1/models/xxx.jpg
                                    var urlPath = uri.LocalPath; // 例如：/upload/machineAdd/1/knowledgeBase/1/models/xxx.jpg
                                    var relativePath = urlPath.StartsWith("/upload/") ? urlPath.Substring(8) : urlPath.TrimStart('/');
                                    relativePath = relativePath.Replace('/', Path.DirectorySeparatorChar);
                                    var sourcePath = Path.Combine(_savePath, relativePath);
                                    var targetPath = Path.Combine(sopRootPath, tempSelectSOP.SOP2Id.ToString(), "model");
                                    var targetFilePath = Path.Combine(targetPath, originalFileName);

                                    Console.WriteLine($"[IMAGE COPY] Checking file copy - Source: {sourcePath}, Target: {targetFilePath}");
                                    Console.WriteLine($"[IMAGE COPY] Source exists: {System.IO.File.Exists(sourcePath)}, Target exists: {System.IO.File.Exists(targetFilePath)}");

                                    try
                                    {
                                        if (System.IO.File.Exists(sourcePath) && !System.IO.File.Exists(targetFilePath))
                                        {
                                            // 確保目標目錄存在
                                            folderFunction.CreateFolder(targetPath, 0);
                                            Console.WriteLine($"[IMAGE COPY] Target directory created: {targetPath}");

                                            // 複製檔案到目標位置
                                            System.IO.File.Copy(sourcePath, targetFilePath);
                                            Console.WriteLine($"[IMAGE COPY] SUCCESS: Copied image file from {sourcePath} to {targetFilePath}");

                                            // 驗證複製是否成功
                                            if (System.IO.File.Exists(targetFilePath))
                                            {
                                                Console.WriteLine($"[IMAGE COPY] VERIFIED: Target file exists after copy");
                                            }
                                            else
                                            {
                                                Console.WriteLine($"[IMAGE COPY] ERROR: Target file does not exist after copy operation");
                                            }
                                        }
                                        else if (!System.IO.File.Exists(sourcePath))
                                        {
                                            Console.WriteLine($"[IMAGE COPY] WARNING: Source file does not exist: {sourcePath}");
                                        }
                                        else if (System.IO.File.Exists(targetFilePath))
                                        {
                                            Console.WriteLine($"[IMAGE COPY] INFO: Target file already exists: {targetFilePath}");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"[IMAGE COPY] ERROR: Exception during file copy: {ex.Message}");
                                        Console.WriteLine($"[IMAGE COPY] ERROR: Stack trace: {ex.StackTrace}");
                                    }

                                    modelImageName = originalFileName;
                                    Console.WriteLine($"Using existing image file: {modelImageName}");
                                }
                                else
                                {
                                    modelImageName = Path.GetFileName(sopModel.SOPModelImage);
                                }
                            }

                            // 處理檔案
                            string? modelFileName = null;
                            if (sopModel.SOPModelFileObj != null)
                            {
                                // 有檔案物件，直接保存新上傳的檔案
                                modelFileName = folderFunction.FileProduceName(sopModel.SOPModelFileObj);
                                modelFiles.Add(sopModel.SOPModelFileObj);
                                modelFileNames.Add(modelFileName);
                                Console.WriteLine($"[NEW UPLOAD] Processing new uploaded model file: {modelFileName}");
                            }
                            else if (!string.IsNullOrEmpty(sopModel.SOPModelFile))
                            {
                                // 如果沒有檔案物件但有檔案路徑，需要複製檔案到目標位置
                                if (sopModel.SOPModelFile.StartsWith("http"))
                                {
                                    // 從 URL 中提取檔案名稱
                                    var uri = new Uri(sopModel.SOPModelFile);
                                    var originalFileName = Path.GetFileName(uri.LocalPath);

                                    // 檢查原始檔案是否存在
                                    // 從 URL 中提取相對路徑，例如：/upload/machineAdd/1/knowledgeBase/1/models/xxx.glb
                                    var urlPath = uri.LocalPath; // 例如：/upload/machineAdd/1/knowledgeBase/1/models/xxx.glb
                                    var relativePath = urlPath.StartsWith("/upload/") ? urlPath.Substring(8) : urlPath.TrimStart('/');
                                    relativePath = relativePath.Replace('/', Path.DirectorySeparatorChar);
                                    var sourcePath = Path.Combine(_savePath, relativePath);
                                    var targetPath = Path.Combine(sopRootPath, tempSelectSOP.SOP2Id.ToString(), "model");
                                    var targetFilePath = Path.Combine(targetPath, originalFileName);

                                    Console.WriteLine($"[MODEL FILE COPY] Checking file copy - Source: {sourcePath}, Target: {targetFilePath}");
                                    Console.WriteLine($"[MODEL FILE COPY] Source exists: {System.IO.File.Exists(sourcePath)}, Target exists: {System.IO.File.Exists(targetFilePath)}");

                                    try
                                    {
                                        if (System.IO.File.Exists(sourcePath) && !System.IO.File.Exists(targetFilePath))
                                        {
                                            // 確保目標目錄存在
                                            folderFunction.CreateFolder(targetPath, 0);
                                            Console.WriteLine($"[MODEL FILE COPY] Target directory created: {targetPath}");

                                            // 複製檔案到目標位置
                                            System.IO.File.Copy(sourcePath, targetFilePath);
                                            Console.WriteLine($"[MODEL FILE COPY] SUCCESS: Copied model file from {sourcePath} to {targetFilePath}");

                                            // 驗證複製是否成功
                                            if (System.IO.File.Exists(targetFilePath))
                                            {
                                                Console.WriteLine($"[MODEL FILE COPY] VERIFIED: Target file exists after copy");
                                            }
                                            else
                                            {
                                                Console.WriteLine($"[MODEL FILE COPY] ERROR: Target file does not exist after copy operation");
                                            }
                                        }
                                        else if (!System.IO.File.Exists(sourcePath))
                                        {
                                            Console.WriteLine($"[MODEL FILE COPY] WARNING: Source file does not exist: {sourcePath}");
                                        }
                                        else if (System.IO.File.Exists(targetFilePath))
                                        {
                                            Console.WriteLine($"[MODEL FILE COPY] INFO: Target file already exists: {targetFilePath}");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"[MODEL FILE COPY] ERROR: Exception during file copy: {ex.Message}");
                                        Console.WriteLine($"[MODEL FILE COPY] ERROR: Stack trace: {ex.StackTrace}");
                                    }

                                    modelFileName = originalFileName;
                                    Console.WriteLine($"Using existing model file: {modelFileName}");
                                }
                                else
                                {
                                    modelFileName = Path.GetFileName(sopModel.SOPModelFile);
                                }
                            }

                            if (sopModel.SOPModelId == 0)
                            {
                                // 新增模型
                                Console.WriteLine($"Adding SOPModel - Image: {modelImageName}, File: {modelFileName}");
                                Console.WriteLine($"SOPModel data - Image: {sopModel.SOPModelImage}, File: {sopModel.SOPModelFile}");

                                var addSOPModel_Dict = new Dictionary<string, object>()
                                {
                                    { "@SOPId", updateSOP.SOP2Id},
                                    { "@SOPModelImage", string.IsNullOrEmpty(modelImageName) ? DBNull.Value : modelImageName},
                                    { "@SOPModelFile", string.IsNullOrEmpty(modelFileName) ? DBNull.Value : modelFileName},
                                    { "@SOPModelPX", sopModel.SOPModelPX},
                                    { "@SOPModelPY", sopModel.SOPModelPY},
                                    { "@SOPModelPZ", sopModel.SOPModelPZ},
                                    { "@SOPModelRX", sopModel.SOPModelRX},
                                    { "@SOPModelRY", sopModel.SOPModelRY},
                                    { "@SOPModelRZ", sopModel.SOPModelRZ},
                                    { "@SOPModelSX", sopModel.SOPModelSX},
                                    { "@SOPModelSY", sopModel.SOPModelSY},
                                    { "@SOPModelSZ", sopModel.SOPModelSZ},
                                    { "@IsCommon", 0},
                                    { "@Creator", myUser.UserId},
                                };
                                addSOPModel_Dicts.Add(addSOPModel_Dict);
                            }
                            else
                            {
                                // 更新模型
                                var updateSOPModel_Dict = new Dictionary<string, object>()
                                {
                                    { "SOPModelId", sopModel.SOPModelId},
                                    { "@SOPModelPX", sopModel.SOPModelPX},
                                    { "@SOPModelPY", sopModel.SOPModelPY},
                                    { "@SOPModelPZ", sopModel.SOPModelPZ},
                                    { "@SOPModelRX", sopModel.SOPModelRX},
                                    { "@SOPModelRY", sopModel.SOPModelRY},
                                    { "@SOPModelRZ", sopModel.SOPModelRZ},
                                    { "@SOPModelSX", sopModel.SOPModelSX},
                                    { "@SOPModelSY", sopModel.SOPModelSY},
                                    { "@SOPModelSZ", sopModel.SOPModelSZ},
                                    { "@Updater", myUser.UserId},
                                    { "@UpdateTime", DateTime.Now}
                                };

                                if (!string.IsNullOrEmpty(modelImageName))
                                {
                                    updateSOPModel_Dict.Add("@SOPModelImage", modelImageName);
                                }
                                else if (sopModel.IsDeletedSOPModelImage)
                                {
                                    updateSOPModel_Dict.Add("@SOPModelImage", DBNull.Value);
                                }

                                if (!string.IsNullOrEmpty(modelFileName))
                                {
                                    updateSOPModel_Dict.Add("@SOPModelFile", modelFileName);
                                }
                                else if (sopModel.IsDeletedSOPModelFile)
                                {
                                    updateSOPModel_Dict.Add("@SOPModelFile", DBNull.Value);
                                }

                                updateSOPModel_Dicts.Add(updateSOPModel_Dict);
                            }
                        }

                        // 執行資料庫操作
                        if (deleteSOPModel_Dicts.Count > 0)
                        {
                            await _baseRepository.UpdateMutiByCustomTable(deleteSOPModel_Dicts, "SOPModel", "\"SOPModelId\" = @SOPModelId");
                        }

                        if (addSOPModel_Dicts.Count > 0)
                        {
                            await _baseRepository.AddMutiByCustomTable(addSOPModel_Dicts, "SOPModel");
                        }

                        if (updateSOPModel_Dicts.Count > 0)
                        {
                            await _baseRepository.UpdateMutiByCustomTable(updateSOPModel_Dicts, "SOPModel", "\"SOPModelId\" = @SOPModelId");
                        }

                        // 保存模型檔案
                        var modelPath = Path.Combine(sopRootPath, tempSelectSOP.SOP2Id.ToString(), "model");
                        if (modelImageFiles.Count > 0)
                        {
                            folderFunction.SavePathFile(modelImageFiles, modelPath, modelImageFileNames);
                        }
                        if (modelFiles.Count > 0)
                        {
                            folderFunction.SavePathFile(modelFiles, modelPath, modelFileNames);
                        }
                    }
                    #endregion
                }
                #endregion

                #region 處理 TempModels (3D Model List 的模型庫) - 增量更新
                if (post.TempModels != null && post.TempModels.Count > 0)
                {
                    // 獲取現有的共用模型
                    var commonModelsWhere = $@"""Deleted"" = 0 AND ""IsCommon"" = 1 AND ""SOPId"" IN (SELECT ""SOP2Id"" FROM ""SOP2"" WHERE ""MachineAddId"" = @MachineAddId AND ""KnowledgeBaseId"" = @KnowledgeBaseId)";
                    var existingCommonModels = await _baseRepository.GetAllAsync<SOP2Model>("SOPModel", commonModelsWhere, new { MachineAddId = post.MachineAddId, KnowledgeBaseId = post.KnowledgeBaseId });

                    // 創建現有模型的字典，用於快速查找（基於檔案名稱）
                    var existingModelDict = existingCommonModels.ToDictionary(
                        m => $"{m.SOPModelImage}_{m.SOPModelFile}",
                        m => m
                    );

                    // 獲取已保存的 SOP ID（包括新創建的）
                    var savedSOPs = await _baseRepository.GetAllAsync<SOP2>("SOP2", "\"MachineAddId\" = @MachineAddId AND \"KnowledgeBaseId\" = @KnowledgeBaseId AND \"Deleted\" = 0", new { MachineAddId = post.MachineAddId, KnowledgeBaseId = post.KnowledgeBaseId });
                    var firstSOPId = savedSOPs.FirstOrDefault()?.SOP2Id ?? 0;

                    if (firstSOPId > 0)
                    {
                        var addTempModel_Dicts = new List<Dictionary<string, object>>();
                        var tempModelImageFiles = new List<IFormFile>();
                        var tempModelImageFileNames = new List<string>();
                        var tempModelFiles = new List<IFormFile>();
                        var tempModelFileNames = new List<string>();
                        var processedModels = new HashSet<string>(); // 追蹤已處理的模型

                        foreach (var tempModel in post.TempModels)
                        {
                            // 跳過空的 tempModel
                            if (tempModel.TempModelImageObj == null &&
                                string.IsNullOrEmpty(tempModel.TempModelImageUrl) &&
                                tempModel.TempModelFileObj == null &&
                                string.IsNullOrEmpty(tempModel.TempModelFileName))
                            {
                                continue;
                            }

                            // 處理圖片
                            string? modelImageName = null;
                            if (tempModel.TempModelImageObj != null)
                            {
                                modelImageName = folderFunction.FileProduceName(tempModel.TempModelImageObj);
                                tempModelImageFiles.Add(tempModel.TempModelImageObj);
                                tempModelImageFileNames.Add(modelImageName);
                            }
                            else if (!string.IsNullOrEmpty(tempModel.TempModelImageUrl))
                            {
                                if (tempModel.TempModelImageUrl.StartsWith("http"))
                                {
                                    var uri = new Uri(tempModel.TempModelImageUrl);
                                    modelImageName = Path.GetFileName(uri.LocalPath);
                                }
                                else
                                {
                                    modelImageName = Path.GetFileName(tempModel.TempModelImageUrl);
                                }
                            }

                            // 處理檔案
                            string? modelFileName = null;
                            if (tempModel.TempModelFileObj != null)
                            {
                                modelFileName = folderFunction.FileProduceName(tempModel.TempModelFileObj);
                                tempModelFiles.Add(tempModel.TempModelFileObj);
                                tempModelFileNames.Add(modelFileName);
                            }
                            else if (!string.IsNullOrEmpty(tempModel.TempModelFileName))
                            {
                                modelFileName = tempModel.TempModelFileName;
                            }

                            // 只有當有圖片和檔案時才處理
                            if (!string.IsNullOrEmpty(modelImageName) && !string.IsNullOrEmpty(modelFileName))
                            {
                                var modelKey = $"{modelImageName}_{modelFileName}";

                                // 檢查是否已經存在相同的模型
                                if (!existingModelDict.ContainsKey(modelKey) && !processedModels.Contains(modelKey))
                                {
                                    var addTempModel_Dict = new Dictionary<string, object>()
                                    {
                                        { "@SOPId", firstSOPId},
                                        { "@SOPModelImage", modelImageName},
                                        { "@SOPModelFile", modelFileName},
                                        { "@SOPModelPX", 0.0},
                                        { "@SOPModelPY", 0.0},
                                        { "@SOPModelPZ", 0.0},
                                        { "@SOPModelRX", 0.0},
                                        { "@SOPModelRY", 0.0},
                                        { "@SOPModelRZ", 0.0},
                                        { "@SOPModelSX", 1.0},
                                        { "@SOPModelSY", 1.0},
                                        { "@SOPModelSZ", 1.0},
                                        { "@IsCommon", 1}, // 標記為共用模型
                                        { "@Creator", myUser.UserId},
                                    };
                                    addTempModel_Dicts.Add(addTempModel_Dict);
                                    processedModels.Add(modelKey);
                                }
                            }
                        }

                        // 檢查需要刪除的模型（在資料庫中存在但在前端已被移除）
                        var currentModelKeys = new HashSet<string>();
                        foreach (var tempModel in post.TempModels)
                        {
                            if (tempModel.TempModelImageObj != null || !string.IsNullOrEmpty(tempModel.TempModelImageUrl))
                            {
                                string? imageName = null;
                                string? fileName = null;

                                if (tempModel.TempModelImageObj != null)
                                {
                                    imageName = folderFunction.FileProduceName(tempModel.TempModelImageObj);
                                }
                                else if (!string.IsNullOrEmpty(tempModel.TempModelImageUrl))
                                {
                                    imageName = tempModel.TempModelImageUrl.StartsWith("http")
                                        ? Path.GetFileName(new Uri(tempModel.TempModelImageUrl).LocalPath)
                                        : Path.GetFileName(tempModel.TempModelImageUrl);
                                }

                                if (tempModel.TempModelFileObj != null)
                                {
                                    fileName = folderFunction.FileProduceName(tempModel.TempModelFileObj);
                                }
                                else if (!string.IsNullOrEmpty(tempModel.TempModelFileName))
                                {
                                    fileName = tempModel.TempModelFileName;
                                }

                                if (!string.IsNullOrEmpty(imageName) && !string.IsNullOrEmpty(fileName))
                                {
                                    currentModelKeys.Add($"{imageName}_{fileName}");
                                }
                            }
                        }

                        // 標記刪除不再存在的模型
                        var modelsToDelete = existingCommonModels.Where(m =>
                            !currentModelKeys.Contains($"{m.SOPModelImage}_{m.SOPModelFile}")
                        ).ToList();

                        if (modelsToDelete.Count > 0)
                        {
                            var deleteModel_Dicts = modelsToDelete.Select(m => new Dictionary<string, object>()
                            {
                                { "SOPModelId", m.SOPModelId},
                                { "@Deleted", 1},
                                { "@Updater", myUser.UserId},
                                { "@UpdateTime", DateTime.Now}
                            }).ToList();

                            await _baseRepository.UpdateMutiByCustomTable(deleteModel_Dicts, "SOPModel", "\"SOPModelId\" = @SOPModelId");
                        }

                        // 執行資料庫操作 - 只插入新模型
                        if (addTempModel_Dicts.Count > 0)
                        {
                            await _baseRepository.AddMutiByCustomTable(addTempModel_Dicts, "SOPModel");
                        }

                        // 保存模型檔案 - 只保存新檔案
                        // TempModels（3D Model List）保存在知識庫專用路徑，不依賴特定 SOP ID
                        if (tempModelImageFiles.Count > 0 || tempModelFiles.Count > 0)
                        {
                            var knowledgeBasePath = Path.Combine(_savePath, "machineAdd", post.MachineAddId.ToString(), "knowledgeBase", post.KnowledgeBaseId.ToString(), "models");
                            folderFunction.CreateFolder(knowledgeBasePath, 0);

                            if (tempModelImageFiles.Count > 0)
                            {
                                folderFunction.SavePathFile(tempModelImageFiles, knowledgeBasePath, tempModelImageFileNames);
                            }
                            if (tempModelFiles.Count > 0)
                            {
                                folderFunction.SavePathFile(tempModelFiles, knowledgeBasePath, tempModelFileNames);
                            }
                        }
                    }
                }
                #endregion

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = $"錯誤更新 - {step}: {ex.Message}";
                exceptionMsg = ex.ToString();
                stackTrace = new StackTrace(ex);
            }

            return Ok(apiResult);
        }
    }
}

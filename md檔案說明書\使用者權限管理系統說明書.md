# 使用者權限管理系統說明書

## 📋 **目錄**
- [系統概述](#系統概述)
- [用戶等級定義](#用戶等級定義)
- [權限控制架構](#權限控制架構)
- [前端實現](#前端實現)
- [後端實現](#後端實現)
- [UI/UX 優化](#uiux-優化)
- [技術細節](#技術細節)
- [檔案修改詳細說明](#檔案修改詳細說明)
- [快速參考指南](#快速參考指南)
- [測試指南](#測試指南)

---

## 🎯 **系統概述**

本系統實現了基於用戶等級的權限管理機制，包含三個用戶等級：**最高管理者**、**專家**、**一般使用者**。系統通過前端UI禁用和後端API權限驗證雙重保護，確保不同等級用戶只能執行相應的操作。

### **核心特性**
- ✅ **三級權限管理**：最高管理者 > 專家 > 一般使用者
- ✅ **前端UI禁用**：一般使用者看到禁用按鈕和自定義提示
- ✅ **後端API保護**：所有敏感操作都有權限驗證
- ✅ **自定義提示系統**：hover 顯示權限說明
- ✅ **視覺反饋**：按鈕樣式變化和載入動畫

---

## 👥 **用戶等級定義**

### **數據庫層級定義**
```csharp
// Models/Userinfo.cs
public enum UserLevelEnum: byte
{
    Admin = 1 << 0,  // 1 - 最高管理員
    Expert = 1 << 1, // 2 - 專家  
    User = 1 << 2,   // 4 - 一般使用者
}

public byte UserLevel { get; set; } // 使用者層級

public string UserLevelText
{
    get
    {
        switch (UserLevel)
        {
            case 1: return "最高管理員";
            case 2: return "專家";
            case 4: return "一般使用者";
        }
        return text;
    }
}
```

### **權限對應表**

| 用戶等級 | UserLevel | 權限範圍 | 可執行操作 |
|---------|-----------|----------|------------|
| **最高管理者** | `1` | 全部權限 | 新增、編輯、刪除、修改密碼 |
| **專家** | `2` | 管理權限 | 新增、編輯、刪除、修改密碼 |
| **一般使用者** | `4` | 僅查看 | 僅能查看，所有操作被禁用 |

---

## 🏗️ **權限控制架構**

### **雙重保護機制**

#### **1. 前端UI層保護**
```javascript
// 權限檢查邏輯
const isGeneralUser = myUser && (myUser.UserLevel === 4 || myUser.userLevel === 4);

// 按鈕事件處理
const handleButtonClick = (originalHandler) => (e) => {
  if (isGeneralUser) {
    e.preventDefault();
    e.stopPropagation();
    if (e.target) e.target.blur();
    return;
  }
  originalHandler(e);
};

// 按鈕樣式控制
const getButtonClassName = (originalClass) => {
  return isGeneralUser ? `${originalClass} general-user-disabled` : originalClass;
};
```

#### **2. 後端API層保護**
```csharp
// 權限驗證邏輯
if ((myUser.UserLevel & ((byte)UserLevelEnum.Admin | (byte)UserLevelEnum.Expert)) == 0)
{
    apiResult.Code = "3002"; // 您不具有新增的權限
    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
    return Ok(apiResult);
}
```

---

## 💻 **前端實現**

### **核心檔案結構**
```
armanagementfront/src/
├── pages/
│   ├── UserManage.js          # 使用者管理主頁面 ⭐ 已修改
│   ├── MachineIOTList.js      # IOT管理頁面 ⭐ 已修改
│   ├── Machine.js             # 機台管理頁面 ⭐ 已修改
│   ├── Knowledge.js           # 知識庫管理頁面 ⭐ 已修改
│   ├── Database.js            # 資料庫管理頁面 ⭐ 已修改
│   ├── SOP.js                 # SOP管理頁面 ⭐ 已修改
│   ├── SOP2.js                # SOP編輯頁面 ⭐ 已修改
│   ├── MachineKnowledge.js    # 機台知識庫頁面 ⭐ 已修改
│   ├── PermissionTest.js      # 權限測試頁面 ⭐ 已修改
│   └── Header.js              # 頂部用戶信息顯示 ⭐ 已修改
├── styles/
│   └── UserPermissions.css    # 權限相關樣式 ⭐ 新增
└── contexts/
    └── MyUserContext.js       # 用戶上下文
```

### **按鈕權限控制實現**

#### **標準按鈕模式**
```javascript
<button
  type="button"
  className={getButtonClassName("btn btn-outline-primary")}
  onClick={handleButtonClick(() => handleOpenEditUserinfoModal(item.userId))}
  data-tooltip={isGeneralUser ? "一般使用者沒有編輯權限" : undefined}
>
  {t('userManage.btn.edit')}
</button>
```

#### **Modal 按鈕模式**
```javascript
<button
  type="button"
  className={getButtonClassName("btn btn-primary")}
  onClick={handleButtonClick(handleSaveAddUserinfo)}
  data-tooltip={isGeneralUser ? "一般使用者沒有新增權限" : undefined}
  disabled={saveAddUserinfoLoading}
>
  {saveAddUserinfoLoading ? <Spinner /> : t('btn.confirm')}
</button>
```

### **自定義提示系統**

#### **CSS 實現**
```css
/* 自定義提示樣式基礎設定 */
.general-user-disabled[data-tooltip] {
  position: relative;
}

/* 自定義提示顯示在按鈕左上方 */
.general-user-disabled:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: calc(100% + 8px);
  left: 20%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.95);
  color: #fff;
  padding: 10px 14px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 2147483647;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  pointer-events: none;
  animation: tooltipFadeInTop 0.2s ease-out;
  border: 1px solid rgba(255,255,255,0.1);
}

/* 提示箭頭向下指向按鈕 */
.general-user-disabled:hover::before {
  content: '';
  position: absolute;
  bottom: calc(100% + 2px);
  left: 20%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.95);
  z-index: 2147483646;
  pointer-events: none;
  animation: tooltipFadeInTop 0.2s ease-out;
}

/* 防止空值提示顯示 */
.general-user-disabled:not([data-tooltip])::after,
.general-user-disabled[data-tooltip=""]::after {
  display: none !important;
}

/* 提示動畫效果 */
@keyframes tooltipFadeInTop {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}
```

---

## 🔧 **後端實現**

### **核心檔案結構**
```
ARManagement/
├── Controllers/
│   ├── UserinfoController.cs     # 使用者管理API ⭐ 已修改
│   ├── MachineController.cs      # 機台管理API ⭐ 已修改
│   ├── SOPController.cs          # SOP管理API ⭐ 已修改
│   ├── MachineAlarmController.cs # 機台警報API ⭐ 已修改
│   ├── LoginController.cs        # 登入驗證API
│   └── MyBaseApiController.cs    # API基底控制器
├── Models/
│   └── Userinfo.cs              # 使用者模型定義 ⭐ 已修改
└── Middleware/
    └── JwtMiddleware.cs         # JWT權限中間件
```

### **API 權限驗證模式**

#### **新增操作權限檢查**
```csharp
[HttpPut]
public async Task<ActionResult<ApiResult<int>>> AddUserinfo(PostAddUserinfo post)
{
    // 判斷帳號是否為系統管理員
    if ((myUser.UserLevel & ((byte)UserLevelEnum.Admin | (byte)UserLevelEnum.Expert)) == 0)
    {
        apiResult.Code = "3002"; // 您不具有新增的權限
        apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
        return Ok(apiResult);
    }
    
    // 執行新增邏輯...
}
```

#### **編輯操作權限檢查**
```csharp
[HttpPost]
public async Task<ActionResult<ApiResult<object>>> EditUserinfo(PostEditUserinfo post)
{
    // 判斷帳號是否為系統管理員
    if ((myUser.UserLevel & ((byte)UserLevelEnum.Admin | (byte)UserLevelEnum.Expert)) == 0)
    {
        apiResult.Code = "3003"; // 您不具有修改的權限
        apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
        return Ok(apiResult);
    }
    
    // 執行編輯邏輯...
}
```

#### **刪除操作權限檢查**
```csharp
[HttpDelete]
public async Task<ActionResult<ApiResult<object>>> DeleteUserinfo(PostDeleteUserinfo post)
{
    // 判斷帳號是否為系統管理員
    if ((myUser.UserLevel & ((byte)UserLevelEnum.Admin | (byte)UserLevelEnum.Expert)) == 0)
    {
        apiResult.Code = "3004"; // 您不具有刪除的權限
        apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
        return Ok(apiResult);
    }
    
    // 執行刪除邏輯...
}
```

---

## 🎨 **UI/UX 優化**

### **視覺反饋系統**

#### **按鈕狀態樣式**
```css
/* 一般使用者禁用按鈕樣式 */
.general-user-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
  transition: all 0.2s ease-in-out;
}

.general-user-disabled:hover {
  opacity: 0.8;
  transform: none;
  box-shadow: none;
}
```

#### **載入動畫**
```javascript
// 載入狀態顯示
{saveAddUserinfoLoading ? (
  <>
    <Spinner as="span" animation="border" size="sm" role="status" />
    <span className="sr-only">Loading...</span>
  </>
) : (
  <span>{t('btn.confirm')}</span>
)}
```

### **用戶信息顯示**
```javascript
// Header.js - 顯示用戶層級而非用戶名
{t('header.user', { e: myUser ? myUser.userLevelText : '' })}
// 顯示結果：您好，一般使用者 / 您好，最高管理者 / 您好，專家
```

---

## 🔍 **技術細節**

### **關鍵技術決策**

#### **1. 條件屬性設置**
```javascript
// ✅ 正確的方式
data-tooltip={isGeneralUser ? "一般使用者沒有編輯權限" : undefined}

// ❌ 有問題的方式（已修復）
{...(isGeneralUser && { "data-tooltip": "一般使用者沒有編輯權限" })}
```

#### **2. CSS 選擇器簡化**
```css
/* ✅ 當前使用的簡化選擇器 */
.general-user-disabled:hover::after

/* ✅ 配合條件顯示控制 */
.general-user-disabled:not([data-tooltip])::after,
.general-user-disabled[data-tooltip=""]::after {
  display: none !important;
}

/* ❌ 之前複雜的選擇器（已廢棄） */
.general-user-disabled[data-tooltip]:not([data-tooltip=""]):hover::after
```

#### **3. 權限檢查邏輯**
```javascript
// 支援大小寫不同的屬性名
const isGeneralUser = myUser && (myUser.UserLevel === 4 || myUser.userLevel === 4);
```

### **安全性考量**

1. **雙重驗證**：前端UI禁用 + 後端API權限檢查
2. **JWT Token 驗證**：所有API請求都需要有效Token
3. **用戶狀態檢查**：檢查帳號是否被刪除或失效
4. **操作日誌**：記錄所有敏感操作（可擴展）

---

## ⚡ **快速參考指南**

### **🔍 如何為新頁面添加權限控制**

#### **步驟1: 添加權限檢查邏輯**
在頁面組件中添加以下代碼：
```javascript
import { MyUserContext } from '../contexts/MyUserContext';
import '../styles/UserPermissions.css';

function YourPage() {
  const { myUser } = useContext(MyUserContext);

  // 檢查是否為一般用戶
  const isGeneralUser = myUser && (myUser.UserLevel === 4 || myUser.userLevel === 4);

  // 按鈕事件處理
  const handleButtonClick = (originalHandler) => (e) => {
    if (isGeneralUser) {
      e.preventDefault();
      e.stopPropagation();
      if (e.target) e.target.blur();
      return;
    }
    originalHandler(e);
  };

  // 按鈕樣式類名
  const getButtonClassName = (originalClass) => {
    return isGeneralUser ? `${originalClass} general-user-disabled` : originalClass;
  };
}
```

#### **步驟2: 修改按鈕**
將現有按鈕按照以下模式修改：
```javascript
// 原始按鈕
<button onClick={() => handleAction()}>操作</button>

// 修改後的按鈕
<button
  className={getButtonClassName("btn btn-primary")}
  onClick={handleButtonClick(() => handleAction())}
  data-tooltip={isGeneralUser ? "一般使用者沒有操作權限" : undefined}
>
  操作
</button>
```

#### **步驟3: 後端API權限檢查**
在對應的Controller中添加權限檢查：
```csharp
if ((myUser.UserLevel & ((byte)UserLevelEnum.Admin | (byte)UserLevelEnum.Expert)) == 0)
{
    apiResult.Code = "3002"; // 權限錯誤代碼
    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
    return Ok(apiResult);
}
```

### **📁 檔案位置快速查找**

#### **前端檔案**
```
armanagementfront/src/pages/
├── UserManage.js          ← 使用者管理
├── MachineIOTList.js      ← IOT管理
├── Machine.js             ← 機台管理
├── Knowledge.js           ← 知識庫管理
├── Database.js            ← 資料庫管理
├── SOP.js                 ← SOP管理
├── SOP2.js                ← SOP編輯
├── MachineKnowledge.js    ← 機台知識庫
└── Header.js              ← 頂部用戶信息

armanagementfront/src/styles/
└── UserPermissions.css    ← 權限樣式 [新增]
```

#### **後端檔案**
```
ARManagement/Controllers/
├── UserinfoController.cs     ← 使用者API
├── MachineController.cs      ← 機台API
├── SOPController.cs          ← SOP API
└── MachineAlarmController.cs ← 警報API

Models/
└── Userinfo.cs              ← 使用者模型
```

### **🎯 常見問題解決**

#### **Q: 按鈕沒有顯示提示？**
A: 檢查以下項目：
1. 是否引入了 `UserPermissions.css`
2. 是否正確設置了 `data-tooltip` 屬性
3. 確認用戶的 `UserLevel` 是否為 4

#### **Q: 按鈕樣式沒有變化？**
A: 檢查以下項目：
1. 確認 `getButtonClassName` 函數是否正確調用
2. 檢查 CSS 檔案是否正確載入
3. 確認 `isGeneralUser` 邏輯是否正確

#### **Q: 後端API沒有權限檢查？**
A: 檢查以下項目：
1. 確認Controller中是否添加了權限檢查代碼
2. 檢查 `myUser.UserLevel` 是否正確獲取
3. 確認位運算邏輯是否正確

---

## 🧪 **測試指南**

### **測試場景**

#### **一般使用者 (UserLevel = 4)**
- [ ] 登入後右上角顯示"您好，一般使用者"
- [ ] 所有操作按鈕顯示為禁用狀態
- [ ] hover 按鈕時顯示權限提示
- [ ] 點擊按鈕無任何反應
- [ ] 直接API調用返回權限錯誤

#### **專家 (UserLevel = 2)**
- [ ] 登入後右上角顯示"您好，專家"
- [ ] 所有操作按鈕正常顯示
- [ ] hover 按鈕時不顯示提示
- [ ] 點擊按鈕正常執行功能
- [ ] API調用正常執行

#### **最高管理者 (UserLevel = 1)**
- [ ] 登入後右上角顯示"您好，最高管理者"
- [ ] 所有操作按鈕正常顯示
- [ ] hover 按鈕時不顯示提示
- [ ] 點擊按鈕正常執行功能
- [ ] API調用正常執行

### **測試頁面**
1. **使用者管理頁面** (`/userManage`)
2. **IOT管理頁面** (`/machine/{id}/machineIOTList`)
3. **機台管理頁面** (`/machine`)

---

## 📝 **修改歷史**

### **2025-01-28 權限管理系統實現與優化**
- ✅ 實現三級用戶權限系統
- ✅ 添加前端UI禁用機制
- ✅ 實現自定義提示系統
- ✅ 優化按鈕樣式和載入動畫
- ✅ 修復提示顯示問題
- ✅ 完善後端API權限驗證
- ✅ **提示位置優化**：調整提示從按鈕下方改為左上方顯示
- ✅ **CSS 簡化**：簡化選擇器邏輯，提高穩定性
- ✅ **動畫改善**：新增 tooltipFadeInTop 動畫效果

### **詳細修改檔案清單**

#### **🎨 前端檔案 (armanagementfront/src/)**

##### **頁面檔案 (pages/)**
- ✅ `UserManage.js` - 使用者管理主頁面
  - 新增權限檢查邏輯 `isGeneralUser`
  - 實現按鈕事件處理 `handleButtonClick`
  - 添加按鈕樣式控制 `getButtonClassName`
  - 所有CRUD按鈕添加自定義提示

- ✅ `MachineIOTList.js` - IOT設備管理頁面
  - 新增權限檢查邏輯
  - 禁用新增/編輯/刪除按鈕
  - 添加自定義提示功能

- ✅ `Machine.js` - 機台管理主頁面
  - 新增權限檢查邏輯
  - 禁用機台相關操作按鈕

- ✅ `Knowledge.js` - 知識庫管理頁面
  - 新增權限檢查邏輯
  - 禁用知識庫編輯功能

- ✅ `Database.js` - 資料庫管理頁面
  - 新增權限檢查邏輯
  - 禁用資料庫操作功能

- ✅ `SOP.js` - SOP管理頁面
  - 新增權限檢查邏輯
  - 禁用SOP編輯功能

- ✅ `SOP2.js` - SOP詳細編輯頁面
  - 新增權限檢查邏輯
  - 禁用SOP內容編輯

- ✅ `MachineKnowledge.js` - 機台知識庫頁面
  - 新增權限檢查邏輯
  - 禁用知識庫編輯功能

- ✅ `PermissionTest.js` - 權限測試頁面
  - 用於測試權限功能的示例頁面

- ✅ `Header.js` - 頁面頂部組件
  - 修改用戶顯示邏輯，顯示用戶層級而非用戶名

##### **樣式檔案 (styles/)**
- ✅ `UserPermissions.css` - 權限相關樣式 **[新增檔案]**
  - 一般用戶按鈕禁用樣式
  - 自定義提示樣式和動畫
  - hover 效果和視覺反饋

#### **🔧 後端檔案 (ARManagement/)**

##### **控制器檔案 (Controllers/)**
- ✅ `UserinfoController.cs` - 使用者管理API
  - 新增/編輯/刪除用戶權限檢查
  - 修改密碼權限檢查

- ✅ `MachineController.cs` - 機台管理API
  - 機台新增/編輯/刪除權限檢查

- ✅ `SOPController.cs` - SOP管理API
  - SOP操作權限檢查

- ✅ `MachineAlarmController.cs` - 機台警報API
  - 警報管理權限檢查

##### **模型檔案 (Models/)**
- ✅ `Userinfo.cs` - 使用者資料模型
  - 定義 `UserLevelEnum` 枚舉
  - 實現 `UserLevelText` 屬性

---

## 📝 **檔案修改詳細說明**

### **🎨 前端檔案修改內容**

#### **1. 權限檢查邏輯 (所有頁面共通)**
每個需要權限控制的頁面都添加了以下標準代碼：

```javascript
// 檢查是否為一般用戶
const isGeneralUser = myUser && (myUser.UserLevel === 4 || myUser.userLevel === 4);

// 按鈕事件處理 - 一般用戶禁用操作
const handleButtonClick = (originalHandler) => (e) => {
  if (isGeneralUser) {
    e.preventDefault();
    e.stopPropagation();
    if (e.target) e.target.blur();
    return;
  }
  originalHandler(e);
};

// 按鈕樣式類名
const getButtonClassName = (originalClass) => {
  return isGeneralUser ? `${originalClass} general-user-disabled` : originalClass;
};
```

#### **2. 按鈕修改模式 (所有頁面共通)**
所有操作按鈕都按照以下模式修改：

```javascript
// 修改前
<button
  type="button"
  className="btn btn-outline-primary"
  onClick={() => handleEdit(item.id)}
>
  編輯
</button>

// 修改後
<button
  type="button"
  className={getButtonClassName("btn btn-outline-primary")}
  onClick={handleButtonClick(() => handleEdit(item.id))}
  data-tooltip={isGeneralUser ? "一般使用者沒有編輯權限" : undefined}
>
  編輯
</button>
```

#### **3. UserPermissions.css 新增內容**
```css
/* 一般用戶按鈕禁用樣式基礎設定 */
.general-user-disabled {
  position: relative;
  transition: all 0.2s ease-in-out !important;
}

.general-user-disabled:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  cursor: not-allowed !important;
  color: #fff !important;
  opacity: 0.8 !important;
}

/* 自定義提示樣式 - 左上方顯示 */
.general-user-disabled:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: calc(100% + 8px);
  left: 20%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.95);
  color: #fff;
  padding: 10px 14px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 2147483647;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  pointer-events: none;
  animation: tooltipFadeInTop 0.2s ease-out;
  border: 1px solid rgba(255,255,255,0.1);
}

/* 提示箭頭向下指向按鈕 */
.general-user-disabled:hover::before {
  content: '';
  position: absolute;
  bottom: calc(100% + 2px);
  left: 20%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.95);
  z-index: 2147483646;
  pointer-events: none;
  animation: tooltipFadeInTop 0.2s ease-out;
}

/* 防止空值提示顯示 */
.general-user-disabled:not([data-tooltip])::after,
.general-user-disabled[data-tooltip=""]::after {
  display: none !important;
}

/* 動畫效果 */
@keyframes tooltipFadeInTop {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}
```

### **🔧 後端檔案修改內容**

#### **1. 權限檢查模式 (所有Controller共通)**
所有需要權限控制的API都添加了以下檢查：

```csharp
// 判斷帳號是否為系統管理員
if ((myUser.UserLevel & ((byte)UserLevelEnum.Admin | (byte)UserLevelEnum.Expert)) == 0)
{
    apiResult.Code = "3002"; // 您不具有新增的權限
    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
    return Ok(apiResult);
}
```

#### **2. Userinfo.cs 新增內容**
```csharp
public enum UserLevelEnum: byte
{
    Admin = 1 << 0,  // 1 - 最高管理員
    Expert = 1 << 1, // 2 - 專家
    User = 1 << 2,   // 4 - 一般使用者
}

public string UserLevelText
{
    get
    {
        switch (UserLevel)
        {
            case 1: return "最高管理員";
            case 2: return "專家";
            case 4: return "一般使用者";
        }
        return text;
    }
}
```

### **📋 修改的具體按鈕清單**

#### **UserManage.js (使用者管理)**
- ✅ 新增使用者按鈕
- ✅ 編輯按鈕 (表格中每一行)
- ✅ 修改密碼按鈕 (表格中每一行)
- ✅ 刪除按鈕 (表格中每一行)
- ✅ Modal中的儲存按鈕 (新增/編輯/刪除/修改密碼)

#### **MachineIOTList.js (IOT管理)**
- ✅ 新增IOT按鈕
- ✅ 編輯按鈕 (表格中每一行)
- ✅ 刪除按鈕 (表格中每一行)
- ✅ Modal中的確定按鈕

#### **其他頁面的按鈕**
- ✅ **Machine.js**: 機台新增/編輯/刪除按鈕
- ✅ **Knowledge.js**: 知識庫新增/編輯/刪除按鈕
- ✅ **Database.js**: 資料庫操作按鈕
- ✅ **SOP.js**: SOP編輯按鈕
- ✅ **SOP2.js**: SOP內容編輯按鈕
- ✅ **MachineKnowledge.js**: 機台知識庫編輯按鈕

---

## 🚀 **未來擴展**

### **可能的改進方向**
1. **細粒度權限**：針對不同功能模組設置獨立權限
2. **角色管理**：支援自定義角色和權限組合
3. **操作日誌**：記錄所有用戶操作歷史
4. **權限繼承**：支援部門或群組權限繼承
5. **動態權限**：支援運行時權限調整

---

**📧 如有問題，請聯繫開發團隊**

{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  //"Kestrel": {
  //  "EndPoints": {
  //    "Http": {
  //      "Url": "http://localhost:8098"
  //    }
  //  }
  //},
  "DBConfig": {
    "ConnectionString": "Server=127.0.0.1;Port=5432;User Id=postgres;Password=*********;Database=AREditor;"
  },
  "FileStorageSettings": {
    "UploadsFolder": "./uploads"
  },
  "JwtSettings": {
    "Issuer": "UNTaiwanensis",
    "SignKey": "U@N#Taiwanensis$@&^",
    "LifeHour": 8760
  },
  "SMTP": {
    "Server": "smtp.gmail.com",
    "Port": 587,
    "Username": "<EMAIL>",
    "Password": "{SMTP_PASSWORD}",
    "EnableSSL": true,
    "SenderEmail": "<EMAIL>"
  }
}

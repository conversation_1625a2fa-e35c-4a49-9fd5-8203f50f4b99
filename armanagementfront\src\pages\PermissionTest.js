import React, { useContext } from 'react';
import { MyUserContext } from '../contexts/MyUserContext';

function PermissionTest() {
  const { myUser } = useContext(MyUserContext);

  // 檢查是否為一般用戶 - 更健壯的檢查
  const isGeneralUser = myUser && (myUser.UserLevel === 4 || myUser.userLevel === 4);

  // 按鈕事件處理 - 一般用戶禁用操作
  const handleButtonClick = (originalHandler) => (e) => {
    console.log('PermissionTest Button clicked - isGeneralUser:', isGeneralUser);
    if (isGeneralUser) {
      e.preventDefault();
      e.stopPropagation();
      // 立即移除焦點，防止樣式殘留
      if (e.target) {
        e.target.blur();
      }
      console.log('PermissionTest Button click prevented for general user');
      return;
    }
    originalHandler(e);
  };

  // 按鈕樣式類名
  const getButtonClassName = (originalClass) => {
    return isGeneralUser ? `${originalClass} general-user-disabled` : originalClass;
  };

  const handleTestClick = () => {
    alert('按鈕點擊成功！');
  };

  return (
    <div className="container mt-4">
      <h2>權限測試頁面</h2>
      
      <div className="card">
        <div className="card-body">
          <h5>用戶信息</h5>
          <p><strong>myUser:</strong> {JSON.stringify(myUser, null, 2)}</p>
          <p><strong>UserLevel:</strong> {myUser?.UserLevel}</p>
          <p><strong>userLevel:</strong> {myUser?.userLevel}</p>
          <p><strong>isGeneralUser:</strong> {isGeneralUser ? 'true' : 'false'}</p>
        </div>
      </div>

      <div className="card mt-3">
        <div className="card-body">
          <h5>按鈕測試</h5>
          <p>如果您是一般用戶（UserLevel = 4），以下按鈕應該：</p>
          <ul>
            <li>✅ 保持原本顏色（藍色、紅色等）</li>
            <li>✅ hover時變灰色</li>
            <li>✅ 顯示禁止游標</li>
            <li>✅ 點擊無效果</li>
            <li>✅ 鼠標移開時立即恢復原本顏色</li>
            <li>✅ hover時在按鈕下方偏左顯示權限提示</li>
            <li>✅ 表格中的按鈕提示不會被覆蓋</li>
            <li>✅ 提示位置往左偏移顯示</li>
            <li>✅ 點擊後立即失去焦點，不會殘留樣式</li>
          </ul>
          
          <button
            type="button"
            className={getButtonClassName("btn btn-primary me-2")}
            onClick={handleButtonClick(handleTestClick)}
            data-tooltip={isGeneralUser ? "一般使用者沒有此功能權限" : ""}
          >
            測試按鈕 1
          </button>

          <button
            type="button"
            className={getButtonClassName("btn btn-outline-primary me-2")}
            onClick={handleButtonClick(handleTestClick)}
            data-tooltip={isGeneralUser ? "一般使用者沒有編輯權限" : ""}
          >
            測試按鈕 2
          </button>

          <button
            type="button"
            className={getButtonClassName("btn btn-outline-danger")}
            onClick={handleButtonClick(handleTestClick)}
            data-tooltip={isGeneralUser ? "一般使用者沒有刪除權限" : ""}
          >
            測試按鈕 3
          </button>
        </div>
      </div>
    </div>
  );
}

export default PermissionTest;

import React, { useState, useEffect, useRef, useContext } from 'react';
import { useTranslation } from 'react-i18next'; //語系
import { useParams, useNavigate } from 'react-router-dom';
import { setWindowClass, removeWindowClass } from '../utils/helpers';
import { ToastContainer, toast } from 'react-toastify';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import Modal from 'react-bootstrap/Modal';
import Spinner from 'react-bootstrap/Spinner';
import { SOPName } from '../components/SOPName';
import styles from '../scss/global.module.scss';
import classNames from 'classnames';
import { MyUserContext } from '../contexts/MyUserContext';
import '../styles/UserPermissions.css';

// import styles from "../scss/AlarmDescription.module.scss";
import { Link } from 'react-router-dom';

import {
  apiGetAllSOPByMachineAddId,
  apiSaveSOP,
  apiGetAllKnowledgeBaseByFilter,
  apiGetCommonModels,
  apiCheckModelUsage,
  apiCascadeDeleteModel,
} from '../utils/Api';

import { Space, ColorPicker, theme } from 'antd';
import { generate, red, green, blue } from '@ant-design/colors';
import { useStore } from '../zustand/store';
import { useLocation } from 'react-router-dom';

// 字數限制常數 - 測試600字步驟說明的極限方案
const CHARACTER_LIMITS = {
  soP2Message: 600,    // 步驟說明限制600字（極限測試方案），對應修改maxLength="1200"的值
  soP2Remark: 100      // 備註說明限制100字
};

// 智能字數計算函數 - 考慮中英文混合的空間佔用
const calculateEffectiveLength = (text) => {
  if (!text) return 0;
  
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
  const numbers = (text.match(/[0-9]/g) || []).length;
  const punctuation = (text.match(/[.,;:!?()[\]{}"'-]/g) || []).length;
  const spaces = (text.match(/\s/g) || []).length;
  const others = text.length - chineseChars - englishChars - numbers - punctuation - spaces;
  
  // 根據不同字符類型計算有效長度
  return Math.ceil(
    chineseChars * 1.0 +        // 中文字符佔用100%空間
    englishChars * 0.6 +        // 英文字母佔用60%空間
    numbers * 0.5 +             // 數字佔用50%空間
    punctuation * 0.4 +         // 標點符號佔用40%空間
    spaces * 0.3 +              // 空格佔用30%空間
    others * 0.7                // 其他字符佔用70%空間
  );
};

// 計算空間佔用百分比
const calculateSpaceUsage = (text, limit) => {
  const effectiveLength = calculateEffectiveLength(text);
  return Math.min(Math.round((effectiveLength / limit) * 100), 100);
};

function SOP2() {
  const { t } = useTranslation();
  const { myUser } = useContext(MyUserContext);
  const navigate = useNavigate();
  const location = useLocation();
  const { knowledgeInfo, SOPData } = location.state || {};

  // 檢查是否為一般用戶 - 更健壯的檢查
  const isGeneralUser = myUser && (myUser.UserLevel === 4 || myUser.userLevel === 4);

  // 按鈕事件處理 - 一般用戶禁用操作
  const handleButtonClick = (originalHandler) => (e) => {
    if (isGeneralUser) {
      e.preventDefault();
      return;
    }
    originalHandler(e);
  };

  // 按鈕樣式類名
  const getButtonClassName = (originalClass) => {
    return isGeneralUser ? `${originalClass} general-user-disabled` : originalClass;
  };

  // 先調用 useStore 獲取 SOPInfo
  const { SOPInfo, setSOPInfo } = useStore();

  // 然後獲取 KnowledgeBaseId
  const knowledgeBaseId = knowledgeInfo?.knowledgeBaseId || SOPInfo?.knowledgeInfo?.knowledgeBaseId;

  useEffect(() => {
    console.log('SOP2 initialization - location.state:', location.state);
    console.log('Current knowledgeBaseId:', knowledgeBaseId);

    // 如果是從預覽返回，優先恢復預覽前的狀態
    if (location.state?.isReturningFromPreview) {
      console.log('SOP2 - Returning from preview, restoring state');
      if (location.state?.SOPData) {
        console.log('Restoring SOPData from preview:', location.state.SOPData);
        setSOPs(location.state.SOPData);
        // 如果有SOP數據，選擇第一個作為當前選中的SOP
        if (location.state.SOPData.length > 0) {
          setSelectSOP(location.state.SOPData[0]);
        }
      }
      return; // 從預覽返回時，不執行其他初始化邏輯
    }

    // 如果是新的知識記錄，清理 SOP 相關狀態但保持 3D Model List 的 UI 結構
    if (location.state?.isNewKnowledge) {
      console.log('Detected new knowledge record, clearing SOP state but maintaining 3D Model List UI');
      setSOPs([]);
      setSelectSOP(null);
      // 重置 tempModels 為初始狀態，但保持 12 欄位結構
      setTempModels(Array.from({ length: 12 }, () => ({
        tempModelImageName: '',
        tempModelImageObj: null,
        tempModelImageUrl: '',
        tempModelFileName: '',
        tempModelFileObj: null,
        tempModelFileUrl: '',
        tempModelPX: 0.0,
        tempModelPY: 0.0,
        tempModelPZ: 0.0,
        tempModelRX: 0.0,
        tempModelRY: 0.0,
        tempModelRZ: 0.0,
        tempModelSX: 1.0,
        tempModelSY: 1.0,
        tempModelSZ: 1.0,
      })));
      // tempModelNumbers 保持不變，維持 12 欄位的 UI
    }

    if (location.state?.SOPData) {
      console.log('Received SOP Data from location.state:', location.state.SOPData);

      // 轉換資料格式，確保 sopModels 結構正確
      const convertedSOPs = location.state.SOPData.map(sop => ({
        ...sop,
        // 確保每個SOP都有soP2Id字段（如果沒有則使用sopId）
        soP2Id: sop.soP2Id || sop.sopId,
        sopModels: (sop.sopModels || []).map(model => {
          // 為從 location.state 載入的模型添加回退信息
          let originalImageUrl = null;
          let tempModelImageUrl = null;

          // 如果是 SOP 路徑的圖片，構建對應的知識庫路徑作為回退
          if (model.sopModelImage && model.sopModelImage.includes('/sop2/') && knowledgeBaseId && machineAddId) {
            const fileName = model.sopModelImage.split('/').pop();
            originalImageUrl = `${window.apiUrl}/upload/machineAdd/${machineAddId}/knowledgeBase/${knowledgeBaseId}/models/${fileName}`;
            tempModelImageUrl = originalImageUrl; // 也設置為 tempModelImageUrl 以便回退邏輯使用
          }

          return {
            sopModelId: model.sopModelId,
            deleted: 0,
            sopModelImage: model.sopModelImage,
            sopModelImageObj: null,
            sopModelFile: model.sopModelFile,
            sopModelFileObj: null,
            sopModelPX: model.sopModelPX || 0.0,
            sopModelPY: model.sopModelPY || 0.0,
            sopModelPZ: model.sopModelPZ || 0.0,
            sopModelRX: model.sopModelRX || 0.0,
            sopModelRY: model.sopModelRY || 0.0,
            sopModelRZ: model.sopModelRZ || 0.0,
            sopModelSX: model.sopModelSX || 1.0,
            sopModelSY: model.sopModelSY || 1.0,
            sopModelSZ: model.sopModelSZ || 1.0,
            isDeletedSOPModelImage: false,
            isDeletedSOPModelFile: false,
            // 添加回退 URL
            originalImageUrl: originalImageUrl,
            tempModelImageUrl: tempModelImageUrl,
          };
        })
      }));

      console.log('Setting SOPs from location.state:', convertedSOPs);
      setSOPs(convertedSOPs);
      if (convertedSOPs.length > 0) {
        setSelectSOP(convertedSOPs[0]);
        // 對於新知識記錄，不要同步 sopModels 到 tempModels，保持 3D Model List 為空
        if (!location.state?.isNewKnowledge) {
          syncSOPModelsToTempModels(convertedSOPs[0]);
        }
      }
    } else if (knowledgeBaseId && !location.state?.isNewKnowledge) {
      console.log('No SOP Data in location.state, calling refreshSOP with knowledgeBaseId:', knowledgeBaseId);
      refreshSOP(); // 只有在不是新知識記錄時才調用 refreshSOP
    } else {
      console.log('No SOPData and no knowledgeBaseId (or is new knowledge), initializing empty state');
    }
  }, [location.state?.SOPData, location.state?.isNewKnowledge]); // 只依賴 SOPData 和 isNewKnowledge，避免 knowledgeBaseId 變化觸發重新載入

  console.log(SOPInfo);

  const { machineId, machineAddId: urlMachineAddId } = useParams();

  // 優先使用 SOPInfo 中的 machineAddId，如果沒有則使用 URL 參數
  const machineAddId = SOPInfo?.machineAddId || urlMachineAddId;

  const [tempModelNumbers, setTempModelNumbers] = useState(
    Array.from({ length: 12 }, (_, index) => index + 1)
  ); //建立12格3D Model List
  const [tempModels, setTempModels] = useState(() =>
    Array.from({ length: 12 }, () => ({
      tempModelImageName: '',
      tempModelImageObj: null,
      tempModelImageUrl: '',
      tempModelFileName: '',
      tempModelFileObj: null,
      tempModelFileUrl: '',
    }))
  ); //暫存 3D Model List的資料
  const [selectTempModelIndex, setSelectTempModelIndex] = useState(-1); //選擇暫存 3D Model的index
  const [selectTempModel, setSelectTempModel] = useState(null); //選擇暫存 3D Model的物件
  const [selectTempModelErrors, setSelectTempModelErrors] = useState({
    //選擇暫存 3D Model的錯誤訊息
    tempModelImage: '',
    tempModelFile: '',
  });
  const [showSaveTempModelModal, setShowSaveTempModelModal] = useState(false);
  const inputTempModelImageRef = useRef(null); //input File類型的 3D Model圖片

  const [sops, setSOPs] = useState([]); //SOP列表
  const [selectSOP, setSelectSOP] = useState(null); //選擇的SOP

  const inputImageRef = useRef(null); //input File類型的圖片
  const [imageError, setImageError] = useState(''); //圖片格式錯誤
  const inputVideoRef = useRef(null); //input File類型的影片
  const videoSrcRef = useRef(null); //input File類型的影片
  const [videoError, setVideoError] = useState(''); //影片格式錯誤

  const inputRemarksImageRef = useRef(null); // input File類型的備註圖片
  const [remarksImageError, setRemarksImageError] = useState(''); // 備註圖片格式錯誤

  const [saveSOPLoading, setSaveSOPLoading] = useState(false); //SOP儲存的轉圈圈

  const [selectDeleteSOPIndex, setSelectDeleteSOPIndex] = useState(-1); //要刪除的topic的index
  const [showDeleteSOPModal, setShowDeleteSOPModal] = useState(false); //顯示"刪除SOP modal"

  // 級聯刪除相關狀態
  const [showCascadeDeleteModal, setShowCascadeDeleteModal] = useState(false);
  const [cascadeDeleteModelInfo, setCascadeDeleteModelInfo] = useState(null);
  const [cascadeDeleteLoading, setCascadeDeleteLoading] = useState(false);

  const [textColor, setTextColor] = useState('#000000'); // 初始文字顏色設為黑色
  const [isSOPName, setIsSOPName] = useState(false);

  const { token } = theme.useToken();

  //#region 同步 SOPModels 到 TempModels (已禁用)
  const syncSOPModelsToTempModels = (sop) => {
    console.log('syncSOPModelsToTempModels called with sop:', sop);

    // 根據新需求，3D Model List 應該是固定的模型庫，不應該被 SOP 的模型影響
    // 因此禁用此函數，3D Model List 只通過 loadCommonModelsToLibrary 載入
    console.log('syncSOPModelsToTempModels is disabled - 3D Model List should remain as fixed model library');
    return;
  };
  //#endregion

  //#region 載入共用模型到模型庫
  const loadCommonModelsToLibrary = async () => {
    try {
      // 檢查必要的參數是否存在
      if (!machineAddId || !knowledgeBaseId) {
        console.log('Missing required parameters for loadCommonModelsToLibrary:', {
          machineAddId,
          knowledgeBaseId
        });
        return;
      }

      console.log('Calling apiGetCommonModels with parameters:', {
        id: machineAddId,
        machineAddId: machineAddId,
        knowledgeBaseId: knowledgeBaseId,
        isCommon: 0
      });

      const response = await apiGetCommonModels({
        id: machineAddId, // PostId 的 Id 屬性
        machineAddId: machineAddId,
        knowledgeBaseId: knowledgeBaseId,
        isCommon: 1 // 只載入共用模型（3D Model List 模型庫），不包含 SOP 中的模型
      });

      console.log('apiGetCommonModels response:', response);

      if (response.code === '0000' && response.result) {
        // 不重置整個 tempModels，而是智能更新
        let newTempModels = [...tempModels];

        console.log('Current tempModels before update:', newTempModels);
        console.log('Common models from API:', response.result);

        // 首先，標記所有當前的共用模型（從後端載入的）
        const currentCommonModelUrls = new Set();
        response.result.forEach((commonModel) => {
          if (commonModel.deleted === 0 && commonModel.sopModelImage) {
            currentCommonModelUrls.add(commonModel.sopModelImage);
          }
        });

        // 移除已經不存在於後端的共用模型（但保留用戶新上傳的模型）
        newTempModels.forEach((tempModel, index) => {
          if (tempModel.tempModelImageUrl &&
              !tempModel.tempModelImageObj && // 不是新上傳的
              !currentCommonModelUrls.has(tempModel.tempModelImageUrl)) {
            // 這個模型在後端已經被刪除，清空這個位置
            console.log('Removing deleted model from position:', index, tempModel);
            newTempModels[index] = {
              tempModelImageName: '',
              tempModelImageObj: null,
              tempModelImageUrl: '',
              tempModelFileName: '',
              tempModelFileObj: null,
              tempModelFileUrl: '',
            };
          }
        });

        // 添加新的共用模型到空位置
        response.result.forEach((commonModel) => {
          if (commonModel.deleted === 0 && commonModel.sopModelImage) {
            // 檢查是否已經存在於 tempModels 中
            const existingIndex = newTempModels.findIndex(temp =>
              temp.tempModelImageUrl === commonModel.sopModelImage
            );

            if (existingIndex === -1) {
              // 找到第一個空位置
              const emptyIndex = newTempModels.findIndex(temp =>
                !temp.tempModelImageObj && !temp.tempModelImageUrl
              );

              if (emptyIndex !== -1) {
                console.log('Adding new common model to position:', emptyIndex, commonModel);
                newTempModels[emptyIndex] = {
                  tempModelImageName: commonModel.sopModelImage ? commonModel.sopModelImage.split('/').pop() : '',
                  tempModelImageObj: null,
                  tempModelImageUrl: commonModel.sopModelImage || '',
                  tempModelFileName: commonModel.sopModelFile ? commonModel.sopModelFile.split('/').pop() : '',
                  tempModelFileObj: null,
                  tempModelFileUrl: commonModel.sopModelFile || '', // 添加檔案 URL
                };
              }
            }
          }
        });

        console.log('Setting tempModels with updated common models:', newTempModels);
        setTempModels(newTempModels);
      } else {
        console.log('No common models found or API call failed');
      }
    } catch (error) {
      console.error('Error loading common models:', error);
    }
  };
  //#endregion

  // 生成顏色組合
  const presets = Object.entries({
    primary: generate('#0052cc'), // 這裡使用一個假設的主要顏色
    red: red,
    green: green,
    blue: blue,
  }).map(([label, colors]) => ({ label, colors }));

  //#region 初始載入
  useEffect(() => {
    removeWindowClass('login-page');

    // 只有在不是新知識記錄且沒有從 location.state 載入數據時才調用 refreshSOP
    if (!location.state?.isNewKnowledge && !location.state?.SOPData) {
      console.log('Initial load: calling refreshSOP for existing knowledge');
      const fetchData = async () => {
        await refreshSOP();
      };
      fetchData();
    } else {
      console.log('Initial load: skipping refreshSOP for new knowledge or location.state data');
    }
  }, []);
  //#endregion

  //#region 刷新SOP
  const refreshSOP = async () => {
    // 如果是新知識記錄，完全不執行 refreshSOP
    if (location.state?.isNewKnowledge) {
      console.log('Skipping refreshSOP for new knowledge record');
      return;
    }

    // 確保有有效的 machineAddId
    const currentMachineAddId = SOPInfo?.machineAddId || urlMachineAddId;

    if (!currentMachineAddId) {
      console.log('No machineAddId available for refreshSOP');
      return;
    }

    var sendData = {
      id: currentMachineAddId,
      knowledgeBaseId: knowledgeBaseId, // 添加 KnowledgeBaseId 過濾
      isCommon: 0,
    };

    console.log('refreshSOP sendData:', sendData);
    console.log('Current knowledgeBaseId for filtering:', knowledgeBaseId);

    let getAllSOPResponse = await apiGetAllSOPByMachineAddId(sendData);
    if (getAllSOPResponse) {
      if (getAllSOPResponse.code == '0000') {
        // 轉換後端資料格式，確保 sopModels 結構正確
        const convertedSOPs = getAllSOPResponse.result.map(sop => ({
          ...sop,
          sopModels: (sop.sopModels || []).map(model => {
            // 為從後端載入的模型添加回退信息
            let originalImageUrl = null;
            let tempModelImageUrl = null;

            // 如果是 SOP 路徑的圖片，構建對應的知識庫路徑作為回退
            if (model.sopModelImage && model.sopModelImage.includes('/sop2/') && knowledgeBaseId && machineAddId) {
              const fileName = model.sopModelImage.split('/').pop();
              originalImageUrl = `${window.apiUrl}/upload/machineAdd/${machineAddId}/knowledgeBase/${knowledgeBaseId}/models/${fileName}`;
              tempModelImageUrl = originalImageUrl; // 也設置為 tempModelImageUrl 以便回退邏輯使用
            }

            return {
              sopModelId: model.sopModelId,
              deleted: 0,
              sopModelImage: model.sopModelImage,
              sopModelImageObj: null, // 從後端載入時沒有檔案物件
              sopModelFile: model.sopModelFile,
              sopModelFileObj: null, // 從後端載入時沒有檔案物件
              sopModelPX: model.sopModelPX || 0.0,
              sopModelPY: model.sopModelPY || 0.0,
              sopModelPZ: model.sopModelPZ || 0.0,
              sopModelRX: model.sopModelRX || 0.0,
              sopModelRY: model.sopModelRY || 0.0,
              sopModelRZ: model.sopModelRZ || 0.0,
              sopModelSX: model.sopModelSX || 1.0,
              sopModelSY: model.sopModelSY || 1.0,
              sopModelSZ: model.sopModelSZ || 1.0,
              isDeletedSOPModelImage: false,
              isDeletedSOPModelFile: false,
              // 添加回退 URL
              originalImageUrl: originalImageUrl,
              tempModelImageUrl: tempModelImageUrl,
            };
          })
        }));

        console.log('Converted SOPs from refreshSOP:', convertedSOPs);
        setSOPs(convertedSOPs);
        if (convertedSOPs.length > 0) {
          console.log('Setting selectSOP to first SOP:', convertedSOPs[0]);
          console.log('First SOP sopModels:', convertedSOPs[0].sopModels);
          setSelectSOP(convertedSOPs[0]);

          // 只有在不是新知識記錄時才載入共用模型到模型庫
          if (!location.state?.isNewKnowledge) {
            loadCommonModelsToLibrary();
          }
        }
      }
    }
  };
  //#endregion



  // useEffect(() => {
  //   if (SOPInfo.sops) {
  //     const convertedSOPData = SOPInfo.sops?.map(item => ({
  //       ...item, sopModels: []
  //     }))
  //     setSOPs(convertedSOPData);
  //     setSelectSOP(convertedSOPData[0]);
  //   }
  // }, [SOPInfo])

  // useEffect(() => {
  //   if (SOPInfo && SOPInfo.sops) {
  //     const convertedSOPData = SOPInfo.sops.map(item => ({
  //       ...item, sopModels: []
  //     }));
  //     setSOPs(convertedSOPData);
  //     setSelectSOP(convertedSOPData[0]);
  //   }
  // }, [SOPInfo]);

  // useEffect(() => {
  //   if (SOPInfo.sops) {
  //     const convertedSOPData = SOPInfo.sops?.map(item => ({
  //       ...item, sopModels: []
  //     }))
  //     setSOPs(convertedSOPData);
  //     setSelectSOP(convertedSOPData[0]);
  //   }
  // }, [SOPInfo])

  useEffect(() => {
    console.log('SOPInfo useEffect triggered:', { SOPInfo, sopsLength: sops.length, hasLocationState: !!location.state?.SOPData });

    // 如果已經從 location.state 載入了資料，不要被 SOPInfo 覆蓋
    if (location.state?.SOPData) {
      console.log('Skipping SOPInfo processing because location.state.SOPData exists');
      return;
    }

    // 確保 SOPInfo 不是 null 且 SOPInfo.sops 存在，並且當前沒有 SOP 數據
    if (SOPInfo && SOPInfo.sops && sops.length === 0) {
      console.log('Processing SOPInfo.sops:', SOPInfo.sops);

      // 只處理屬於當前 knowledgeBaseId 的 SOP
      const filteredSOPs = knowledgeBaseId
        ? SOPInfo.sops.filter(sop => sop.knowledgeBaseId === knowledgeBaseId)
        : SOPInfo.sops;

      console.log('Filtered SOPs by knowledgeBaseId:', filteredSOPs);

      const convertedSOPData = filteredSOPs.map((item) => ({
        ...item,
        sopModels: item.sopModels || [], // 保留現有的 sopModels 或初始化為空陣列
      }));

      console.log('Setting SOPs from SOPInfo:', convertedSOPData);
      setSOPs(convertedSOPData);
      if (convertedSOPData.length > 0) {
        setSelectSOP(convertedSOPData[0]); // 選擇第一個 SOP
        syncSOPModelsToTempModels(convertedSOPData[0]);

        // 只有在不是新知識記錄時才載入共用模型到模型庫
        if (!location.state?.isNewKnowledge) {
          loadCommonModelsToLibrary();
        }
      } else {
        setSelectSOP(null); // 沒有 SOPs 時設置為 null
      }
    } else if (!SOPInfo || !SOPInfo.sops) {
      console.log('SOPInfo is null or has no sops, ensuring clean state');
      // 只有在沒有從 location.state 載入數據時才清空
      if (!location.state?.SOPData && sops.length > 0) {
        setSOPs([]); // 清空 SOPs
        setSelectSOP(null); // 沒有選中的 SOP
      }
    }
  }, [SOPInfo, location.state?.SOPData, knowledgeBaseId, sops.length]);

  // 專門用於載入共用模型的 useEffect
  useEffect(() => {
    console.log('loadCommonModelsToLibrary useEffect triggered:', {
      machineAddId,
      knowledgeBaseId: SOPInfo?.knowledgeInfo?.knowledgeBaseId,
      SOPInfo,
      isNewKnowledge: location.state?.isNewKnowledge,
      hasKnowledgeBaseId: !!knowledgeBaseId
    });

    // 如果是新知識記錄（沒有 knowledgeBaseId），跳過載入
    if (location.state?.isNewKnowledge) {
      console.log('Skipping loadCommonModelsToLibrary for new knowledge record');
      return;
    }

    // 如果有 knowledgeBaseId 和 machineAddId，載入該知識記錄的模型庫
    if (machineAddId && knowledgeBaseId) {
      console.log('Calling loadCommonModelsToLibrary for existing knowledge record');
      loadCommonModelsToLibrary();
    } else {
      console.log('Missing parameters for loadCommonModelsToLibrary:', {
        machineAddId,
        knowledgeBaseId
      });
    }
  }, [machineAddId, knowledgeBaseId, location.state?.isNewKnowledge]);

  //#region 新增SOP
  const handleAddSOP = (e) => {
    let lastSOP = sops[sops.length - 1];
    let newSOPs = [...sops];

    // 為新SOP生成臨時唯一ID（使用負數避免與真實ID衝突）
    const tempId = -(Date.now() + Math.random());

    let sop = {
      sopId: 0,
      soP2Id: tempId, // 添加臨時唯一ID用於前端識別
      deleted: 0,
      machineAddId: machineAddId,
      knowledgeBaseId: knowledgeBaseId, // 添加 KnowledgeBaseId
      soP2Step: lastSOP != null ? lastSOP.soP2Step + 1 : 1,
      soP2Message: '',
      soP2Name: '', //test
      soP2Image: '',
      soP2ImageObj: null,
      isDeletedSOP2Image: false,
      /* 新增：sopRemarksMessage、sopRemarksImage、sopRemarksImageObj 三元素 */
      soP2Remark: '',
      soP2RemarkImage: '',
      soP2RemarkImageObj: null,
      isDeletedSOP2RemarkImage: false,
      sopVideo: '',
      sopVideoObj: null,
      isDeletedSOPVideo: false,
      plC1: '', // 更新字段名稱
      plC2: '', // 更新字段名稱
      plC3: '', // 更新字段名稱
      plC4: '', // 更新字段名稱
      sopModels: [],
    };

    newSOPs.push(sop);

    setSOPs(newSOPs);
    if (newSOPs.length == 1) {
      setSelectSOP(sop);
    }

    console.log('Added new SOP with tempId:', tempId, sop);
  };
  //#endregion

  //#region 選擇SOP / 開啟刪除SOP modal
  const handleSelectSOP = (event, index) => {
    const target = event.target;
    if (target.classList.contains('fa-trash')) {
      const activeSops = sops.filter((sop) => sop.deleted !== 1);
      const actualSop = activeSops[index];
      const actualIndex = sops.findIndex(
        (sop) => sop.soP2Step === actualSop.soP2Step
      );
      setSelectDeleteSOPIndex(actualIndex);
      setShowDeleteSOPModal(true);
    } else {
      const activeSops = sops.filter((sop) => sop.deleted !== 1);
      const selectedSOP = activeSops[index];

      // 防止重複選擇相同的SOP（使用更可靠的比較邏輯）
      const isSameSOP = selectedSOP && selectSOP && (
        (selectedSOP.soP2Id && selectSOP.soP2Id && selectedSOP.soP2Id === selectSOP.soP2Id) ||
        (selectedSOP.sopId && selectSOP.sopId && selectedSOP.sopId === selectSOP.sopId) ||
        (selectedSOP.soP2Step === selectSOP.soP2Step)
      );

      if (selectedSOP && !isSameSOP) {
        console.log('=== SOP切換 ===');
        console.log('從SOP:', selectSOP);
        console.log('切換到SOP:', selectedSOP);
        console.log('Selected SOP sopModels:', selectedSOP.sopModels);
        console.log('當前sops數組狀態:', sops);

        // 檢查選中的SOP是否有完整的數據
        console.log('選中SOP的詳細信息:', {
          soP2Step: selectedSOP.soP2Step,
          soP2Message: selectedSOP.soP2Message,
          soP2Image: selectedSOP.soP2Image,
          soP2Remark: selectedSOP.soP2Remark,
          sopVideo: selectedSOP.sopVideo,
          plC1: selectedSOP.plC1,
          plC2: selectedSOP.plC2,
          plC3: selectedSOP.plC3,
          plC4: selectedSOP.plC4
        });

        setSelectSOP(selectedSOP);

        // 不需要同步 sopModels 到 tempModels，因為 tempModels 是模型庫
        // 模型庫應該保持所有可用的模型

        // 重置輸入欄位
        if (inputImageRef.current) inputImageRef.current.value = '';
        if (inputVideoRef.current) inputVideoRef.current.value = '';
      } else if (isSameSOP) {
        console.log('相同的SOP，跳過切換');
      }
    }
  };
  //#endregion

  // //#region 拖曳選單步驟
  // const onDragEnd = (event) => {
  //   const { source, destination } = event;

  //   if (!destination) {
  //     return;
  //   }

  //   let newSOPs = [...sops];
  //   const activeSops = newSOPs.filter((sop) => sop.deleted !== 1);
  //   const [removed] = activeSops.splice(source.index, 1);
  //   activeSops.splice(destination.index, 0, removed);

  //   // 重新排序步驟號
  //   activeSops.forEach((sop, index) => {
  //     sop.soP2Step = index + 1;
  //   });

  //   // 更新原始數組
  //   newSOPs = newSOPs.map((sop) => {
  //     if (sop.deleted === 1) return sop;
  //     const updatedSop = activeSops.find(
  //       (activeSop) => activeSop.sopId === sop.sopId
  //     );
  //     return updatedSop || sop;
  //   });

  //   setSOPs(newSOPs);
  // };
  // //#endregion

  //#region 拖曳選單步驟 (加入重新排序 step 的邏輯)
  const onDragEnd = (event) => {
    const { source, destination } = event;

    if (!destination) {
      return;
    }

    let newSOPs = [...sops];
    const [remove] = newSOPs.splice(source.index, 1);
    newSOPs.splice(destination.index, 0, remove);

    // 重新排序未刪除的步驟
    let stepCount = 1;
    newSOPs = newSOPs.map((sop) => {
      if (sop.deleted !== 1) {
        sop.soP2Step = stepCount++;
        return sop;
      }
      return sop;
    });

    setSOPs(newSOPs);
  };
  //#endregion

  //#region 輔助函數：查找SOP索引
  const findSOPIndex = (targetSOP) => {
    if (!targetSOP) {
      console.warn('findSOPIndex: targetSOP is null or undefined');
      return -1;
    }

    console.log('findSOPIndex: 查找SOP:', targetSOP);
    console.log('findSOPIndex: 當前sops數組:', sops);

    // 優先使用soP2Id（更可靠的唯一標識符）
    if (targetSOP.soP2Id) {
      const index = sops.findIndex(sop => sop.soP2Id === targetSOP.soP2Id);
      if (index !== -1) {
        console.log(`findSOPIndex: 通過soP2Id找到索引 ${index}`);
        return index;
      }
    }

    // 回退到使用sopId（對於已保存的SOP）
    if (targetSOP.sopId && targetSOP.sopId > 0) {
      const index = sops.findIndex(sop => sop.sopId === targetSOP.sopId);
      if (index !== -1) {
        console.log(`findSOPIndex: 通過sopId找到索引 ${index}`);
        return index;
      }
    }

    // 最後回退到使用soP2Step（最不可靠，但作為最後手段）
    const index = sops.findIndex(sop => sop.soP2Step === targetSOP.soP2Step);
    console.log(`findSOPIndex: 通過soP2Step找到索引 ${index}`);
    return index;
  };
  //#endregion

  //#region 修改SOP 改變Input的欄位
  const handleSelectSOPChange = (e) => {
    const { name, value } = e.target;

    // 檢查字數限制（針對文字輸入欄位）
    if (CHARACTER_LIMITS[name]) {
      const newEffectiveLength = calculateEffectiveLength(value);
      const currentEffectiveLength = calculateEffectiveLength(selectSOP[name] || '');
      const limit = CHARACTER_LIMITS[name];
      
      // 如果新輸入超出限制，檢查是否為減少字數的操作
      if (newEffectiveLength > limit) {
        // 如果新字數比當前字數更多，則不允許（不能增加）
        if (newEffectiveLength > currentEffectiveLength) {
          const fieldName = name === 'soP2Message' ? '步驟說明' : '備註說明';
          toast.warn(`${fieldName}已達字數限制！請先刪減部分內容。(目前: ${newEffectiveLength}/${limit})`);
          return; // 阻止增加字數
        }
        // 如果新字數比當前字數少或相等，允許操作（可以刪減或保持）
      }
      
      // 接近限制時的提醒（85%以上）
      const usage = calculateSpaceUsage(value, limit);
      if (usage >= 85 && usage < 100) {
        const fieldName = name === 'soP2Message' ? '步驟說明' : '備註說明';
        console.log(`⚠️ ${fieldName}接近字數限制: ${usage}%`);
      }
    }

    // 更新當前選中的SOP
    const updatedSelectSOP = { ...selectSOP, [name]: value };
    setSelectSOP(updatedSelectSOP);

    // 同時更新sops數組中對應的SOP，確保數據持久化
    const newSOPs = [...sops];
    const sopIndex = findSOPIndex(selectSOP);
    if (sopIndex !== -1) {
      newSOPs[sopIndex] = { ...newSOPs[sopIndex], [name]: value };
      setSOPs(newSOPs);
      console.log(`✅ Updated SOP ${selectSOP.soP2Step} (index: ${sopIndex}) field ${name} to:`, value);
      console.log('Updated SOP object:', newSOPs[sopIndex]);
    } else {
      console.warn('❌ Could not find SOP to update:', selectSOP);
      console.warn('Available SOPs:', sops);
    }
  };
  //#endregion

  //#region 上傳步驟片按鈕
  const handleUploadImageBtn = (e) => {
    e.preventDefault();
    inputImageRef.current.click();
  };
  //#endregion

  //#region 上傳圖片Change事件
  const onImageChange = (e) => {
    var file, img;
    file = e.target.files[0];
    let newSelectSOP = { ...selectSOP };
    if (file != null) {
      var fileExtension = file.name
        .substr(file.name.lastIndexOf('.') + 1 - file.name.length)
        .toLowerCase();
      if (
        !(
          fileExtension == 'png' ||
          fileExtension == 'jpg' ||
          fileExtension == 'jpeg'
        )
      ) {
        setImageError('format');
      } else {
        var img = new Image();
        var objectUrl = URL.createObjectURL(file);
        img.onload = function () {
          newSelectSOP.soP2ImageObj = file;
          if (newSelectSOP.soP2Image != '') {
            newSelectSOP.isDeletedSOP2Image = true;
          }
          setSelectSOP(newSelectSOP);

          // 同步更新到sops數組
          const newSOPs = [...sops];
          const sopIndex = findSOPIndex(selectSOP);
          if (sopIndex !== -1) {
            newSOPs[sopIndex] = { ...newSOPs[sopIndex], soP2ImageObj: file };
            if (newSOPs[sopIndex].soP2Image !== '') {
              newSOPs[sopIndex].isDeletedSOP2Image = true;
            }
            setSOPs(newSOPs);
          }
        };
        img.src = objectUrl;
      }
    }
  };
  //#endregion

  //#region 移除圖片按鈕
  const handleRemoveImageBtn = (e) => {
    e.preventDefault();
    let newSelectSOP = { ...selectSOP };

    newSelectSOP.soP2Image = ''; // 保持與後端一致的大小寫
    newSelectSOP.soP2ImageObj = null;
    newSelectSOP.isDeletedSOP2Image = true; // 保持與後端一致的大小寫

    setSelectSOP(newSelectSOP);

    // 同步更新到sops數組
    const newSOPs = [...sops];
    const sopIndex = findSOPIndex(selectSOP);
    if (sopIndex !== -1) {
      newSOPs[sopIndex] = {
        ...newSOPs[sopIndex],
        soP2Image: '',
        soP2ImageObj: null,
        isDeletedSOP2Image: true
      };
      setSOPs(newSOPs);
    }
  };
  //#endregion

  //#region 上傳影片按鈕
  const handleUploadVideoBtn = (e) => {
    e.preventDefault();
    inputVideoRef.current.click();
  };
  //#endregion

  //#region 上傳影片Change事件
  const onVideoChange = (e) => {
    var file;
    file = e.target.files[0];
    let newSelectSOP = { ...selectSOP };
    if (file != null) {
      var fileExtension = file.name
        .substr(file.name.lastIndexOf('.') + 1 - file.name.length)
        .toLowerCase();
      if (!fileExtension == 'mp4') {
        setVideoError('format');
      } else {
        newSelectSOP.sopVideoObj = file;
        if (newSelectSOP.sopVideo != '') {
          newSelectSOP.isDeletedSOPVideo = true;
        }
        setSelectSOP(newSelectSOP);

        // 同步更新到sops數組
        const newSOPs = [...sops];
        const sopIndex = findSOPIndex(selectSOP);
        if (sopIndex !== -1) {
          newSOPs[sopIndex] = { ...newSOPs[sopIndex], sopVideoObj: file };
          if (newSOPs[sopIndex].sopVideo !== '') {
            newSOPs[sopIndex].isDeletedSOPVideo = true;
          }
          setSOPs(newSOPs);
        }
      }
    }
  };
  //#endregion

  //#region 移除影片按鈕
  const handleRemoveVideoBtn = (e) => {
    e.preventDefault();
    let newSelectSOP = { ...selectSOP };

    newSelectSOP.sopVideo = '';
    newSelectSOP.sopVideoObj = null;
    newSelectSOP.isDeletedSOPVideo = true;

    setSelectSOP(newSelectSOP);

    // 同步更新到sops數組
    const newSOPs = [...sops];
    const sopIndex = findSOPIndex(selectSOP);
    if (sopIndex !== -1) {
      newSOPs[sopIndex] = {
        ...newSOPs[sopIndex],
        sopVideo: '',
        sopVideoObj: null,
        isDeletedSOPVideo: true
      };
      setSOPs(newSOPs);
    }
  };
  //#endregion

  //#region 上傳備註圖片按鈕
  const handleUploadRemarksImageBtn = (e) => {
    e.preventDefault();
    inputRemarksImageRef.current.click();
  };
  //#endregion

  //#region 上傳備註圖片Change事件
  const onRemarksImageChange = (e) => {
    var file;
    file = e.target.files[0];
    let newSelectSOP = { ...selectSOP };
    if (file != null) {
      var fileExtension = file.name
        .substr(file.name.lastIndexOf('.') + 1 - file.name.length)
        .toLowerCase();
      if (
        !(
          fileExtension == 'png' ||
          fileExtension == 'jpg' ||
          fileExtension == 'jpeg'
        )
      ) {
        setRemarksImageError('format');
      } else {
        var img = new Image();
        var objectUrl = URL.createObjectURL(file);
        img.onload = function () {
          newSelectSOP.soP2RemarkImageObj = file;
          if (newSelectSOP.soP2RemarkImage != '') {
            newSelectSOP.isDeletedSOP2RemarksImage = true;
          }
          setSelectSOP(newSelectSOP);

          // 同步更新到sops數組
          const newSOPs = [...sops];
          const sopIndex = findSOPIndex(selectSOP);
          if (sopIndex !== -1) {
            newSOPs[sopIndex] = { ...newSOPs[sopIndex], soP2RemarkImageObj: file };
            if (newSOPs[sopIndex].soP2RemarkImage !== '') {
              newSOPs[sopIndex].isDeletedSOP2RemarkImage = true;
            }
            setSOPs(newSOPs);
          }
        };
        img.src = objectUrl;
      }
    }
  };
  //#endregion

  //#region 移除備註圖片按鈕
  const handleRemoveRemarksImageBtn = (e) => {
    e.preventDefault();
    let newSelectSOP = { ...selectSOP };

    newSelectSOP.soP2RemarkImage = ''; // 保持與後端一致的大小寫
    newSelectSOP.soP2RemarkImageObj = null;
    newSelectSOP.isDeletedSOP2RemarkImage = true; // 保持與後端一致的大小寫

    setSelectSOP(newSelectSOP);

    // 同步更新到sops數組
    const newSOPs = [...sops];
    const sopIndex = findSOPIndex(selectSOP);
    if (sopIndex !== -1) {
      newSOPs[sopIndex] = {
        ...newSOPs[sopIndex],
        soP2RemarkImage: '',
        soP2RemarkImageObj: null,
        isDeletedSOP2RemarkImage: true
      };
      setSOPs(newSOPs);
    }
  };
  //#endregion

  //#region 更新SOPs
  useEffect(() => {
    if (selectSOP != null) {
      let newSOPs = [...sops];
      const index = newSOPs.findIndex(
        (sop) => sop.deleted !== 1 && sop.soP2Step === selectSOP.soP2Step
      );

      if (index !== -1) {
        // 確保不會重複添加相同的SOP
        newSOPs = newSOPs.map((sop) => {
          if (sop.deleted !== 1 && sop.soP2Step === selectSOP.soP2Step) {
            return selectSOP;
          }
          return sop;
        });
        setSOPs(newSOPs);
      }
    }
  }, [selectSOP]);
  //#endregion

  // useEffect(() => {
  //   console.log(sops);
  // }, [sops]);

  //#region 關閉刪除SOP Modal
  const handleCloseDeleteSOPModal = () => {
    setShowDeleteSOPModal(false);
    setSelectDeleteSOPIndex(-1);
  };
  //#endregion

  //#region 刪除SOP
  const handleSaveDeleteSOP = async () => {
    if (selectDeleteSOPIndex !== -1) {
      let newSOPs = [...sops];
      const deletedSOP = newSOPs[selectDeleteSOPIndex];

      if (deletedSOP.sopId > 0) {
        deletedSOP.deleted = 1;
        newSOPs[selectDeleteSOPIndex] = deletedSOP;
      } else {
        newSOPs.splice(selectDeleteSOPIndex, 1);
      }

      // 重新排序未刪除的步驟
      const activeSOPs = newSOPs.filter((sop) => sop.deleted !== 1);
      activeSOPs.forEach((sop, index) => {
        sop.soP2Step = index + 1;
      });

      // 更新原始數組中未刪除的SOP的步驟號
      newSOPs = newSOPs.map((sop) => {
        if (sop.deleted === 1) return sop;
        const updatedSop = activeSOPs.find(
          (activeSop) => activeSop.sopId === sop.sopId
        );
        return updatedSop || sop;
      });

      setSOPs(newSOPs);

      // 更新選中的SOP
      if (selectSOP && selectSOP.soP2Step === deletedSOP.soP2Step) {
        const previousSOP = activeSOPs.find(
          (sop) => sop.soP2Step === deletedSOP.soP2Step - 1
        );
        setSelectSOP(previousSOP || activeSOPs[0] || null);
      }

      setShowDeleteSOPModal(false);
      setSelectDeleteSOPIndex(-1);

      toast.success('刪除成功!', {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 2000,
        hideProgressBar: true,
        closeOnClick: false,
        pauseOnHover: true,
      });
    }
  };
  //#endregion

  //#region 儲存SOP
  const handleSaveSOP = async () => {
    // e.preventDefault();

    setSaveSOPLoading(true);
    console.log('sops', sops);
    setSOPInfo((prev) => ({ ...prev, sops: sops }));
    setIsSOPName((prev) => !prev);
  };
  //#endregion

  useEffect(() => {
    console.log('SOPs updated:', sops);
  }, [sops]);

  const handlePreview = () => {
    // 傳遞完整的編輯數據到Preview，而不依賴全局store
    navigate('/preview', {
      state: {
        step: 'sop2',
        knowledgeInfo: knowledgeInfo,
        SOPData: sops,
        // 保留原始編輯狀態用於返回時恢復
        originalEditingState: {
          knowledgeInfo: knowledgeInfo,
          SOPData: sops
        }
      }
    });
  };

  //#region 開啟上傳暫存3DModel Modal / 清空暫存3DModel
  const handleOpenSaveTempModelBtn = (event, index) => {
    const target = event.target;

    if (target.classList.contains('fa-times')) {
      // 智能刪除檢測：區分已保存 vs 未保存的模型
      const deletedTempModel = tempModels[index];

      console.log('=== 模型刪除觸發 ===');
      console.log('Delete button clicked for model at index:', index);
      console.log('Model data:', deletedTempModel);
      console.log('Has tempModelImageName:', !!deletedTempModel.tempModelImageName);
      console.log('Has tempModelFileName:', !!deletedTempModel.tempModelFileName);
      console.log('Has tempModelImageUrl:', !!deletedTempModel.tempModelImageUrl);
      console.log('Has tempModelImageObj:', !!deletedTempModel.tempModelImageObj);
      console.log('Current machineAddId:', machineAddId);
      console.log('Current knowledgeBaseId:', knowledgeBaseId);

      // 改進判斷邏輯：只要有模型名稱就進行級聯刪除檢查
      // 這樣可以同時處理已保存和未保存的模型
      if (deletedTempModel.tempModelImageName || deletedTempModel.tempModelFileName) {
        console.log('Triggering cascade delete check for model (improved logic)');
        handleCascadeDeleteCheck(deletedTempModel, index);
      } else {
        // 如果沒有檔案名稱，使用直接刪除
        console.log('Using direct delete for empty model');
        handleDirectDelete(deletedTempModel, index);
      }
    } else {
      setSelectTempModelIndex(index);
      setSelectTempModel(tempModels[index]);
      setSelectTempModelErrors({
        tempModelImage: '',
        tempModelFile: '',
      });
      setShowSaveTempModelModal(true);
    }
  };
  //#endregion

  //#region 級聯刪除檢查
  const handleCascadeDeleteCheck = async (deletedTempModel, index) => {
    try {
      console.log('Starting cascade delete check...');
      console.log('Request parameters:', {
        machineAddId: machineAddId,
        knowledgeBaseId: knowledgeBaseId,
        modelImageName: deletedTempModel.tempModelImageName,
        modelFileName: deletedTempModel.tempModelFileName
      });

      // 1. 檢查前端狀態中的使用情況（未保存的模型）
      const frontendUsage = checkFrontendModelUsage(deletedTempModel);
      console.log('Frontend usage check result:', frontendUsage);

      // 2. 檢查後端數據庫中的使用情況（已保存的模型）
      const response = await apiCheckModelUsage({
        machineAddId: machineAddId,
        knowledgeBaseId: knowledgeBaseId,
        modelImageName: deletedTempModel.tempModelImageName,
        modelFileName: deletedTempModel.tempModelFileName
      });

      console.log('CheckModelUsage API response:', response);

      if (response && response.code === '0000') {
        const backendUsage = response.result;
        console.log('Backend usage info:', backendUsage);

        // 3. 合併前端和後端的使用情況，並去除重複
        const allUsedSteps = [...frontendUsage.usedInSteps, ...backendUsage.usedInSteps];

        // 去除重複的步驟（基於soP2Id去重）
        const uniqueUsedSteps = allUsedSteps.filter((step, index, self) =>
          index === self.findIndex(s => s.soP2Id === step.soP2Id)
        );

        const totalUsage = {
          modelImageName: deletedTempModel.tempModelImageName,
          modelFileName: deletedTempModel.tempModelFileName,
          isUsed: frontendUsage.isUsed || backendUsage.isUsed,
          usageCount: uniqueUsedSteps.length, // 使用去重後的數量
          usedInSteps: uniqueUsedSteps,
          frontendUsage: frontendUsage,
          backendUsage: backendUsage,
          tempModelIndex: index,
          deletedTempModel: deletedTempModel
        };

        console.log('Total usage (frontend + backend):', totalUsage);

        // 設置級聯刪除信息並顯示確認對話框
        setCascadeDeleteModelInfo(totalUsage);
        setShowCascadeDeleteModal(true);
        console.log('Showing cascade delete modal');
      } else {
        console.error('檢查模型使用情況失敗:', response);
        // 如果檢查失敗，回退到直接刪除
        handleDirectDelete(deletedTempModel, index);
      }
    } catch (error) {
      console.error('檢查模型使用情況時發生錯誤:', error);
      // 如果檢查失敗，回退到直接刪除
      handleDirectDelete(deletedTempModel, index);
    }
  };
  //#endregion

  //#region 檢查前端狀態中的模型使用情況
  const checkFrontendModelUsage = (deletedTempModel) => {
    const usedInSteps = [];
    let usageCount = 0;

    console.log('=== 開始檢查前端模型使用情況 ===');
    console.log('要刪除的模型:', deletedTempModel);
    console.log('當前所有SOP:', sops);

    // 檢查所有SOP步驟中是否有使用該模型（包括已保存和未保存的）
    sops.forEach(sop => {
      console.log(`\n檢查SOP ${sop.soP2Step} (ID: ${sop.soP2Id}):`, sop);

      if (sop.sopModels && sop.sopModels.length > 0) {
        console.log(`SOP ${sop.soP2Step} 的模型數量: ${sop.sopModels.length}`);
        console.log(`SOP ${sop.soP2Step} 的模型:`, sop.sopModels);

        sop.sopModels.forEach((model, modelIndex) => {
          console.log(`\n--- 檢查模型 ${modelIndex} ---`);
          console.log('SOP模型完整信息:', model);

          // 檢查是否為相同的模型（通過檔案名稱匹配）
          const modelImageName = model.sopModelImage ? model.sopModelImage.split('/').pop() : '';
          const modelFileName = model.sopModelFile ? model.sopModelFile.split('/').pop() : '';

          console.log('檔案名稱比較:', {
            sopModelImageName: modelImageName,
            sopModelFileName: modelFileName,
            targetImageName: deletedTempModel.tempModelImageName,
            targetFileName: deletedTempModel.tempModelFileName,
            sopModelImageUrl: model.sopModelImage,
            sopModelFileUrl: model.sopModelFile,
            targetImageUrl: deletedTempModel.tempModelImageUrl,
            targetFileUrl: deletedTempModel.tempModelFileUrl,
            deleted: model.deleted
          });

          // 更寬鬆的匹配邏輯：只要有一個檔案名稱匹配就算使用
          const imageNameMatch = modelImageName && deletedTempModel.tempModelImageName &&
                                modelImageName === deletedTempModel.tempModelImageName;
          const fileNameMatch = modelFileName && deletedTempModel.tempModelFileName &&
                               modelFileName === deletedTempModel.tempModelFileName;

          // 也檢查完整路徑匹配
          const imageUrlMatch = model.sopModelImage && deletedTempModel.tempModelImageUrl &&
                               model.sopModelImage === deletedTempModel.tempModelImageUrl;
          const fileUrlMatch = model.sopModelFile && deletedTempModel.tempModelFileUrl &&
                              model.sopModelFile === deletedTempModel.tempModelFileUrl;

          // 新增：檢查URL中是否包含檔案名稱
          const imageUrlContainsName = model.sopModelImage && deletedTempModel.tempModelImageName &&
                                      model.sopModelImage.includes(deletedTempModel.tempModelImageName);
          const fileUrlContainsName = model.sopModelFile && deletedTempModel.tempModelFileName &&
                                     model.sopModelFile.includes(deletedTempModel.tempModelFileName);

          const isMatch = (imageNameMatch || fileNameMatch || imageUrlMatch || fileUrlMatch ||
                          imageUrlContainsName || fileUrlContainsName) &&
                         model.deleted !== 1;

          console.log('匹配結果詳細:', {
            imageNameMatch,
            fileNameMatch,
            imageUrlMatch,
            fileUrlMatch,
            imageUrlContainsName,
            fileUrlContainsName,
            isMatch,
            modelDeleted: model.deleted
          });

          if (isMatch) {
            console.log(`✅ 找到使用該模型的SOP: ${sop.soP2Step}`);

            // 檢查是否已經添加過這個SOP步驟（避免重複計算）
            const alreadyAdded = usedInSteps.some(step => step.soP2Id === sop.soP2Id);
            if (!alreadyAdded) {
              usedInSteps.push({
                soP2Id: sop.soP2Id,
                soP2Step: sop.soP2Step,
                soP2Name: sop.soP2Message || '未命名步驟',
                sopModelId: model.sopModelId || 0,
                isFrontendOnly: model.sopModelId === 0 // 標記為前端未保存的模型
              });
              usageCount++;
            } else {
              console.log(`⚠️ SOP ${sop.soP2Step} 已經被計算過，跳過重複添加`);
            }
          } else {
            console.log(`❌ 模型不匹配`);
          }
        });
      } else {
        console.log(`SOP ${sop.soP2Step} 沒有模型`);
      }
    });

    console.log('\n=== 前端檢查最終結果 ===');
    console.log('檢查結果:', {
      isUsed: usageCount > 0,
      usageCount,
      usedInSteps
    });

    return {
      isUsed: usageCount > 0,
      usageCount: usageCount,
      usedInSteps: usedInSteps
    };
  };
  //#endregion

  //#region 直接刪除模型（保留原有邏輯）
  const handleDirectDelete = (deletedTempModel, index) => {
    let newTempModels = [...tempModels];

    newTempModels[index] = {
      tempModelImageName: '',
      tempModelImageObj: null,
      tempModelImageUrl: '', // 清空圖片 URL
      tempModelFileName: '',
      tempModelFileObj: null,
      tempModelFileUrl: '', // 清空檔案 URL
      tempModelPX: 0.0,
      tempModelPY: 0.0,
      tempModelPZ: 0.0,
      tempModelRX: 0.0,
      tempModelRY: 0.0,
      tempModelRZ: 0.0,
      tempModelSX: 1.0,
      tempModelSY: 1.0,
      tempModelSZ: 1.0,
    };
    setTempModels(newTempModels);

    // 如果這個 tempModel 是從 sopModels 載入的，需要在 sopModels 中標記為刪除
    if (deletedTempModel.tempModelImageUrl && selectSOP && selectSOP.sopModels) {
      let newSOPs = [...sops];
      let newSelectSOP = { ...selectSOP };

      // 找到對應的 sopModel
      const sopModelIndex = newSelectSOP.sopModels.findIndex(sopModel =>
        sopModel.sopModelImage === deletedTempModel.tempModelImageUrl
      );

      if (sopModelIndex !== -1) {
        if (newSelectSOP.sopModels[sopModelIndex].sopModelId != 0) {
          // 已存在的模型標記為刪除
          newSelectSOP.sopModels[sopModelIndex].deleted = 1;
        } else {
          // 新模型直接移除
          newSelectSOP.sopModels.splice(sopModelIndex, 1);
        }

        // 更新整個sops
        var sopIndex = newSOPs.findIndex((x) => x.soP2Step == selectSOP.soP2Step);
        newSOPs[sopIndex] = newSelectSOP;

        setSelectSOP(newSelectSOP);
        setSOPs(newSOPs);
      }
    }
  };
  //#endregion

  //#region 執行混合級聯刪除
  const handleConfirmCascadeDelete = async () => {
    if (!cascadeDeleteModelInfo) return;

    setCascadeDeleteLoading(true);
    try {
      console.log('Starting hybrid cascade delete execution...');

      // 1. 先處理前端狀態中的模型刪除（未保存的模型）
      handleFrontendCascadeDelete();

      // 2. 如果有後端保存的模型，調用API進行數據庫級聯刪除
      let apiResponse = null;
      if (cascadeDeleteModelInfo.backendUsage && cascadeDeleteModelInfo.backendUsage.isUsed) {
        console.log('Executing backend cascade delete...');
        apiResponse = await apiCascadeDeleteModel({
          machineAddId: machineAddId,
          knowledgeBaseId: knowledgeBaseId,
          modelImageName: cascadeDeleteModelInfo.modelImageName,
          modelFileName: cascadeDeleteModelInfo.modelFileName
        });
      }

      if (!cascadeDeleteModelInfo.backendUsage?.isUsed || (apiResponse && apiResponse.code === '0000')) {
        // 級聯刪除成功，更新前端狀態
        const { tempModelIndex } = cascadeDeleteModelInfo;

        // 清空 3D Model List 中的模型
        let newTempModels = [...tempModels];
        newTempModels[tempModelIndex] = {
          tempModelImageName: '',
          tempModelImageObj: null,
          tempModelImageUrl: '',
          tempModelFileName: '',
          tempModelFileObj: null,
          tempModelFileUrl: '',
          tempModelPX: 0.0,
          tempModelPY: 0.0,
          tempModelPZ: 0.0,
          tempModelRX: 0.0,
          tempModelRY: 0.0,
          tempModelRZ: 0.0,
          tempModelSX: 1.0,
          tempModelSY: 1.0,
          tempModelSZ: 1.0,
        };
        setTempModels(newTempModels);

        // 更新所有受影響的 SOP 步驟，移除相關模型
        let newSOPs = [...sops];
        console.log('Updating SOPs after cascade delete. UsedInSteps:', cascadeDeleteModelInfo.usedInSteps);
        console.log('Current SOPs:', newSOPs);
        console.log('Current selectSOP:', selectSOP);

        cascadeDeleteModelInfo.usedInSteps.forEach(stepInfo => {
          console.log('Processing step:', stepInfo);
          // 修正字段名稱：後端返回的字段名稱可能是 soP2Id, sop2Id, 或 SOP2Id
          const targetSopId = stepInfo.soP2Id || stepInfo.sop2Id || stepInfo.SOP2Id;
          const sopIndex = newSOPs.findIndex(sop => sop.soP2Id === targetSopId);
          console.log('Target SOP ID:', targetSopId);
          console.log('Available SOPs:', newSOPs.map(s => ({id: s.soP2Id, step: s.soP2Step})));
          console.log('Found SOP index:', sopIndex);

          if (sopIndex !== -1 && newSOPs[sopIndex].sopModels) {
            console.log('Before filtering models:', newSOPs[sopIndex].sopModels);
            console.log('Looking for sopModelId:', stepInfo.sopModelId || stepInfo.SOPModelId);

            // 移除相關模型 - 支持兩種字段名稱格式
            const targetModelId = stepInfo.sopModelId || stepInfo.SOPModelId;
            newSOPs[sopIndex].sopModels = newSOPs[sopIndex].sopModels.filter(model => {
              const modelId = model.sopModelId || model.SOPModelId;
              const shouldKeep = modelId !== targetModelId;
              console.log(`Model ${modelId}: ${shouldKeep ? 'keeping' : 'removing'}`);
              return shouldKeep;
            });
            console.log('After filtering models:', newSOPs[sopIndex].sopModels);
          }
        });
        setSOPs(newSOPs);

        // 直接更新當前選中的 SOP（重要！）
        if (selectSOP) {
          console.log('Updating selectSOP. Current sopModels:', selectSOP.sopModels);

          // 檢查當前選中的SOP是否受到影響
          const isCurrentSOPAffected = cascadeDeleteModelInfo.usedInSteps.some(stepInfo => {
            const targetSopId = stepInfo.soP2Id || stepInfo.sop2Id || stepInfo.SOP2Id;
            return selectSOP.soP2Id === targetSopId;
          });

          if (isCurrentSOPAffected) {
            console.log('Current SOP is affected, updating selectSOP directly');
            let updatedSelectSOP = { ...selectSOP };

            // 移除相關模型
            cascadeDeleteModelInfo.usedInSteps.forEach(stepInfo => {
              const targetModelId = stepInfo.sopModelId || stepInfo.SOPModelId;
              console.log('Removing model with ID:', targetModelId);

              if (updatedSelectSOP.sopModels) {
                updatedSelectSOP.sopModels = updatedSelectSOP.sopModels.filter(model => {
                  const modelId = model.sopModelId || model.SOPModelId;
                  const shouldKeep = modelId !== targetModelId;
                  console.log(`Model ${modelId}: ${shouldKeep ? 'keeping' : 'removing'}`);
                  return shouldKeep;
                });
              }
            });

            console.log('Updated selectSOP sopModels:', updatedSelectSOP.sopModels);
            setSelectSOP(updatedSelectSOP);
          } else {
            // 如果當前SOP沒有受到影響，但仍然需要檢查sops數組中的更新
            const updatedSelectSOP = newSOPs.find(sop => sop.soP2Id === selectSOP.soP2Id);
            if (updatedSelectSOP) {
              setSelectSOP(updatedSelectSOP);
            }
          }
        }

        setShowCascadeDeleteModal(false);
        setCascadeDeleteModelInfo(null);

        // 顯示成功消息
        console.log('混合級聯刪除成功 - 前端狀態已更新');
        if (apiResponse) {
          console.log('後端刪除結果:', apiResponse.result);
        }

        // 提示用戶需要保存以完成刪除
        console.log('請點擊"儲存"按鈕以完成級聯刪除操作');

        // 可選：顯示提示信息給用戶
        // alert('模型已從界面中移除，請點擊"儲存"按鈕以完成刪除操作');
      } else {
        console.error('級聯刪除失敗:', apiResponse);
        alert('刪除失敗，請稍後再試');
      }
    } catch (error) {
      console.error('級聯刪除時發生錯誤:', error);
      alert('刪除時發生錯誤，請稍後再試');
    } finally {
      setCascadeDeleteLoading(false);
    }
  };
  //#endregion

  //#region 處理前端狀態的級聯刪除
  const handleFrontendCascadeDelete = () => {
    if (!cascadeDeleteModelInfo) return;

    console.log('Executing frontend cascade delete...');
    const { tempModelIndex, deletedTempModel, frontendUsage } = cascadeDeleteModelInfo;

    // 1. 清空 3D Model List 中的模型
    let newTempModels = [...tempModels];
    newTempModels[tempModelIndex] = {
      tempModelImageName: '',
      tempModelImageObj: null,
      tempModelImageUrl: '',
      tempModelFileName: '',
      tempModelFileObj: null,
      tempModelFileUrl: '',
      tempModelPX: 0.0,
      tempModelPY: 0.0,
      tempModelPZ: 0.0,
      tempModelRX: 0.0,
      tempModelRY: 0.0,
      tempModelRZ: 0.0,
      tempModelSX: 1.0,
      tempModelSY: 1.0,
      tempModelSZ: 1.0,
    };
    setTempModels(newTempModels);

    // 2. 移除所有SOP步驟中的相關模型（包括未保存的）
    if (frontendUsage && frontendUsage.usedInSteps.length > 0) {
      let newSOPs = [...sops];

      frontendUsage.usedInSteps.forEach(stepInfo => {
        const sopIndex = newSOPs.findIndex(sop => sop.soP2Id === stepInfo.soP2Id);
        if (sopIndex !== -1 && newSOPs[sopIndex].sopModels) {
          console.log(`Removing frontend model from SOP ${stepInfo.soP2Id}`);

          // 移除相關模型 - 使用更寬鬆的匹配邏輯
          newSOPs[sopIndex].sopModels = newSOPs[sopIndex].sopModels.filter(model => {
            const modelImageName = model.sopModelImage ? model.sopModelImage.split('/').pop() : '';
            const modelFileName = model.sopModelFile ? model.sopModelFile.split('/').pop() : '';

            // 更寬鬆的匹配邏輯
            const imageNameMatch = modelImageName && deletedTempModel.tempModelImageName &&
                                  modelImageName === deletedTempModel.tempModelImageName;
            const fileNameMatch = modelFileName && deletedTempModel.tempModelFileName &&
                                 modelFileName === deletedTempModel.tempModelFileName;

            // 也檢查完整路徑匹配
            const imageUrlMatch = model.sopModelImage && deletedTempModel.tempModelImageUrl &&
                                 model.sopModelImage === deletedTempModel.tempModelImageUrl;
            const fileUrlMatch = model.sopModelFile && deletedTempModel.tempModelFileUrl &&
                                model.sopModelFile === deletedTempModel.tempModelFileUrl;

            const isTargetModel = imageNameMatch || fileNameMatch || imageUrlMatch || fileUrlMatch;

            if (isTargetModel) {
              console.log(`Removing model: ${modelImageName || modelFileName}`);
            }

            return !isTargetModel;
          });
        }
      });

      setSOPs(newSOPs);

      // 3. 更新當前選中的SOP
      if (selectSOP) {
        const updatedSelectSOP = newSOPs.find(sop => sop.soP2Id === selectSOP.soP2Id);
        if (updatedSelectSOP) {
          console.log('Updating selectSOP after frontend cascade delete');
          setSelectSOP(updatedSelectSOP);
        }
      }
    }
  };
  //#endregion

  //#region 關閉上傳暫存3DModel Modal
  const handleCloseSaveTempModelModal = () => {
    setSelectTempModelIndex(-1);
    setSelectTempModel(null);
    setShowSaveTempModelModal(false);
  };
  //#endregion

  //#region 上傳3DModel圖片按鈕
  const handleUploadTempModelImageBtn = (e) => {
    e.preventDefault();
    inputTempModelImageRef.current.click();
  };
  //#endregion

  //#region 上傳3DModel圖片Change事件
  const onTempModelImageChange = (e) => {
    var file, img;
    file = e.target.files[0];
    let newSelectTempModel = { ...selectTempModel };
    let newSelectTempModelErrors = { ...selectTempModelErrors };
    if (file != null) {
      let newSelectTempModelErrors = { ...selectTempModelErrors };
      var fileExtension = file.name
        .substr(file.name.lastIndexOf('.') + 1 - file.name.length)
        .toLowerCase();
      if (
        !(
          fileExtension == 'png' ||
          fileExtension == 'jpg' ||
          fileExtension == 'jpeg'
        )
      ) {
        newSelectTempModelErrors.tempModelImage = 'format';

        newSelectTempModel.tempModelImageName = '';
        newSelectTempModel.tempModelImageObj = null;

        setSelectTempModel(newSelectTempModel);
        setSelectTempModelErrors(newSelectTempModelErrors);
      } else {
        var img = new Image();
        var objectUrl = URL.createObjectURL(file);
        img.onload = function () {
          newSelectTempModelErrors.tempModelImage = '';

          newSelectTempModel.tempModelImageName = file.name;
          newSelectTempModel.tempModelImageObj = file;

          setSelectTempModel(newSelectTempModel);
          setSelectTempModelErrors(newSelectTempModelErrors);
        };
        img.src = objectUrl;
      }
    } else {
      newSelectTempModelErrors.tempModelImage = '';
      setSelectTempModelErrors(newSelectTempModelErrors);
    }
  };
  //#endregion

  //#region 上傳檔案Change事件
  const onFileChange = (e) => {
    let newSelectTempModel = { ...selectTempModel };
    let newSelectTempModelErrors = { ...selectTempModelErrors };
    var file = e.target.files[0];
    if (file != null) {
      var fileExtension = file.name
        .substr(file.name.lastIndexOf('.') + 1 - file.name.length)
        .toLowerCase();
      if (!(fileExtension == 'zip')) {
        newSelectTempModelErrors.tempModelFile = 'format';
        newSelectTempModel.tempModelFileObj = null;
        newSelectTempModel.tempModelFileName = '';
      } else {
        newSelectTempModelErrors.tempModelFile = '';
        newSelectTempModel.tempModelFileObj = file;
        newSelectTempModel.tempModelFileName = file.name; // 設置檔案名稱
      }
      setSelectTempModelErrors(newSelectTempModelErrors);
      setSelectTempModel(newSelectTempModel);
    } else {
      newSelectTempModelErrors.tempModelFile = '';
      setSelectTempModelErrors(newSelectTempModelErrors);
    }
  };
  //#endregion

  //#region 儲存暫存 3DModel
  // const handleSaveTempModel = async (e) => {
  //   e.preventDefault();
  //   let newTempModels = [...tempModels];

  //   //判斷圖片/檔案是否為空
  //   if (
  //     selectTempModel &&
  //     (selectTempModel.tempModelImageObj == null ||
  //       selectTempModel.tempModelFileObj == null)
  //   ) {
  //     let newSelectTempModelErrors = { ...selectTempModelErrors };
  //     if (selectTempModel.tempModelImageObj == null) {
  //       newSelectTempModelErrors.tempModelImage = 'required';
  //     }

  //     if (selectTempModel.tempModelFileObj == null) {
  //       newSelectTempModelErrors.tempModelFile = 'required';
  //     }

  //     setSelectTempModelErrors(newSelectTempModelErrors);
  //   } else {
  //     if (
  //       selectTempModelErrors.tempModelImage == '' &&
  //       selectTempModelErrors.tempModelFile == ''
  //     ) {
  //       newTempModels[selectTempModelIndex] = selectTempModel;

  //       setTempModels(newTempModels);
  //       setShowSaveTempModelModal(false);
  //     }
  //   }
  // };
  //#endregion

  //#region 儲存暫存 3DModel
  const handleSaveTempModel = async (e) => {
    e.preventDefault();
    let newTempModels = [...tempModels];

    //判斷圖片/檔案是否為空
    if (
      selectTempModel &&
      (selectTempModel.tempModelImageObj == null ||
        selectTempModel.tempModelFileObj == null)
    ) {
      let newSelectTempModelErrors = { ...selectTempModelErrors };
      if (selectTempModel.tempModelImageObj == null) {
        newSelectTempModelErrors.tempModelImage = 'required';
      }

      if (selectTempModel.tempModelFileObj == null) {
        newSelectTempModelErrors.tempModelFile = 'required';
      }

      setSelectTempModelErrors(newSelectTempModelErrors);
    } else {
      if (
        selectTempModelErrors.tempModelImage == '' &&
        selectTempModelErrors.tempModelFile == ''
      ) {
        // 確保檔案名稱正確設置
        let updatedTempModel = { ...selectTempModel };

        // 如果有圖片檔案物件但沒有檔案名稱，設置檔案名稱
        if (updatedTempModel.tempModelImageObj && !updatedTempModel.tempModelImageName) {
          updatedTempModel.tempModelImageName = updatedTempModel.tempModelImageObj.name;
        }

        // 如果有模型檔案物件但沒有檔案名稱，設置檔案名稱
        if (updatedTempModel.tempModelFileObj && !updatedTempModel.tempModelFileName) {
          updatedTempModel.tempModelFileName = updatedTempModel.tempModelFileObj.name;
        }

        console.log('Saving temp model with data:', updatedTempModel);

        newTempModels[selectTempModelIndex] = updatedTempModel;

        setTempModels(newTempModels);
        setShowSaveTempModelModal(false);
      }
    }
  };
  //#endregion

  //#region 拖曳Model
  const onModelDragEnd = (event) => {
    try {
      const { source, destination } = event; //source：被拖曳的卡片原先的 DroppableId 與順序；destination：被拖曳的卡片最終的 DroppableId 與順序

      console.log('onModelDragEnd called:', { source, destination });

      if (!destination) {
        console.log('No destination, returning');
        return;
      }

      // 添加額外的驗證
      if (!source || !source.droppableId || !destination.droppableId) {
        console.log('Invalid source or destination, returning');
        return;
      }

    if (
      source.droppableId == '3DModel' &&
      destination.droppableId == '3DModel'
    ) {
      console.log('Same droppable area, returning');
      return;
    }

    let newSOPs = [...sops];
    let newTempModels = [...tempModels];
    let newSelectSOP = { ...selectSOP };

    console.log('Current selectSOP:', newSelectSOP);
    console.log('Current selectSOP.sopModels:', newSelectSOP.sopModels);

    // 獲取要拖曳的模型資料（複製，不移除原位置）
    const draggedModel = newTempModels[source.index];
    console.log('Dragged model:', draggedModel);

    // 不清空原位置，保留 3D Model List 中的模型作為模型庫

    // 檢查是否有圖片（檔案物件或 URL）和檔案
    const hasValidImage = draggedModel.tempModelImageObj != null || draggedModel.tempModelImageUrl;
    const hasValidFile = draggedModel.tempModelFileObj != null || draggedModel.tempModelFileName || draggedModel.tempModelFileUrl;

    console.log('Drag validation:', {
      hasImageObj: draggedModel.tempModelImageObj != null,
      hasImageUrl: !!draggedModel.tempModelImageUrl,
      hasFileObj: draggedModel.tempModelFileObj != null,
      hasFileName: !!draggedModel.tempModelFileName,
      hasFileUrl: !!draggedModel.tempModelFileUrl,
      hasValidImage,
      hasValidFile,
      draggedModel
    });

    if (!hasValidImage || !hasValidFile) {
      console.log('Dragged model missing required data, but continuing with drag operation');
      // 繼續執行拖拽，但創建一個空的模型
    }

    if (destination.droppableId == '3DModel') {
      // 確保 sopModels 陣列存在
      if (!newSelectSOP.sopModels) {
        newSelectSOP.sopModels = [];
      }

      var newModel = {
        sopModelId: 0,
        deleted: 0,
        sopModelImage: (() => {
          if (draggedModel.tempModelImageObj) {
            console.log('Dragging new uploaded model with image object:', draggedModel.tempModelImageName);
            return draggedModel.tempModelImageName;
          } else if (draggedModel.tempModelImageUrl) {
            // 對於從 3D Model List 拖拽的模型，保持完整的 URL
            console.log('Dragging existing model with image URL:', draggedModel.tempModelImageUrl);
            return draggedModel.tempModelImageUrl;
          }
          return '';
        })(),
        sopModelImageObj: draggedModel.tempModelImageObj,
        sopModelFile: (() => {
          if (draggedModel.tempModelFileObj) {
            console.log('Dragging new uploaded model with file object:', draggedModel.tempModelFileName);
            return draggedModel.tempModelFileName;
          } else if (draggedModel.tempModelFileUrl) {
            // 對於從 3D Model List 拖拽的模型，保持完整的 URL
            console.log('Dragging existing model with file URL:', draggedModel.tempModelFileUrl);
            return draggedModel.tempModelFileUrl;
          } else if (draggedModel.tempModelFileName) {
            console.log('Dragging model with file name only:', draggedModel.tempModelFileName);
            return draggedModel.tempModelFileName;
          }
          return '';
        })(),
        sopModelFileObj: draggedModel.tempModelFileObj,
        sopModelPX: 0.0,
        sopModelPY: 0.0,
        sopModelPZ: 0.0,
        sopModelRX: 0.0,
        sopModelRY: 0.0,
        sopModelRZ: 0.0,
        sopModelSX: 1.0,
        sopModelSY: 1.0,
        sopModelSZ: 1.0,
        isDeletedSOPModelImage: false,
        isDeletedSOPModelFile: false,
        // 添加 URL 屬性以支持從 tempModels 拖拽的模型
        tempModelImageUrl: draggedModel.tempModelImageUrl,
        tempModelFileUrl: draggedModel.tempModelFileUrl,
        // 保存原始圖片 URL 以備用
        originalImageUrl: draggedModel.tempModelImageUrl,
      };

      newSelectSOP.sopModels.splice(destination.index, 0, newModel);
      console.log('Added new model to sopModels:', newModel);
      console.log('newModel.sopModelImage:', newModel.sopModelImage);
      console.log('newModel.tempModelImageUrl:', newModel.tempModelImageUrl);
      console.log('Updated sopModels:', newSelectSOP.sopModels);
    }

    //更新整個sops
    var index = newSOPs.findIndex((x) => x.soP2Step == selectSOP.soP2Step);
    newSOPs[index] = newSelectSOP;

    console.log('Updated SOP in SOPs array:', newSelectSOP);

    // 不更新 tempModels，保留 3D Model List 中的模型作為模型庫
    setSelectSOP(newSelectSOP);
    setSOPs(newSOPs);
    } catch (error) {
      console.error('Error in onModelDragEnd:', error);
      // 在發生錯誤時，確保狀態保持一致
      toast.error('拖拽操作失敗，請重試', {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 2000,
        hideProgressBar: true,
        closeOnClick: false,
        pauseOnHover: true,
      });
    }
  };
  //#endregion

  //#region 清空3DModel
  const handleCleanModelBtn = (index) => {
    let newSOPs = [...sops];
    let newSelectSOP = { ...selectSOP };

    if (newSelectSOP.sopModels[index].sopModelId != 0) {
      newSelectSOP.sopModels[index].deleted = 1;
    } else {
      const [remove] = newSelectSOP.sopModels.splice(index, 1);
    }

    //更新整個sops
    var sopIndex = newSOPs.findIndex((x) => x.soP2Step == selectSOP.soP2Step);
    newSOPs[sopIndex] = newSelectSOP;

    setSelectSOP(newSelectSOP);
    setSOPs(newSOPs);
  };
  //#endregion

  //#region 編輯 SOPModel
  const handleEditSOPModel = (index) => {
    const sopModel = selectSOP.sopModels[index];
    if (!sopModel) return;

    // 找到一個空的 tempModel 位置來編輯
    const emptyIndex = tempModels.findIndex(temp =>
      !temp.tempModelImageObj && !temp.tempModelImageUrl
    );

    if (emptyIndex === -1) {
      alert('請先清空一個 3D Model List 位置來編輯此模型');
      return;
    }

    // 將 sopModel 的資料複製到 tempModel 進行編輯
    let newTempModels = [...tempModels];
    newTempModels[emptyIndex] = {
      tempModelImageName: sopModel.sopModelImage ? sopModel.sopModelImage.split('/').pop() : '',
      tempModelImageObj: sopModel.sopModelImageObj || null,
      tempModelImageUrl: sopModel.sopModelImage || '',
      tempModelFileName: sopModel.sopModelFile ? sopModel.sopModelFile.split('/').pop() : '',
      tempModelFileObj: sopModel.sopModelFileObj || null,
      tempModelFileUrl: sopModel.sopModelFile || '', // 添加檔案 URL
      editingSOPModelIndex: index, // 標記正在編輯的 SOPModel 索引
    };

    setTempModels(newTempModels);

    // 從 sopModels 中移除原模型（暫時）
    let newSOPs = [...sops];
    let newSelectSOP = { ...selectSOP };

    if (newSelectSOP.sopModels[index].sopModelId != 0) {
      newSelectSOP.sopModels[index].deleted = 1;
    } else {
      newSelectSOP.sopModels.splice(index, 1);
    }

    var sopIndex = newSOPs.findIndex((x) => x.soP2Step == selectSOP.soP2Step);
    newSOPs[sopIndex] = newSelectSOP;

    setSelectSOP(newSelectSOP);
    setSOPs(newSOPs);
  };
  //#endregion



  //#region 新增確認刪除的處理函數
  const handleConfirmDeleteSOP = () => {
    if (selectDeleteSOPIndex !== -1) {
      let newSOPs = [...sops];
      const currentStep = newSOPs[selectDeleteSOPIndex].soP2Step;

      // 標記當前步驟為已刪除
      newSOPs[selectDeleteSOPIndex].deleted = 1;

      // 重新排序剩餘步驟
      reorderSOPSteps(newSOPs);

      // 找到前一個未刪除的步驟
      const previousStep = newSOPs
        .filter((sop) => sop.deleted !== 1 && sop.soP2Step < currentStep)
        .pop();

      // 如果找到前一個步驟，則選中它
      if (previousStep) {
        setSelectSOP(previousStep);
      } else {
        // 如果沒有前一個步驟，找第一個未刪除的步驟
        const firstAvailableStep = newSOPs.find((sop) => sop.deleted !== 1);
        setSelectSOP(firstAvailableStep || null);
      }

      setSOPs(newSOPs);
      setShowDeleteSOPModal(false);
      setSelectDeleteSOPIndex(-1);

      // 顯示刪除成功提示
      toast.success('刪除成功!', {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 2000,
        hideProgressBar: true,
        closeOnClick: false,
        pauseOnHover: true,
      });
    }
  };
  //#endregion

  //#region 新增重新排序函數
  const reorderSOPSteps = (sops) => {
    let stepCount = 1;
    sops.forEach((sop) => {
      if (sop.deleted !== 1) {
        sop.soP2Step = stepCount++;
      }
    });
    return sops;
  };
  //#endregion

  const validateSOP = (sop) => {
    // 驗證必要字段
    if (!sop.soP2Step || !sop.soP2Message) {
      return false;
    }

    // 驗證圖片刪除標記與實際狀態是否一致
    if (sop.isDeletedSOP2Image && sop.soP2Image !== '') {
      return false;
    }

    if (sop.isDeletedSOP2RemarkImage && sop.soP2RemarkImage !== '') {
      return false;
    }

    return true;
  };

  return (
    <>
      <section className="content-header">
        <div className="container-fluid">
          <div className="d-flex mb-2 justify-content-between align-items-center">
            <div>
              <Link to="/knowledge" className={'fas fa-angle-left'}>
                {' '}
                知識庫
              </Link>
              <Link to="/document-editor" className={'fas fa-angle-left'}>
                {' '}
                故障說明
              </Link>
              {/* <i className="fas fa-angle-left"></i>&nbsp;&nbsp;{t("machineAlarm.content.header")}Alarm管理 */}
            </div>
            <div className="content-header-text-color">
              <h1>
                <strong>
                  {t('sop.content.header')}
                  {/*SOP*/}
                </strong>
              </h1>
            </div>
            <div>
              <div className={styles['buttons-container']}>
                <button
                  type="button"
                  className={classNames(styles['button'], styles['btn-save'], isGeneralUser ? 'general-user-disabled' : '')}
                  onClick={handleButtonClick(() => handleSaveSOP())}
                >
                  <span>
                    {t('btn.save')}
                    {/*儲存*/}
                  </span>
                  {/* )} */}
                </button>
                <a
                  href="/knowledge"
                  className={classNames(styles['button'], styles['btn-cancel'])}
                >
                  取消
                </a>
                <div
                  className={classNames(
                    styles['button'],
                    styles['btn-preview']
                  )}
                  onClick={handlePreview}
                >
                  預覽
                </div>

                {/* <div className={styles['showMachine']}>
                  <a
                    href="#"
                    className={classNames(
                      styles['button'],
                      styles['btn-showMachine']
                    )}
                  >
                    {SOPInfo?.machineInfo?.machineName}
                  </a>
                </div> */}
              </div>
              {isSOPName && <SOPName onClose={() => setIsSOPName(false)} />}

              {/* <button
                type="button"
                className="btn btn-primary btn-add"
                disabled={saveSOPLoading}
                onClick={(e) => handleSaveSOP(e)}
              >
                {saveSOPLoading ? (
                  <>
                    <Spinner
                      as="span"
                      animation="border"
                      size="sm"
                      role="status"
                      aria-hidden="true"
                    />
                  </>
                ) : (
                  <>
                    <i className="fas fa-plus"></i> {t("machineIOT.btn.save")}
                    儲存設定
                  </>
                )}
              </button> */}
            </div>
          </div>
        </div>
      </section>
      <section className="content" style={{ marginTop: 5 }}>
        <div className="container-fluid">
          <div className="row">
            <div className="col-sm-12 col-md-2 mb-2">
              <div className="sop-col-border-l">
                <DragDropContext onDragEnd={onDragEnd}>
                  <Droppable droppableId="sop-step">
                    {(provided) => (
                      <div
                        {...provided.droppableProps}
                        {...provided.dragHandleProps}
                        ref={provided.innerRef}
                      >
                        {sops
                          .filter((item) => item.deleted !== 1) // 只顯示未刪除的項目
                          .map((item, index) => (
                            <Draggable
                              key={index}
                              draggableId={item.soP2Step.toString()}
                              index={index}
                            >
                              {(provided) => (
                                <div
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  ref={provided.innerRef}
                                >
                                  <div
                                    className={`card ${
                                      item.soP2Step === selectSOP?.soP2Step
                                        ? 'bg-info'
                                        : ''
                                    }`}
                                    style={{ cursor: 'pointer' }}
                                  >
                                    <div
                                      className="card-body"
                                      onClick={(e) => handleSelectSOP(e, index)}
                                    >
                                      <div className="row">
                                        <div className="col-10">
                                          <span>Step {item.soP2Step}</span>
                                        </div>
                                        {index !== 0 && (
                                          <div className="col-2">
                                            <i className="fas fa-trash"></i>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </Draggable>
                          ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
                <div
                  className="card"
                  style={{ cursor: 'pointer', marginBottom: 0 }}
                  onClick={() => handleAddSOP()}
                >
                  <div className="card-body text-center text-info">
                    <i className="fas fa-plus"></i>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-sm-12 col-md-10 mb-2">
              <div className="sop-col-border-r">
                {selectSOP != null ? (
                  <div className="row">
                    <div className="col-sm-12 col-md-8 mb-2">
                      <div className="row mb-3">
                        <div className="col-12">
                          <div className="form-group">
                            <div className={styles['text-area-container']}>
                              <label>{t('sop.sopMessage')}</label>
                              <textarea
                                className="form-control"
                                rows="8"
                                name="soP2Message"
                                maxLength="1200"
                                value={selectSOP.soP2Message}
                                onChange={(e) => handleSelectSOPChange(e)}
                                style={{ color: textColor }}
                              ></textarea>
                              {/* 字數統計顯示 */}
                              <div className="d-flex justify-content-between align-items-center mt-1">
                                <small className="text-muted">
                                  字數限制: {calculateEffectiveLength(selectSOP.soP2Message || '')} / {CHARACTER_LIMITS.soP2Message}
                                </small>
                                <small 
                                  className={`badge ${
                                    calculateSpaceUsage(selectSOP.soP2Message || '', CHARACTER_LIMITS.soP2Message) >= 90 
                                      ? 'badge-danger' 
                                      : calculateSpaceUsage(selectSOP.soP2Message || '', CHARACTER_LIMITS.soP2Message) >= 85 
                                      ? 'badge-warning' 
                                      : 'badge-success'
                                  }`}
                                  style={{ opacity: 0.9 }}
                                >
                                  空間佔用: {calculateSpaceUsage(selectSOP.soP2Message || '', CHARACTER_LIMITS.soP2Message)}%
                                </small>
                              </div>
                            </div>
                          </div>
                          <div className="form-group">
                            <div className={styles['text-area-container']}>
                              <label>{t('sop.sopRemarksMessage')}</label>
                              <textarea
                                className="form-control"
                                rows="8"
                                name="soP2Remark"
                                maxLength="200"
                                value={selectSOP.soP2Remark}
                                onChange={(e) => handleSelectSOPChange(e)}
                                style={{ color: textColor }}
                              ></textarea>
                              {/* 字數統計顯示 */}
                              <div className="d-flex justify-content-between align-items-center mt-1">
                                <small className="text-muted">
                                  字數限制: {calculateEffectiveLength(selectSOP.soP2Remark || '')} / {CHARACTER_LIMITS.soP2Remark}
                                </small>
                                <small 
                                  className={`badge ${
                                    calculateSpaceUsage(selectSOP.soP2Remark || '', CHARACTER_LIMITS.soP2Remark) >= 90 
                                      ? 'badge-danger' 
                                      : calculateSpaceUsage(selectSOP.soP2Remark || '', CHARACTER_LIMITS.soP2Remark) >= 85 
                                      ? 'badge-warning' 
                                      : 'badge-success'
                                  }`}
                                  style={{ opacity: 0.9 }}
                                >
                                  空間佔用: {calculateSpaceUsage(selectSOP.soP2Remark || '', CHARACTER_LIMITS.soP2Remark)}%
                                </small>
                              </div>
                            </div>
                            {/* <div
                              className={styles['color-picker-container-sop']}
                            >
                              <Space direction="vertical">
                                <ColorPicker
                                  defaultValue={textColor}
                                  size="small"
                                  onChange={(color) =>
                                    setTextColor(color.hex)
                                  }
                                />
                              </Space>
                            </div> */}
                          </div>
                        </div>
                      </div>
                      <div className="row mb-3">
                        <div className="col-sm-12 col-md-6">
                          <div
                            className="form-group"
                            style={{ minHeight: '150px' }}
                          >
                            <label>
                              {t('sop.sopVideo')}
                              {/*步驟影片*/}
                            </label>
                            <div className="d-flex align-items-center justify-content-center sop-file-view">
                              {selectSOP.sopVideo != '' ||
                              selectSOP.sopVideoObj != null ? (
                                <video
                                  ref={videoSrcRef}
                                  width="100%"
                                  controls
                                  src={
                                    selectSOP.sopVideoObj != null
                                      ? URL.createObjectURL(
                                          selectSOP.sopVideoObj
                                        )
                                      : selectSOP.sopVideo
                                  }
                                ></video>
                              ) : (
                                <></>
                              )}
                            </div>
                            {(() => {
                              switch (videoError) {
                                case 'format':
                                  return (
                                    <div className="invalid-feedback d-block">
                                      <i className="fas fa-exclamation-circle"></i>{' '}
                                      {t('helpWord.imageFormat')}
                                      {/*圖片格式不正確*/}
                                    </div>
                                  );
                                default:
                                  return null;
                              }
                            })()}
                          </div>
                          <div className="mt-3">
                            <button
                              className="btn btn-primary"
                              onClick={(e) => handleUploadVideoBtn(e)}
                            >
                              {t('sop.btn.uploadSopVideo')}
                              {/*上傳影片*/}
                            </button>
                            <input
                              type="file"
                              className="form-control d-none"
                              name="soP2Image"
                              ref={inputVideoRef}
                              onChange={(e) => onVideoChange(e)}
                              autoComplete="off"
                              accept="video/mp4"
                            />{' '}
                            <button
                              className="btn btn-danger"
                              onClick={(e) => handleRemoveVideoBtn(e)}
                            >
                              {t('sop.btn.removeSopVideo')}
                              {/*移除影片*/}
                            </button>
                          </div>
                        </div>
                        <div className="col-sm-12 col-md-6">
                          <div
                            className="form-group"
                            style={{ minHeight: '150px' }}
                          >
                            <label>
                              {t('sop.sopImage')}
                              {/*步驟圖片*/}
                            </label>
                            <div className="d-flex align-items-center justify-content-center sop-file-view">
                              {selectSOP.soP2Image != '' ||
                              selectSOP.soP2ImageObj != null ? (
                                <img
                                  src={
                                    selectSOP.soP2ImageObj != null
                                      ? URL.createObjectURL(
                                          selectSOP.soP2ImageObj
                                        )
                                      : selectSOP.soP2Image
                                  }
                                  style={{ width: '100%' }}
                                />
                              ) : (
                                <></>
                              )}
                            </div>
                            {(() => {
                              switch (imageError) {
                                case 'format':
                                  return (
                                    <div className="invalid-feedback d-block">
                                      <i className="fas fa-exclamation-circle"></i>{' '}
                                      {t('helpWord.imageFormat')}
                                      {/*圖片格式不正確*/}
                                    </div>
                                  );
                                default:
                                  return null;
                              }
                            })()}
                          </div>
                          <div className="mt-3">
                            <button
                              className="btn btn-primary"
                              onClick={(e) => handleUploadImageBtn(e)}
                            >
                              {t('sop.btn.uploadSopImage')}
                              {/*上傳圖片*/}
                            </button>
                            <input
                              type="file"
                              className="form-control d-none"
                              name="soP2Image"
                              ref={inputImageRef}
                              onChange={(e) => onImageChange(e)}
                              autoComplete="off"
                              accept="image/png, image/jpeg"
                            />{' '}
                            <button
                              className="btn btn-danger"
                              onClick={(e) => handleRemoveImageBtn(e)}
                            >
                              {t('sop.btn.removeSopImage')}
                              {/*移除圖片*/}
                            </button>
                          </div>
                        </div>
                        <div className="col-sm-12 col-md-6">
                          <div
                            className="form-group"
                            style={{ minHeight: '150px', marginTop: '20px' }}
                          >
                            <label>
                              {t('sop.sopRemarksImage')}
                              {/*備註圖片*/}
                            </label>
                            <div className="d-flex align-items-center justify-content-center sop-file-view">
                              {selectSOP.soP2RemarkImage != '' ||
                              selectSOP.soP2RemarkImageObj != null ? (
                                <img
                                  src={
                                    selectSOP.soP2RemarkImageObj != null
                                      ? URL.createObjectURL(
                                          selectSOP.soP2RemarkImageObj
                                        )
                                      : selectSOP.soP2RemarkImage
                                  }
                                  style={{ width: '100%' }}
                                />
                              ) : (
                                <></>
                              )}
                            </div>
                            {(() => {
                              switch (imageError) {
                                case 'format':
                                  return (
                                    <div className="invalid-feedback d-block">
                                      <i className="fas fa-exclamation-circle"></i>{' '}
                                      {t('helpWord.imageFormat')}
                                      {/*圖片格式不正確*/}
                                    </div>
                                  );
                                default:
                                  return null;
                              }
                            })()}
                          </div>
                          <div className="mt-3">
                            <button
                              className="btn btn-primary"
                              onClick={(e) => handleUploadRemarksImageBtn(e)}
                            >
                              {t('sop.btn.uploadSopRemarksImage')}
                              {/*上傳圖片*/}
                            </button>
                            <input
                              type="file"
                              className="form-control d-none"
                              name="soP2Image"
                              ref={inputRemarksImageRef}
                              onChange={(e) => onRemarksImageChange(e)}
                              autoComplete="off"
                              accept="image/png, image/jpeg"
                            />{' '}
                            <button
                              className="btn btn-danger"
                              onClick={(e) => handleRemoveRemarksImageBtn(e)}
                            >
                              {t('sop.btn.removeSopRemarksImage')}
                              {/*移除圖片*/}
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="row">
                        <div className="col-12">
                          <div className="form-group">
                            <label>PLC</label>
                            <div className="row">
                              <div className="col">
                                <input
                                  type="text"
                                  className="form-control"
                                  placeholder="PLC1"
                                  name="plC1" // 修改為與後端一致的名稱
                                  maxLength="10"
                                  value={selectSOP.plC1} // 修改為與後端一致的名稱
                                  onChange={(e) => handleSelectSOPChange(e)}
                                />
                              </div>
                              <div className="col">
                                <input
                                  type="text"
                                  className="form-control"
                                  placeholder="PLC2"
                                  name="plC2"
                                  maxLength="10"
                                  value={selectSOP.plC2}
                                  onChange={(e) => handleSelectSOPChange(e)}
                                />
                              </div>
                              <div className="col">
                                <input
                                  type="text"
                                  className="form-control"
                                  placeholder="PLC3"
                                  name="plC3"
                                  maxLength="10"
                                  value={selectSOP.plC3}
                                  onChange={(e) => handleSelectSOPChange(e)}
                                />
                              </div>
                              <div className="col">
                                <input
                                  type="text"
                                  className="form-control"
                                  placeholder="PLC4"
                                  name="plC4"
                                  maxLength="10"
                                  value={selectSOP.plC4}
                                  onChange={(e) => handleSelectSOPChange(e)}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-sm-12 col-md-4 mb-2">
                      <DragDropContext onDragEnd={onModelDragEnd}>
                        <div className="row">
                          <div className="col-12">
                            <div className="form-group">
                              <label>3D Model List</label>

                              <Droppable droppableId="3DModelList">
                                {(provided) => (
                                  <div
                                    className="row"
                                    {...provided.droppableProps}
                                    {...provided.dragHandleProps}
                                    ref={provided.innerRef}
                                  >
                                    {tempModelNumbers.map((item, index) => {
                                      // 創建穩定的唯一 ID，避免使用 Date.now()
                                      const stableId = `3DModelList-${index}-${tempModels[index]?.tempModelImageName || 'empty'}`;
                                      return (
                                        <Draggable
                                          key={stableId}
                                          draggableId={stableId}
                                          index={index}
                                        >
                                          {(provided, snapshot) => (
                                            <div
                                              className="col-sm-6 col-md-3 mb-2 d-flex justify-content-center"
                                              {...provided.draggableProps}
                                              {...provided.dragHandleProps}
                                              ref={provided.innerRef}
                                              style={{
                                                ...provided.draggableProps.style,
                                                opacity: snapshot.isDragging ? 0.8 : 1,
                                                transform: snapshot.isDragging
                                                  ? `${provided.draggableProps.style?.transform} rotate(5deg)`
                                                  : provided.draggableProps.style?.transform,
                                              }}
                                            >
                                              <div
                                                className="sop-model-col"
                                                onClick={(e) =>
                                                  handleOpenSaveTempModelBtn(
                                                    e,
                                                    index
                                                  )
                                                }
                                              >
                                                {tempModels[index]
                                                  .tempModelImageObj != null ? (
                                                  <img
                                                    src={URL.createObjectURL(
                                                      tempModels[index]
                                                        .tempModelImageObj
                                                    )}
                                                    style={{
                                                      width: '100%',
                                                      height: '100%',
                                                    }}
                                                  />
                                                ) : tempModels[index]
                                                  .tempModelImageUrl ? (
                                                  <img
                                                    src={tempModels[index]
                                                      .tempModelImageUrl}
                                                    style={{
                                                      width: '100%',
                                                      height: '100%',
                                                    }}
                                                  />
                                                ) : (
                                                  <></>
                                                )}
                                                {(tempModels[index]
                                                  .tempModelImageObj != null ||
                                                  tempModels[index]
                                                  .tempModelImageUrl) ? (
                                                  <div
                                                    className="remove-temp-model"
                                                    style={{
                                                      position: 'absolute',
                                                      top: '0px',
                                                      right: '7px',
                                                    }}
                                                  >
                                                    <i className="fas fa-times"></i>
                                                  </div>
                                                ) : (
                                                  <></>
                                                )}
                                              </div>
                                            </div>
                                          )}
                                        </Draggable>
                                      );
                                    })}
                                    {provided.placeholder}
                                  </div>
                                )}
                              </Droppable>
                            </div>
                          </div>
                        </div>
                        <div className="d-flex justify-content-center">
                          <i className="fas fa-angle-double-down"></i>
                        </div>
                        <div className="row">
                          <div className="col-12">
                            <div className="form-group">
                              <label>3D Model</label>
                            </div>
                          </div>
                        </div>
                        <div className="row">
                          <div className="col-12" style={{ paddingLeft: '7px', paddingRight: '7px' }}>
                            <Droppable droppableId="3DModel">
                              {(provided) => (
                                <div
                                  className="row w-100"
                                  style={{
                                    minHeight: '345px',
                                    position: 'relative',
                                    border: '2px dashed #ccc',
                                    borderRadius: '8px',
                                    backgroundColor: '#f9f9f9',
                                    margin: '0'
                                  }}
                                  {...provided.droppableProps}
                                  {...provided.dragHandleProps}
                                  ref={provided.innerRef}
                                >
                                    {(() => {
                                      console.log('Rendering 3D Model area for selectSOP:', selectSOP);
                                      console.log('selectSOP.sopModels:', selectSOP?.sopModels);

                                      // 檢查是否有有效的模型（未刪除的）
                                      const hasValidModels = selectSOP &&
                                        selectSOP.sopModels &&
                                        selectSOP.sopModels.some(item => item.deleted == 0);

                                      // 如果沒有有效模型，顯示提示框
                                      if (!hasValidModels) {
                                        return (
                                          <div
                                            style={{
                                              position: 'absolute',
                                              top: '50%',
                                              left: '50%',
                                              transform: 'translate(-50%, -50%)',
                                              textAlign: 'center',
                                              color: '#666',
                                              fontSize: '16px',
                                              fontWeight: '500',
                                              pointerEvents: 'none',
                                              zIndex: 1
                                            }}
                                          >
                                            <i className="fas fa-arrow-up" style={{ fontSize: '24px', marginBottom: '10px', display: 'block' }}></i>
                                            拖拽上方 3D Model List 中的模型到此區域
                                          </div>
                                        );
                                      }

                                      return selectSOP &&
                                        selectSOP.sopModels &&
                                        selectSOP.sopModels.map((item, index) => {
                                          console.log(`Rendering sopModel ${index}:`, item);
                                          if (item.deleted == 0) {
                                          // 創建穩定的唯一 ID
                                          const stableId = `3DModel-${selectSOP?.soP2Step || 0}-${index}-${item.sopModelId || Date.now()}`;
                                          return (
                                            <Draggable
                                              key={stableId}
                                              draggableId={stableId}
                                              index={index}
                                            >
                                              {(provided, snapshot) => (
                                                <div
                                                  className="col-sm-6 col-md-3 mt-2 mb-2 d-flex justify-content-center"
                                                  {...provided.draggableProps}
                                                  {...provided.dragHandleProps}
                                                  ref={provided.innerRef}
                                                  style={{
                                                    ...provided.draggableProps.style,
                                                    opacity: snapshot.isDragging ? 0.8 : 1,
                                                    transform: snapshot.isDragging
                                                      ? `${provided.draggableProps.style?.transform} rotate(5deg)`
                                                      : provided.draggableProps.style?.transform,
                                                  }}
                                                >
                                                  {(item.sopModelImage != '' ||
                                                  item.sopModelImageObj != null ||
                                                  item.tempModelImageUrl != '') ? (
                                                    <div className="sop-model-col">
                                                      <img
                                                        src={(() => {
                                                          // 第一優先：檔案物件（新上傳的）
                                                          if (item.sopModelImageObj != null) {
                                                            console.log('Using sopModelImageObj for model:', item);
                                                            return URL.createObjectURL(item.sopModelImageObj);
                                                          }

                                                          // 第二優先：sopModelImage（從後端載入的，優先使用實際存在的路徑）
                                                          if (item.sopModelImage) {
                                                            console.log('Using sopModelImage for model:', item.sopModelImage);
                                                            return item.sopModelImage;
                                                          }

                                                          // 第三優先：tempModelImageUrl（從 3D Model List 拖拽來的）
                                                          if (item.tempModelImageUrl) {
                                                            console.log('Using tempModelImageUrl for model:', item.tempModelImageUrl);
                                                            return item.tempModelImageUrl;
                                                          }

                                                          // 第四優先：originalImageUrl（備用的原始 URL）
                                                          if (item.originalImageUrl) {
                                                            console.log('Using originalImageUrl for dragged model:', item.originalImageUrl);
                                                            return item.originalImageUrl;
                                                          }

                                                          console.log('No valid image source found for model:', item);
                                                          return '';
                                                        })()}
                                                        style={{
                                                          width: '100%',
                                                          height: '100%',
                                                        }}
                                                        onError={(e) => {
                                                          console.error('Image load error for sopModel:', {
                                                            src: e.target.src,
                                                            item: item,
                                                            sopModelImage: item.sopModelImage,
                                                            tempModelImageUrl: item.tempModelImageUrl,
                                                            originalImageUrl: item.originalImageUrl,
                                                            hasImageObj: item.sopModelImageObj != null,
                                                            currentSOP: selectSOP?.soP2Step
                                                          });

                                                          // 如果當前是SOP路徑失敗，嘗試回退到知識庫路徑
                                                          if (item.sopModelImage && e.target.src === item.sopModelImage && item.sopModelImage.includes('/sop2/') && knowledgeBaseId && machineAddId) {
                                                            const fileName = item.sopModelImage.split('/').pop();
                                                            const knowledgeBasePath = `${window.apiUrl}/upload/machineAdd/${machineAddId}/knowledgeBase/${knowledgeBaseId}/models/${fileName}`;
                                                            console.log('SOP path failed, falling back to knowledge base path:', knowledgeBasePath);
                                                            e.target.src = knowledgeBasePath;
                                                            return;
                                                          }

                                                          // 嘗試回退到其他圖片源
                                                          if (item.tempModelImageUrl && e.target.src !== item.tempModelImageUrl) {
                                                            console.log('Falling back to tempModelImageUrl:', item.tempModelImageUrl);
                                                            e.target.src = item.tempModelImageUrl;
                                                            return;
                                                          }

                                                          if (item.originalImageUrl && e.target.src !== item.originalImageUrl) {
                                                            console.log('Falling back to originalImageUrl:', item.originalImageUrl);
                                                            e.target.src = item.originalImageUrl;
                                                            return;
                                                          }

                                                          // 如果所有圖片都失敗，隱藏圖片
                                                          console.log('All image sources failed, hiding image');
                                                          e.target.style.display = 'none';
                                                        }}
                                                        onLoad={(e) => {
                                                          console.log('Image loaded successfully for sopModel:', {
                                                            src: e.target.src,
                                                            currentSOP: selectSOP?.soP2Step
                                                          });
                                                        }}
                                                      />
                                                      <div
                                                        className="remove-temp-model"
                                                        style={{
                                                          position: 'absolute',
                                                          top: '0px',
                                                          right: '7px',
                                                        }}
                                                        onClick={() =>
                                                          handleCleanModelBtn(
                                                            index
                                                          )
                                                        }
                                                      >
                                                        <i className="fas fa-times"></i>
                                                      </div>
                                                    </div>
                                                  ) : (
                                                    <></>
                                                  )}
                                                </div>
                                              )}
                                            </Draggable>
                                          );
                                          }
                                          return null;
                                        });
                                    })()}
                                    {provided.placeholder}
                                  </div>
                                )}
                              </Droppable>
                            </div>
                          </div>
                        </DragDropContext>
                    </div>
                  </div>
                ) : (
                  <div
                    className="d-flex justify-content-center align-items-center"
                    style={{ height: '80vh' }}
                  >
                    <div>
                      <strong>
                        {t('sop.emptySelectSOP')}
                        {/*尚未選擇SOP*/}
                      </strong>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      <ToastContainer />

      {/*save temp model modal - start*/}
      <Modal
        show={showSaveTempModelModal}
        onHide={(e) => handleCloseSaveTempModelModal(e)}
        backdrop="static"
      >
        <Modal.Header closeButton>
          <Modal.Title>3D Model List</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <form>
            <div className="row">
              <div className="col-12 form-group" style={{ marginTop: '15px' }}>
                <label className="form-label">
                  <span className="text-danger">*</span>
                  {t('sop.tempModelImage')}
                  {/*3D模型圖片*/}
                </label>
                <input
                  type="file"
                  className="form-control d-none"
                  name="machineImage"
                  ref={inputTempModelImageRef}
                  onChange={(e) => onTempModelImageChange(e)}
                  autoComplete="off"
                  accept="image/png, image/jpeg"
                />
                <div
                  style={{
                    borderStyle: 'dotted',
                    borderWidth: '3px', // 調整虛線的大小
                    cursor: 'pointer',
                    minHeight: '240px',
                  }}
                  className="d-flex justify-content-center align-items-center"
                  onClick={(e) => handleUploadTempModelImageBtn(e)}
                >
                  {selectTempModel &&
                  selectTempModel.tempModelImageObj != null ? (
                    <img
                      alt="not found"
                      style={{ width: '100px', height: '100px' }}
                      src={
                        selectTempModel.tempModelImageObj != null
                          ? URL.createObjectURL(
                              selectTempModel.tempModelImageObj
                            )
                          : null
                      }
                    />
                  ) : (
                    <span>
                      {t('machine.uploadImage')}
                      {/*上傳圖片*/}
                    </span>
                  )}
                </div>
                {(() => {
                  switch (selectTempModelErrors.tempModelImage) {
                    case 'required':
                      return (
                        <div className="invalid-feedback d-block">
                          <i className="fas fa-exclamation-circle"></i>{' '}
                          {t('helpWord.required')}
                          {/*不得空白*/}
                        </div>
                      );
                    case 'format':
                      return (
                        <div className="invalid-feedback d-block">
                          <i className="fas fa-exclamation-circle"></i>{' '}
                          {t('helpWord.imageFormat')}
                          {/*圖片格式不正確*/}
                        </div>
                      );
                    default:
                      return null;
                  }
                })()}
              </div>
              <div className="col-12 form-group" style={{ marginTop: '15px', marginBottom: '15px' }}>
                <label className="form-label">
                  <span className="text-danger">*</span>
                  {t('sop.tempModelFile')}
                  {/*3D模型檔案*/}
                </label>
                <br />
                {(() => {
                  var fileExtension = '';
                  if (
                    selectTempModel &&
                    selectTempModel.tempModelFileObj != null
                  ) {
                    fileExtension = selectTempModel.tempModelFileObj.name
                      .substr(
                        selectTempModel.tempModelFileObj.name.lastIndexOf('.') +
                          1 -
                          selectTempModel.tempModelFileObj.name.length
                      )
                      .toLowerCase();
                  }

                  // 移除多餘的檔案類型圖標顯示
                  return null;
                })()}
                <input
                  type="file"
                  className="form-control"
                  name="tempModelFile"
                  onChange={(e) => onFileChange(e)}
                  autoComplete="off"
                  accept=".zip"
                  style={{ padding: '.2rem .3rem' }}  // ✅ 加這行
                />
                {(() => {
                  switch (selectTempModelErrors.tempModelFile) {
                    case 'required':
                      return (
                        <div className="invalid-feedback d-block">
                          <i className="fas fa-exclamation-circle"></i>{' '}
                          {t('helpWord.required')}
                          {/*不得空白*/}
                        </div>
                      );
                    case 'format':
                      return (
                        <div className="invalid-feedback d-block">
                          <i className="fas fa-exclamation-circle"></i>{' '}
                          {t('helpWord.imageFormat')}
                          {/*檔案格式不正確*/}
                        </div>
                      );
                    default:
                      return null;
                  }
                })()}
              </div>
            </div>
          </form>
        </Modal.Body>
        <Modal.Footer>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={(e) => handleCloseSaveTempModelModal(e)}
          >
            {t('btn.cancel')}
            {/*取消*/}
          </button>
          <button
            type="button"
            className={getButtonClassName("btn btn-primary")}
            onClick={handleButtonClick((e) => handleSaveTempModel(e))}
          >
            <span>
              {t('btn.confirm')}
              {/*確定*/}
            </span>
          </button>
        </Modal.Footer>
      </Modal>
      {/*add temp model modal - end*/}

      {/*delete topic modal - start*/}
      <Modal
        show={showDeleteSOPModal}
        onHide={handleCloseDeleteSOPModal}
        backdrop="static"
      >
        <Modal.Header closeButton>
          <Modal.Title>{t('sop.deleteSOP')}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>{t('sop.deleteContent')}</p>
        </Modal.Body>
        <Modal.Footer>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={handleCloseDeleteSOPModal}
          >
            {t('btn.cancel')}
          </button>
          <button
            type="button"
            className={getButtonClassName("btn btn-primary")}
            onClick={handleButtonClick(handleConfirmDeleteSOP)}
          >
            <span>{t('btn.confirm')}</span>
          </button>
        </Modal.Footer>
      </Modal>

      {/* 級聯刪除確認對話框 */}
      <Modal
        show={showCascadeDeleteModal}
        onHide={() => setShowCascadeDeleteModal(false)}
        backdrop="static"
        size="lg"
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>
            確認刪除 3D 模型
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {cascadeDeleteModelInfo && (
            <div>
              <p><strong>您即將刪除以下模型：</strong></p>
              <ul>
                <li>圖片檔案：{cascadeDeleteModelInfo.modelImageName}</li>
                <li>模型檔案：{cascadeDeleteModelInfo.modelFileName}</li>
              </ul>

              {cascadeDeleteModelInfo.isUsed ? (
                <div className="alert alert-warning">
                  <h6><i className="fas fa-exclamation-triangle"></i> 警告：此模型正在被使用</h6>
                  <p>此模型目前被 <strong>{cascadeDeleteModelInfo.usageCount}</strong> 個 SOP 步驟使用：</p>
                  <ul>
                    {cascadeDeleteModelInfo.usedInSteps.map((step, index) => (
                      <li key={index}>
                        Step {step.soP2Step || step.sop2Step}
                        {step.isFrontendOnly && <span className="badge badge-info ml-2">未保存</span>}
                      </li>
                    ))}
                  </ul>
                  <p className="mb-0">
                    <strong>刪除此模型將會同時從以上所有 SOP 步驟中移除該模型。</strong>
                  </p>
                  <p className="mt-2 mb-0 text-info">
                    <i className="fas fa-info-circle"></i> 確認刪除後，請點擊頁面上的"儲存"按鈕以完成刪除操作。
                  </p>
                  {cascadeDeleteModelInfo.frontendUsage?.isUsed && (
                    <p className="mt-2 mb-0 text-info">
                      <i className="fas fa-info-circle"></i> 包含 {cascadeDeleteModelInfo.frontendUsage.usageCount} 個未保存的使用記錄，將立即清除。
                    </p>
                  )}
                  {cascadeDeleteModelInfo.backendUsage?.isUsed && (
                    <p className="mt-1 mb-0 text-warning">
                      <i className="fas fa-database"></i> 包含 {cascadeDeleteModelInfo.backendUsage.usageCount} 個已保存的使用記錄，將從數據庫中刪除。
                    </p>
                  )}
                </div>
              ) : (
                <div className="alert alert-info">
                  <p className="mb-0">此模型目前沒有被任何 SOP 步驟使用，可以安全刪除。</p>
                </div>
              )}

              <p>您確定要繼續刪除嗎？此操作無法復原。</p>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={() => setShowCascadeDeleteModal(false)}
            disabled={cascadeDeleteLoading}
          >
            取消
          </button>
          <button
            type="button"
            className={getButtonClassName("btn btn-danger")}
            onClick={handleButtonClick(handleConfirmCascadeDelete)}
            disabled={cascadeDeleteLoading}
          >
            {cascadeDeleteLoading ? (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  aria-hidden="true"
                />
                {' '}刪除中...
              </>
            ) : (
              '確認刪除'
            )}
          </button>
        </Modal.Footer>
      </Modal>

      {isSOPName && <SOPName onClose={() => setIsSOPName(false)} tempModels={tempModels} />}
      <ToastContainer />
    </>
  );
}
export default SOP2;

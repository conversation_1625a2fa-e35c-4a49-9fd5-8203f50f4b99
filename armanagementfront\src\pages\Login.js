﻿import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Form, InputGroup, Row, Col, Modal } from 'react-bootstrap';
import Spinner from 'react-bootstrap/Spinner';
import { setWindowClass } from '../utils/helpers';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import SimpleReactValidator from 'simple-react-validator';
import reactStringReplace from 'react-string-replace';
import {
  setAuthToken,
  checkValidator,
  removeAuthToken,
} from '../utils/TokenUtil'; // 確保這行正確導入
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';
import {
  apiSignIn,
  // 註釋Email註冊相關API
  // apiSendVerificationCode,
  // apiVerifyCode,
  // apiSignUp,
} from '../utils/Api';
import '../styles/Login.css';

function Login() {
  const navigate = useNavigate();
  const [isAuthLoading, setAuthLoading] = useState(false);
  const [account, setAccount] = useState(''); //帳號
  const paw = useRef(''); //密碼

  const [isLoggingIn, setIsLoggingIn] = useState(false);

  const [errors, setErrors] = useState({
    account: '',
    paw: '',
  });

  // 註釋Email註冊相關狀態，改用純User登入
  // const [showModal, setShowModal] = useState(false);
  // const [email, setEmail] = useState('');
  // const [password, setPassword] = useState('');
  // const [confirmPassword, setConfirmPassword] = useState('');
  // const [verificationCode, setVerificationCode] = useState('');
  // const [verificationCodeSent, setVerificationCodeSent] = useState(false);
  // const [verificationSuccess, setVerificationSuccess] = useState(false);
  // const [userName, setUserName] = useState('');

  // const handleShowModal = () => setShowModal(true);
  // const handleCloseModal = () => {
  //   setShowModal(false);
  //   setEmail('');
  //   setPassword('');
  //   setConfirmPassword('');
  //   setVerificationCode('');
  //   setVerificationCodeSent(false);
  //   setVerificationSuccess(false);
  //   setErrors({});
  // };

  // 註釋Email相關函數
  // function isValidEmail(email) {
  //   const re =
  //     /^[^\s@]+@(?:[^\s@]+\.)?(gmail\.com|yahoo\.com|hotmail\.com|outlook\.com|yahoo\.co\.uk|googlemail\.com|msn\.com|aol\.com|live\.com|icloud\.com)$/;
  //   return re.test(String(email).toLowerCase());
  // }

  // const handleEmailChange = (e) => {
  //   const newEmail = e.target.value;
  //   setEmail(newEmail);

  //   if (!isValidEmail(newEmail)) {
  //     setErrors((prevErrors) => ({
  //       ...prevErrors,
  //       email: '請輸入有效的電子郵件地址',
  //     }));
  //   } else {
  //     setErrors((prevErrors) => ({ ...prevErrors, email: '' }));
  //   }
  // };

  // const handleRegister = (e) => {
  // e.preventDefault();
  // const newErrors = {};

  // if (!email) {
  //   newErrors.email = '電子郵件不能為空';
  // } else if (!isValidEmail(email)) {
  //   newErrors.email = '請輸入有效的電子郵件地址';
  // }

  // if (!password) {
  //   newErrors.password = '密碼不能為空';
  // } else if (password.length < 6) {
  //   newErrors.password = '密碼至少需要6個字符';
  // }

  // if (password !== confirmPassword) {
  //   newErrors.confirmPassword = '輸入的密碼不一致';
  // }

  // if (!verificationCode) {
  //   newErrors.verificationCode = '驗證碼不能為空';
  // } else if (!verificationCodeSent) {
  //   newErrors.verificationCode = '驗證碼未發送或已過期';
  // }

  // // if (!verificationCode) {
  // //   newErrors.verificationCode = '驗證碼不能為空';
  // // } else {
  // //   // const verificationResult = await checkVerificationCode(email, verificationCode);
  // //   if (!verificationResult.isValid) {
  // //     newErrors.verificationCode = '驗證碼錯誤或已過期';
  // //   }
  // // }

  // if (Object.keys(newErrors).length > 0) {
  //   setErrors(newErrors);
  //   return;
  // }

  //   // 如果沒有錯誤，執行註冊邏輯
  //   console.log('Registering:', email, password);
  //   handleCloseModal();
  //   toast.success('註冊成功！');
  // };

  // const handleSendVerificationCode = () => {
  //   if (isValidEmail(email)) {
  //     setVerificationCodeSent(true);
  //     toast.success('驗證碼已發送到您的郵箱');
  //   } else {
  //     toast.error('請輸入有效的電子郵件地址');
  //   }
  // };
  // 註釋Email註冊相關函數
  // const handleRegister = async (e) => {
  //   e.preventDefault();
  //   if (!verificationSuccess) {
  //     toast.error('請先通過郵件驗證');
  //     return;
  //   }
  //   // 其他註冊邏輯
  //   const registrationData = {
  //     UserName: userName,
  //     UserAccount: email, // Assuming email as account
  //     UserPassword: password,
  //     Email: email,
  //     EmailVerified: true,
  //   };
  //   const result = await apiSignUp(registrationData);
  //   if (result.success) {
  //     handleCloseModal();
  //     toast.success('註冊成功！');
  //   } else {
  //     toast.error(result.message || '註冊失敗');
  //   }
  // };

  // const handleSendVerificationCode = async () => {
  //   if (isValidEmail(email)) {
  //     const result = await apiSendVerificationCode(email);
  //     if (result.success) {
  //       setVerificationCodeSent(true);
  //       toast.success('驗證碼已發送到您的郵箱');
  //     } else {
  //       toast.error('驗證碼發送失敗');
  //     }
  //   } else {
  //     toast.error('請輸入有效的電子郵件地址');
  //   }
  // };

  // const handleVerifyCode = async () => {
  //   const result = await apiVerifyCode(email, verificationCode);
  //   if (result.success) {
  //     setVerificationSuccess(true);
  //     toast.success('驗證碼正確，現在您可以完成註冊。');
  //   } else {
  //     setVerificationSuccess(false);
  //     toast.error('驗證碼錯誤，請重新輸入或重新獲取。');
  //   }
  // };

  const validator = new SimpleReactValidator({
    validators: {
      pawFormat: {
        rule: (val, params, validator) => {
          let result = false;

          if (val.length < 6 || val.length > 30) {
            return result;
          }

          let strRep = reactStringReplace(val, /(\d+)/g, (match, i) => '');
          strRep = reactStringReplace(strRep, /([a-zA-Z])/g, (match, i) => '');
          strRep = reactStringReplace(strRep, /([-)(*#+])/g, (match, i) => '');

          if (strRep.join('') == '') {
            result = true;
          }

          return result;
        },
      },
    },
    autoForceUpdate: this,
  });

  //#region 初始載入
  //useEffect(() => {
  //    paw.current.value = "123456";
  //}, []);
  //#endregion

  //#region 改變Input的欄位
  const handleChange = (e) => {
    const { name, value } = e.target;
    setAccount(value);
  };
  //#endregion

  //#region 失去焦點Input的欄位
  const handleBlur = async (e) => {
    const { name, value } = e.target;

    await checkValidator(name);
  };
  //#endregion

  //#region 欄位驗證
  const checkValidator = async (name = '', val = '') => {
    let result = true;

    if (name == 'account' || name == '') {
      if (!validator.check(account, 'required')) {
        toast.error('帳號不得空白', {
          autoClose: 4000,
          hideProgressBar: false
        });
        result = false;
      }
    }

    if (name == 'password' || name == '') {
      var tempPaw = paw.current.value;

      if (!validator.check(tempPaw, 'required')) {
        toast.error('密碼不得空白', {
          autoClose: 4000,
          hideProgressBar: false
        });
        result = false;
      } else if (!validator.check(tempPaw, 'pawFormat')) {
        toast.error('密碼格式有誤', {
          autoClose: 4000,
          hideProgressBar: false
        });
        result = false;
      }
    }

    return result;
  };
  //#endregion

  //#region 登入
  // const handleSubmit = async (e) => {
  //   e.preventDefault();
  //   if (await checkValidator()) {
  //     setAuthLoading(true); // 啟動轉圈圈效果

  //     var sendData = {
  //       account: account,
  //       paw: paw.current.value,
  //     };

  //     try {
  //       let signInResponse = await apiSignIn(sendData);
  //       if (signInResponse && signInResponse.code == '0000') {
  //         // 添加延遲效果
  //         setTimeout(() => {
  //           setAuthToken(signInResponse.result);
  //           toast.success('登入成功！');
  //           // 添加延遲效果，導向指定頁面
  //           setTimeout(() => {
  //             navigate('machine');
  //             setAuthLoading(false); // 登入成功後停止轉圈圈
  //           }, 1000); // 1秒後導向 machine 頁面
  //         }, 500); // 0.5秒後顯示登入成功消息
  //       } else {
  //         setTimeout(() => {
  //           toast.error(signInResponse.message, {
  //             position: toast.POSITION.TOP_CENTER,
  //             autoClose: 5000,
  //             hideProgressBar: true,
  //             closeOnClick: false,
  //             pauseOnHover: false,
  //           });
  //           setAuthLoading(false); // 登入失敗停止轉圈圈
  //         }, 1000); // 延遲 1 秒後顯示錯誤消息
  //       }
  //     } catch (error) {
  //       console.error('Login error:', error);
  //       setTimeout(() => {
  //         toast.error('登入請求失敗: ' + error.message);
  //         setAuthLoading(false); // 登入錯誤停止轉圈圈
  //       }, 1000); // 延遲 1 秒後顯示錯誤消息
  //     }
  //   }
  // };
  //#endregion

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (await checkValidator()) {
      setAuthLoading(true); // 啟動轉圈圈效果

      var sendData = {
        account: account,
        paw: paw.current.value,
      };

      try {
        // 从localStorage获取schemaName
        const schemaName = localStorage.getItem('schema_name');

        let signInResponse = await apiSignIn(sendData, schemaName); // 将schemaName传递给apiSignIn

        // 防護性檢查：確保 signInResponse 存在且有效
        if (signInResponse && signInResponse.code == '0000') {
          // 添加延遲效果
          setTimeout(() => {
            setAuthToken(signInResponse.result);
            toast.success('登入成功！', {
              autoClose: 4000,
              hideProgressBar: false
            });
            // 添加延遲效果，導向指定頁面
            setTimeout(() => {
              navigate('/machineKnowledge');
              setAuthLoading(false); // 登入成功後停止轉圈圈
            }, 1000); // 1秒後導向 machine 頁面
          }, 500); // 0.5秒後顯示登入成功消息
        } else {
          setTimeout(() => {
            // 防護性檢查：確保 signInResponse 和 message 存在
            const errorMessage = signInResponse?.message || '登入失敗，請檢查帳號密碼是否正確';
            toast.error(errorMessage, {
              position: toast.POSITION.TOP_CENTER,
              autoClose: 5000,
              hideProgressBar: true,
              closeOnClick: false,
              pauseOnHover: false,
            });
            setAuthLoading(false); // 登入失敗停止轉圈圈
          }, 1000); // 延遲 1 秒後顯示錯誤消息
        }
      } catch (error) {
        console.error('Login error:', error);
        setTimeout(() => {
          // 防護性檢查：確保 error 和 message 存在
          const errorMessage = error?.message || '登入請求失敗，請檢查網路連線';
          toast.error('登入請求失敗: ' + errorMessage);
          setAuthLoading(false); // 登入錯誤停止轉圈圈
        }, 1000); // 延遲 1 秒後顯示錯誤消息
      }
    }
  };

  setWindowClass('login-page');

  return (
    <>
      <div className="login-box">
        <div className="card hover-lift fade-in">
          <div className="card-header text-center">
            <h1 style={{ fontSize: '50px' }}>生成式AR售服平台</h1>
          </div>
          <div className="card-body">
            <p className="login-box-msg fade-in">登入</p>
            <form onSubmit={handleSubmit} className="fade-in">
              <div className="custom-input-group">
                <div className="input-focus-animation">
                  <Form.Control
                    id="account"
                    name="account"
                    type="text"
                    placeholder="帳號"
                    onChange={handleChange}
                    onFocus={(e) => e.target.parentElement.classList.add('focused')}
                    onBlur={(e) => {
                      handleBlur(e);
                      if (!e.target.value) {
                        e.target.parentElement.classList.remove('focused');
                      }
                    }}
                    value={account}
                    className="hover-lift"
                  />
                  <div className="input-group-append">
                    <div className="input-group-text">
                      <i className="fas fa-user" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="custom-input-group">
                <div className="input-focus-animation">
                  <Form.Control
                    id="password"
                    name="password"
                    type="password"
                    placeholder="密碼"
                    onFocus={(e) => e.target.parentElement.classList.add('focused')}
                    onBlur={(e) => {
                      handleBlur(e);
                      if (!e.target.value) {
                        e.target.parentElement.classList.remove('focused');
                      }
                    }}
                    ref={paw}
                    className="hover-lift"
                  />
                  <div className="input-group-append">
                    <div className="input-group-text">
                      <i className="fas fa-lock" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="d-flex justify-content-end">
                <Button 
                  type="submit" 
                  disabled={isAuthLoading}
                  className="custom-login-btn hover-lift"
                >
                  {isAuthLoading ? (
                    <>
                      <Spinner
                        as="span"
                        animation="border"
                        size="sm"
                        role="status"
                        aria-hidden="true"
                        className="custom-spinner"
                      />
                      <span className="ms-2">登入中...</span>
                    </>
                  ) : (
                    <span>登入</span>
                  )}
                </Button>
              </div>
            </form>
            <Row className="mt-3">
              <Col className="text-center">
                {/* <Button
                  variant="link"
                  onClick={handleShowModal}
                  style={{ textDecoration: 'none' }}
                >
                  創建Mail帳號 (Click Me)
                </Button> */}
              </Col>
            </Row>
          </div>
        </div>
      </div>

      <ToastContainer 
        position="top-right"
        autoClose={4000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss={false}
        draggable
        pauseOnHover={false}
        theme="light"
        style={{ 
          top: '20px', 
          right: '20px',
          zIndex: 9999
        }}
      />

      {/* 註釋Email註冊Modal組件 */}
      {/* <Modal show={showModal} onHide={handleCloseModal} backdrop="static">
        <Modal.Header closeButton>
          <Modal.Title>註冊帳號</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleRegister}>
            <Form.Group controlId="formBasicUserName">
              <Form.Label>
                <span className="text-danger">*</span>用戶名
              </Form.Label>
              <Form.Control
                type="text"
                placeholder="輸入用戶名"
                required
                onChange={(e) => setUserName(e.target.value)}
                isInvalid={!!errors.userName}
              />
              <Form.Control.Feedback type="invalid">
                {errors.userName}
              </Form.Control.Feedback>
            </Form.Group>
            <Form.Group controlId="formBasicEmail">
              <Form.Label>
                <span className="text-danger">*</span>電子郵件
              </Form.Label>
              <Form.Control
                type="email"
                placeholder="輸入電子郵件"
                required
                onChange={handleEmailChange}
                isInvalid={!!errors.email}
              />
              <Form.Control.Feedback type="invalid">
                {errors.email}
              </Form.Control.Feedback>
            </Form.Group>
            <Row className="mb-3">
              <Col xs="auto">
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={handleSendVerificationCode}
                  disabled={!isValidEmail(email)}
                >
                  發送驗證碼
                </Button>
              </Col>
              <Col xs="auto">
                <Button
                  variant="outline-primary"
                  size="sm"
                  onClick={handleVerifyCode}
                  disabled={!verificationCode}
                >
                  確認驗證碼
                </Button>
              </Col>
            </Row>
            <Form.Group controlId="formVerificationCode">
              <Form.Label>
                <span className="text-danger">*</span>驗證碼
              </Form.Label>
              <InputGroup className="mb-3">
                <Form.Control
                  type="text"
                  placeholder="輸入驗證碼"
                  required
                  onChange={(e) => setVerificationCode(e.target.value)}
                  isInvalid={!!errors.verificationCode}
                />
              </InputGroup>
              <Form.Control.Feedback type="invalid">
                {errors.verificationCode}
              </Form.Control.Feedback>
              {verificationSuccess && (
                <div className="text-success">驗證成功！</div>
              )}
            </Form.Group>
            <Form.Group controlId="formBasicPassword">
              <Form.Label>
                <span className="text-danger">*</span>密碼
              </Form.Label>
              <Form.Control
                type="password"
                placeholder="密碼"
                required
                onChange={(e) => setPassword(e.target.value)}
                isInvalid={!!errors.password}
              />
              <Form.Control.Feedback type="invalid">
                {errors.password}
              </Form.Control.Feedback>
            </Form.Group>
            <Form.Group controlId="formBasicConfirmPassword">
              <Form.Label>
                <span className="text-danger">*</span>再次輸入密碼
              </Form.Label>
              <Form.Control
                type="password"
                placeholder="再次輸入密碼"
                required
                onChange={(e) => setConfirmPassword(e.target.value)}
                isInvalid={!!errors.confirmPassword}
              />
              <Form.Control.Feedback type="invalid">
                {errors.confirmPassword}
              </Form.Control.Feedback>
            </Form.Group>
            <Button variant="primary" type="submit">
              註冊
            </Button>
          </Form>
        </Modal.Body>
      </Modal> */}
    </>
  );
}

export default Login;
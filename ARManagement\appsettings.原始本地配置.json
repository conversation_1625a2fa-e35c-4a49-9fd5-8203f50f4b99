{"說明": "這是原始本地運行配置，需要時請將內容複製到 appsettings.json", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "DBConfig": {"ConnectionString": "Server=*************;Port=10003;User Id=postgres;Password=*********;Database=AREditor;"}, "FileStorageSettings": {"UploadsFolder": "C:\\Users\\<USER>\\Desktop\\PDFSaveTest"}, "JwtSettings": {"Issuer": "UNTaiwanensis", "SignKey": "U@N#Taiwanensis$@&^", "LifeHour": 8760}, "SMTP": {"Server": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "{SMTP_PASSWORD}", "EnableSSL": true, "SenderEmail": "<EMAIL>"}}
# AR設備與知識管理系統開發文檔

前置說明：
作為一名資深前端開發專家，我參與開發了這個基於React的企業級AR設備與知識管理系統。本系統採用最新的前端技術架構，整合了AR設備控制、知識庫管理、用戶權限等多個核心功能模組。在開發過程中，我們特別注重系統的可擴展性和用戶體驗，通過模組化設計確保了系統的穩定性和可維護性。

項目簡介：
本系統是一個綜合性的AR設備和知識管理平台，基於React 18.x、Zustand狀態管理和TailwindCSS前端框架開發。系統實現了完整的機台管理、知識庫管理、用戶權限控制等功能，並整合了GPT智能助手系統。在技術選型上，我們採用了最新的React Hooks API，配合Material-UI組件庫，實現了豐富的交互功能和響應式設計。系統支援多媒體內容管理、PDF文檔生成、多語言國際化等特性，為用戶提供了全方位的解決方案。

技術架構：
核心框架：React 18.x、React Router v6、Zustand狀態管理、TailwindCSS
UI組件庫：Material-UI (MUI)、React Icons、Ant Design
功能類庫：Axios、jsPDF、html2canvas、react-dropzone、moment.js、i18next、react-query

數據模型定義：
1. MachineKnowledge - 機台管理數據結構
   - machineKnowledgeId: 機台識別碼
   - machineType: 機台種類
   - modelSeries: 型號系列
   - machineName: 機台名稱
   - machineImage: 機台圖片
   - isDeletedMachineImage: 圖片刪除標記

2. DocumentEditor - 文檔編輯數據結構
   - 設備資訊：設備種類、設備部件、維修項目、維修類型
   - 故障資訊：故障發生原因、故障描述、故障發生時機
   - 圖片資訊：For Model機型、使用工具、部位位置

3. SOP2 - 文檔編輯數據結構
   - SOP資訊：步驟說明、備註說明、步驟圖片、備註圖片、3D Model List、3D Model

目錄結構與功能說明：

src/components/ 元件目錄：

DocumentEditor.js、SOP2.js - 文檔編輯器主元件，支援富文本編輯、圖片上傳和管理、文檔格式化設置
PDFDocument.js - PDF文檔生成元件，支援PDF轉換與生成、多頁面處理、自定義頁面布局
PDFContent.js - PDF內容渲染元件，負責PDF內容展示和格式轉換
PreviewPDFContent.js - PDF預覽元件，提供即時預覽功能、支援縮放和頁面切換
PDFBackUp.js - PDF備份功能元件，處理文檔備份管理和版本控制
SOPName.js - 提交DocumentEditor.js、SOP2.js兩個檔案數據給後端API運行保存、支援SOP命名和管理元件，負責SOP文檔命名管理、版本控制和權限設置
AddingKnowledge.js - 新增知識元件，處理知識條目創建、分類管理和標籤系統
RepairDocument.js - 維修文檔元件，管理維修記錄、故障診斷文檔和維修指南
MindMap.js - 思維導圖元件，實現知識結構可視化和互動式節點管理
ContentHeader.js - 內容頭部元件，處理頁面標題和導航麵包屑
VendorsAccount.js - 供應商帳戶管理，處理供應商資訊和帳戶權限
Assistant.js - 輔助功能元件，提供用戶操作引導和提示信息

src/pages/ 頁面目錄：

SOP.js / SOP2.js - SOP管理頁面，處理SOP文檔創建和編輯、流程管理、版本控制
Knowledge.js - 知識庫主頁面，管理知識庫內容、搜索和過濾功能
Database.js - 數據庫管理頁面，處理數據存儲、備份和同步
Machine.js - 機台管理主頁面，處理機台信息管理、設備狀態監控
MachineIOT.js - 機台IOT功能頁面，管理IOT設備連接和數據採集
MachineIOTList.js - 機台IOT列表頁面，展示設備清單和狀態概覽
MachineAlarm.js - 機台警報頁面，處理警報監控和記錄管理
MachineKnowledge.js - 機台知識庫頁面，管理機台相關知識和技術文檔
Login.js - 登入頁面，處理用戶認證和權限驗證
UserManage.js - 用戶管理頁面，處理用戶帳戶和權限分配
GPT.js - GPT功能頁面，提供AI對話和智能輔助
Alarm.js - 警報系統頁面，管理系統警報和通知設置
Nav.js - 導航組件，處理系統導航和選單控制
Header.js - 頁頭組件，管理頁面頭部和用戶信息展示

功能流程說明：

1. 知識庫操作流程：
   - 新增知識：填寫基本信息 → 故障說明 → SOP內容 → 預覽和保存
   - 文檔管理：故障說明填寫 → SOP步驟編輯 → 圖片上傳管理 → PDF生成導出
   - 心智圖管理：機台分類展示 → 知識結構可視化 → 互動式節點管理

2. 用戶權限管理：
   最高管理者：系統所有功能的操作權限
   專家：知識庫編輯、SOP管理等核心功能權限
   一般用戶：基本查看和使用權限

3. 錯誤處理機制：
   - 輸入驗證：必填欄位檢查、格式驗證
   - 操作確認：刪除確認、重要操作提示
   - 錯誤反饋：友好的錯誤提示、操作引導

開發規範：
命名規範：
- 元件使用大駝峰命名（如：DocumentEditor）
- 函數使用小駝峰命名（如：handleSubmit）
- 常量使用大寫底線（如：MAX_COUNT）

代碼規範：
- 使用ESLint進行代碼檢查
- 使用Prettier進行代碼格式化
- 遵循React Hooks規範

部署說明：
開發環境：npm install（安裝依賴）、npm start（啟動開發服務器）
生產環境：npm run build（構建生產版本）

維護說明：
1. 定期更新依賴包
2. 保持文檔的同步更新
3. 遵循Git提交規範
4. 保持代碼審查流程

版本控制：
- 使用Git進行版本控制
- 遵循語義化版本規範
- 保持清晰的提交信息
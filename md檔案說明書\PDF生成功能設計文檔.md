# PDF生成功能設計文檔

## 📋 概述

本文檔詳細說明了系統中PDF生成功能的設計架構、字數限制、自動分頁邏輯以及相關配置。主要涉及 `PDFDocument.js`、`SOP2.js` 等核心文件。

---

## 🏗️ 系統架構

### 核心文件結構
```
armanagementfront/src/
├── components/
│   ├── PDFDocument.js          # PDF渲染核心組件
│   ├── PDFBackUp.js           # PDF備份功能
│   └── RepairDocument.js      # PDF預覽與下載
├── pages/
│   ├── SOP2.js               # SOP編輯頁面（含字數限制）
│   └── Database.js           # 數據庫頁面（PDF生成入口）
└── utils/
    └── PDFUtils.js           # PDF工具函數
```

---

## 📝 字數限制系統

### SOP2.js 中的字數限制配置

#### 基本限制設定
```javascript
const CHARACTER_LIMITS = {
  soP2Message: 600,    // 步驟說明限制600字（極限測試方案）
  soP2Remark: 100      // 備註說明限制100字
};
```

#### 智能字數計算算法
```javascript
const calculateEffectiveLength = (text) => {
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
  const numbers = (text.match(/[0-9]/g) || []).length;
  const punctuation = (text.match(/[.,;:!?()[\]{}"'-]/g) || []).length;
  const spaces = (text.match(/\s/g) || []).length;
  
  return Math.ceil(
    chineseChars * 1.0 +        // 中文字符佔用100%空間
    englishChars * 0.6 +        // 英文字母佔用60%空間
    numbers * 0.5 +             // 數字佔用50%空間
    punctuation * 0.4 +         // 標點符號佔用40%空間
    spaces * 0.3 +              // 空格佔用30%空間
    others * 0.7                // 其他字符佔用70%空間
  );
};
```

#### 空間佔用計算
```javascript
const calculateSpaceUsage = (text, limit) => {
  const effectiveLength = calculateEffectiveLength(text);
  return Math.min(Math.round((effectiveLength / limit) * 100), 100);
};
```

---

## 📄 PDF自動分頁系統

### PDFDocument.js 中的分頁邏輯

#### 內容評分算法
```javascript
const checkStepContentLength = (illustrationText, remarkText, hasRemarkImage) => {
  const illLength = illustrationText?.length || 0;
  const remarkLength = remarkText?.length || 0;
  const totalLength = illLength + remarkLength;
  
  // 計算換行數量
  const illNewLines = (illustrationText?.match(/\n/g) || []).length;
  const remarkNewLines = (remarkText?.match(/\n/g) || []).length;
  
  // 圖片佔用空間評估
  const imageSpace = hasRemarkImage ? 150 : 0;
  
  // 綜合內容評分
  const contentScore = totalLength + (illNewLines + remarkNewLines) * 15 + imageSpace;
  
  return { contentScore, /* 其他屬性 */ };
};
```

#### 內容分類閾值
| 內容類型 | 評分範圍 | 容器高度 | 每頁Step數 | 說明 |
|---------|----------|----------|-----------|------|
| **短內容** | ≤250分 | 85mm | 最多3個 | 約100字以內 |
| **中等內容** | 251-350分 | 120mm | 最多2個 | 約150-250字 |
| **中長內容** | 351-600分 | 150mm | 最多2個 | 約250-450字 |
| **長內容** | 601-900分 | 180mm | 獨佔頁面 | 約500-650字 |
| **超長內容** | >900分 | 225mm | 全頁 | 約700字以上 |

#### 容器樣式定義
```javascript
const styles = StyleSheet.create({
  stepContent: {
    flexDirection: 'row',
    height: '85mm',     // 短內容容器
    marginTop: '0',
    marginBottom: '0',
    flex: 1,
  },
  
  stepContentExtended: {
    flexDirection: 'row',
    height: '120mm',    // 中等內容容器
    marginTop: '0',
    marginBottom: '0',
    flex: 1,
  },
  
  stepContentMediumLong: {
    flexDirection: 'row',
    height: '150mm',    // 中長內容容器
    marginTop: '0',
    marginBottom: '0',
    flex: 1,
  },
  
  stepContentLong: {
    flexDirection: 'row',
    height: '180mm',    // 長內容容器
    marginTop: '0',
    marginBottom: '0',
    flex: 1,
  },
  
  stepContentFullPage: {
    flexDirection: 'row',
    height: '225mm',    // 超長內容容器
    marginTop: '0',
    marginBottom: '0',
    flex: 1,
  },
});
```

---

## 🔄 分頁決策邏輯

### shouldStartNewPage 函數
```javascript
const shouldStartNewPage = (currentSteps, nextStep) => {
  if (currentSteps.length === 0) return false;
  
  const nextStepAnalysis = checkStepContentLength(
    nextStep.soP2Message, 
    nextStep.soP2Remark, 
    !!nextStep.soP2RemarkImage
  );

  // 計算當前頁面已有內容的總評分
  const currentPageScore = currentSteps.reduce((total, step) => {
    const stepAnalysis = checkStepContentLength(
      step.soP2Message, 
      step.soP2Remark, 
      !!step.soP2RemarkImage
    );
    return total + stepAnalysis.contentScore;
  }, 0);

  const totalScore = currentPageScore + nextStepAnalysis.contentScore;
  
  // 分頁決策規則
  if (nextStepAnalysis.needsFullPage && currentSteps.length > 0) {
    return true; // 超長內容必須開新頁
  }
  
  if (totalScore > 700) {
    return true; // 容量限制開新頁
  }
  
  if (nextStepAnalysis.isLongContent && currentSteps.length > 0) {
    return true; // 長內容獨佔頁面
  }
  
  if (nextStepAnalysis.isMediumLongContent && currentSteps.length >= 2) {
    return true; // 中長內容最多2個Step
  }
  
  if (nextStepAnalysis.isMediumContent && currentSteps.length >= 2) {
    return true; // 中等內容最多2個Step
  }
  
  if (nextStepAnalysis.isShortContent && currentSteps.length >= 3) {
    return true; // 短內容最多3個Step
  }

  return false;
};
```

---

## 📐 高度計算系統

### 動態高度增量
```javascript
// 根據Step類型決定高度增量
if (stepAnalysis.isLongContent) {
  currentHeight += 188; // 180mm step + 8mm header = 188mm
  // 長內容添加後立即結束當前頁面
} else if (stepAnalysis.isMediumLongContent) {
  currentHeight += 158; // 150mm step + 8mm header = 158mm
  // 中長內容最多2個Step一頁
} else if (stepAnalysis.isMediumContent) {
  currentHeight += 128; // 120mm step + 8mm header = 128mm
} else {
  currentHeight += 93;  // 85mm step + 8mm header = 93mm
}
```

### 頁面高度限制
```javascript
const maxPageHeight = 277; // 最大頁面高度 277mm
let currentHeight = 48;     // 初始高度 48mm（標題區域）
```

---

## 🎨 佈局模式系統

### 佈局模式映射
```javascript
const contentStyle = layoutMode === 'fullPage' 
  ? styles.stepContentFullPage 
  : layoutMode === 'long'
  ? styles.stepContentLong
  : layoutMode === 'mediumLong'
  ? styles.stepContentMediumLong
  : layoutMode === 'extended'
  ? styles.stepContentExtended
  : styles.stepContent;
```

### 佈局模式決策
```javascript
layoutMode: contentScore <= 250 ? 'short' : 
            contentScore <= 350 ? 'medium' :
            contentScore <= 600 ? 'mediumLong' :
            contentScore <= 900 ? 'long' : 'fullPage'
```

---

## 🔧 文字處理功能

### AutoWrapText 組件
```javascript
const AutoWrapText = ({ text, style }) => {
  const words = useMemo(() => {
    if (!text) return [];
    
    return text
      .split(/(?<=[\u4e00-\u9fa5])|(?=[\u4e00-\u9fa5])|(?<=\s)|(?=\s)|(?<=[@#$%^&*])|(?=[@#$%^&*])|(?<=\w{10})(?=\w)/g)
      .filter((word) => word.trim().length > 0);
  }, [text]);

  return (
    <View style={[styles.autoWrapContainer, style]}>
      {words.map((word, index) => {
        const isNumber = /^(\d+\.|[\(（]\d+[\)）])(?!\d+\.\d+)/.test(word);
        const hasMM = /(?:\d+\.?\d*|\(\d+\.?\d*\))(?:\s)?mm/.test(word);

        return (
          <Text
            key={index}
            style={[
              styles.wordSpan,
              isNumber || hasMM ? { color: 'red' } : null,
            ]}
          >
            {word}
          </Text>
        );
      })}
    </View>
  );
};
```

---

## 📊 調試與監控

### 調試信息輸出
```javascript
console.log(`Step分頁決策 - 當前Steps數量: ${currentSteps.length}, 當前頁評分: ${currentPageScore}, 下一Step分類: ${nextStepAnalysis.layoutMode}, 評分: ${nextStepAnalysis.contentScore}`);

console.log(`Content Analysis - Score: ${contentScore}, illLength: ${illLength}, remarkLength: ${remarkLength}, hasImage: ${hasRemarkImage}`);
```

### 分頁觸發日誌
- `觸發超長內容開新頁`
- `觸發容量限制開新頁 - 總評分: ${totalScore}`
- `觸發長內容開新頁（獨佔頁面）`
- `觸發中長內容開新頁（最多2個Step）`
- `觸發中等內容開新頁`
- `觸發短內容開新頁`

---

## 🚀 PDF生成流程

### 生成入口點
1. **Database.js** - `handleGeneratePdf()` 函數
2. **RepairDocument.js** - `handleDownloadPdf()` 函數

### 生成流程
```javascript
const pdfBlob = await pdf(
  <PDFDocument knowledgeInfo={knowledgeInfo} SOPData={SOPData} />
).toBlob();

const fileName = `${knowledgeInfo.knowledgeBaseFileNo || 'repair'}_document.pdf`;
const pdfFile = new File([pdfBlob], fileName, { type: 'application/pdf' });

await apiUploadAndBackupPdf(pdfFile);
```

---

## ⚙️ 配置參數

### 關鍵配置值
```javascript
// 字數限制
CHARACTER_LIMITS = {
  soP2Message: 600,  // 步驟說明
  soP2Remark: 100    // 備註說明
}

// 分頁閾值
CONTENT_THRESHOLDS = {
  short: 250,        // 短內容
  medium: 350,       // 中等內容
  mediumLong: 600,   // 中長內容
  long: 900,         // 長內容
  fullPage: 900+     // 超長內容
}

// 容器高度
CONTAINER_HEIGHTS = {
  short: '85mm',
  medium: '120mm',
  mediumLong: '150mm',
  long: '180mm',
  fullPage: '225mm'
}

// 頁面限制
PAGE_LIMITS = {
  maxHeight: 277,    // 最大頁面高度
  headerHeight: 48,  // 標題區域高度
  stepHeader: 8      // Step標題高度
}
```

---

## 🔍 故障排除

### 常見問題與解決方案

#### 1. 文字溢出問題
- **症狀**：Step內容溢出到下一個Step區域
- **原因**：內容評分計算不準確或容器高度不足
- **解決**：調整 `checkStepContentLength` 函數的評分算法

#### 2. 分頁不當問題
- **症狀**：過多或過少的Step在同一頁
- **原因**：`shouldStartNewPage` 邏輯需要調整
- **解決**：修改分頁決策閾值

#### 3. 容器高度不足
- **症狀**：內容被截斷
- **原因**：容器樣式高度設定過小
- **解決**：增加對應佈局模式的容器高度

---

## 📈 性能優化

### useMemo 優化
```javascript
const paginatedSOPData = useMemo(() => {
  // 分頁邏輯
}, [SOPData]);

const words = useMemo(() => {
  // 文字分割邏輯
}, [text]);
```

### 延遲加載
```javascript
await Promise.all([
  pdf(<PDFDocument />).toBlob(),
  new Promise(resolve => setTimeout(resolve, 2000))
]);
```

---

## 📝 維護注意事項

1. **修改閾值時**：需要同時更新 `checkStepContentLength` 和 `shouldStartNewPage` 函數
2. **新增容器類型**：需要添加對應的樣式定義和佈局邏輯
3. **調試模式**：保留 console.log 輸出以便問題追蹤
4. **測試覆蓋**：確保各種內容長度組合都能正確分頁
5. **向下兼容**：修改時保持對現有數據的兼容性

---

## 📚 相關文件

- `armanagementfront/src/components/PDFDocument.js` - PDF渲染核心
- `armanagementfront/src/pages/SOP2.js` - 字數限制與編輯
- `armanagementfront/src/utils/PDFUtils.js` - PDF工具函數
- `armanagementfront/src/components/PDFBackUp.js` - PDF備份功能
- `armanagementfront/src/pages/Database.js` - PDF生成入口
- `armanagementfront/src/components/RepairDocument.js` - PDF預覽下載

---

*最後更新：2025-08-08*
*版本：v2.0 - 支援中長內容分類與智能分頁*

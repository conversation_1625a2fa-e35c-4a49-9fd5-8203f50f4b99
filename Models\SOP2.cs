﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace Models
{
    /// <summary>
    /// 取得所有SOP資料
    /// </summary>
    [BindNever]
    public class PostAllSOP2 : PostId
    {
        public int? KnowledgeBaseId { get; set; } // 知識庫ID，用於過濾特定知識庫的SOP
        public short IsCommon { get; set; } //0:取得網頁的；1:取得眼鏡的
    }

    /// <summary>
    /// 取得共用模型
    /// </summary>
    [BindNever]
    public class PostCommonModels
    {
        public int MachineAddId { get; set; } //機台流水號
        public int KnowledgeBaseId { get; set; } //知識庫ID
        public short IsCommon { get; set; } //0:取得網頁的；1:取得眼鏡的
    }

    /// <summary>
    /// 檢查模型使用情況請求
    /// </summary>
    [BindNever]
    public class CheckModelUsageRequest
    {
        public int MachineAddId { get; set; } //機台流水號
        public int KnowledgeBaseId { get; set; } //知識庫ID
        public string ModelImageName { get; set; } = string.Empty; //模型圖片檔名
        public string ModelFileName { get; set; } = string.Empty; //模型檔案檔名
    }

    /// <summary>
    /// 模型使用情況信息
    /// </summary>
    public class ModelUsageInfo
    {
        public string ModelImageName { get; set; } = string.Empty;
        public string ModelFileName { get; set; } = string.Empty;
        public bool IsUsed { get; set; } = false;
        public int UsageCount { get; set; } = 0;
        public List<SOPStepInfo> UsedInSteps { get; set; } = new List<SOPStepInfo>();
    }

    /// <summary>
    /// SOP步驟信息
    /// </summary>
    public class SOPStepInfo
    {
        public int SOP2Id { get; set; }
        public int SOP2Step { get; set; }
        public string SOP2Name { get; set; } = string.Empty;
        public int SOPModelId { get; set; }
    }

    /// <summary>
    /// 級聯刪除模型請求
    /// </summary>
    [BindNever]
    public class CascadeDeleteModelRequest
    {
        public int MachineAddId { get; set; } //機台流水號
        public int KnowledgeBaseId { get; set; } //知識庫ID
        public string ModelImageName { get; set; } = string.Empty; //模型圖片檔名
        public string ModelFileName { get; set; } = string.Empty; //模型檔案檔名
    }

    /// <summary>
    /// 級聯刪除結果
    /// </summary>
    public class CascadeDeleteResult
    {
        public string ModelImageName { get; set; } = string.Empty;
        public string ModelFileName { get; set; } = string.Empty;
        public int DeletedCommonModelId { get; set; } = 0;
        public List<int> DeletedSOPModelIds { get; set; } = new List<int>();
        public int TotalDeletedCount { get; set; } = 0;
        public int AffectedSOPCount { get; set; } = 0;
    }



    /// <summary>
    /// 故障流程
    /// </summary>
    public class SOP2
    {
        public int SOP2Id { get; set; } // 流程流水號
        public short Deleted { get; set; } // 是否刪除
        public int SOP2Step { get; set; } // 流程步驟
        public string SOP2Message { get; set; } // 步驟說明
        public string SOP2Image { get; set; } = string.Empty; //步驟圖片
        [NotMapped]
        public IFormFile? SOP2ImageObj { get; set; } // 步驟圖片檔案
        public string? SOPVideo { get; set; } = string.Empty; //步驟檔案
        [NotMapped]
        public IFormFile? SOPVideoObj { get; set; } // 步驟檔案
        public string SOP2Remark { get; set; } // 備註補充
        public string SOP2RemarkImage { get; set; } = string.Empty; //備註圖片
        [NotMapped]
        public IFormFile? SOP2RemarkImageObj { get; set; } // 備註圖片檔案
        public string SOP2Name { get; set; } // SOP名稱
        public string? PLC1 { get; set; } = string.Empty;
        public string? PLC2 { get; set; } = string.Empty;
        public string? PLC3 { get; set; } = string.Empty;
        public string? PLC4 { get; set; } = string.Empty;
        public int MachineAddId { get; set; } //機台流水號
        public int KnowledgeBaseId { get; set; } // 知識庫ID
        public string T3DModels { get; set; } = "[]"; // 3D模型ID字串
        [NotMapped]
        public List<SOP2ModelDTO>? SOPModels { get; set; } // 3D 模型清單

    }

    /// <summary>
    /// 儲存故障流程
    /// </summary>
    public class PostSaveSOP2
    {
        public int MachineAddId { get; set; }
        public int KnowledgeBaseId { get; set; } // 知識庫ID
        public List<PostSOP2>? SOP2s { get; set; }
        public List<PostTempModel>? TempModels { get; set; } // 3D Model List 的模型庫
    }

    /// <summary>
    /// 儲存單一故障流程
    /// </summary>
    public class PostSOP2
    {
        public int SOP2Id { get; set; } // 流程流水號
        public short? Deleted { get; set; } // 是否刪除
        public int SOP2Step { get; set; } // 流程步驟
        public string? SOP2Message { get; set; } = string.Empty; // 步驟說明
        public string? SOP2Image { get; set; } = string.Empty; //步驟圖片
        [NotMapped]
        public IFormFile? SOP2ImageObj { get; set; } // 步驟圖片檔案
        public bool IsDeletedSOP2Image { get; set; } = false; //是否刪除步驟圖片
        public string? SOPVideo { get; set; } = string.Empty; //步驟檔案
        [NotMapped]
        public IFormFile? SOPVideoObj { get; set; } // 步驟檔案
        public bool IsDeletedSOPVideo { get; set; } = false; //是否刪除步驟檔案
        public string? SOP2Remark { get; set; } = string.Empty; // 備註補充
        public string? SOP2RemarkImage { get; set; } = string.Empty; //備註圖片
        [NotMapped]
        public IFormFile? SOP2RemarkImageObj { get; set; } // 備註圖片檔案
        public bool IsDeletedSOP2RemarkImage { get; set; } = false; //是否刪除備註圖片
        public string? SOP2Name { get; set; } = string.Empty; // SOP名稱
        public string? PLC1 { get; set; } = string.Empty;
        public string? PLC2 { get; set; } = string.Empty;
        public string? PLC3 { get; set; } = string.Empty;
        public string? PLC4 { get; set; } = string.Empty;
        public int MachineAddId { get; set; }
        public int KnowledgeBaseId { get; set; } // 知識庫ID
        public string T3DModels { get; set; } = "[]"; // 3D模型ID字串
        public List<PostSOP2Model>? SOPModels { get; set; } // 3D Model 清單
    }

    /// <summary>
    /// 儲存 SOP2 3D 模型
    /// </summary>
    public class PostSOP2Model
    {
        public int SOPModelId { get; set; } = 0; // 模型流水號
        public short? Deleted { get; set; } = 0; // 是否刪除
        public string? SOPModelImage { get; set; } = string.Empty; // 模型圖片路徑
        [NotMapped]
        public IFormFile? SOPModelImageObj { get; set; } // 模型圖片檔案
        public string? SOPModelFile { get; set; } = string.Empty; // 模型檔案路徑
        [NotMapped]
        public IFormFile? SOPModelFileObj { get; set; } // 模型檔案
        public double SOPModelPX { get; set; } = 0.0; // X軸位置
        public double SOPModelPY { get; set; } = 0.0; // Y軸位置
        public double SOPModelPZ { get; set; } = 0.0; // Z軸位置
        public double SOPModelRX { get; set; } = 0.0; // X軸旋轉
        public double SOPModelRY { get; set; } = 0.0; // Y軸旋轉
        public double SOPModelRZ { get; set; } = 0.0; // Z軸旋轉
        public double SOPModelSX { get; set; } = 1.0; // X軸縮放
        public double SOPModelSY { get; set; } = 1.0; // Y軸縮放
        public double SOPModelSZ { get; set; } = 1.0; // Z軸縮放
        public bool IsDeletedSOPModelImage { get; set; } = false; // 是否刪除模型圖片
        public bool IsDeletedSOPModelFile { get; set; } = false; // 是否刪除模型檔案
    }

    public class SOP2ModelDTO
    {
        public int SOPModelId { get; set; }
        public short Deleted { get; set; } = 0; // 添加 Deleted 欄位
        public string SOPModelImage { get; set; } = string.Empty;
        public string SOPModelFile { get; set; } = string.Empty;
        public double SOPModelPX { get; set; }
        public double SOPModelPY { get; set; }
        public double SOPModelPZ { get; set; }
        public double SOPModelRX { get; set; }
        public double SOPModelRY { get; set; }
        public double SOPModelRZ { get; set; }
        public double SOPModelSX { get; set; }
        public double SOPModelSY { get; set; }
        public double SOPModelSZ { get; set; }
    }

    /// <summary>
    /// 儲存 TempModel (3D Model List 的模型庫)
    /// </summary>
    public class PostTempModel
    {
        public string? TempModelImageName { get; set; } = string.Empty; // 模型圖片名稱
        [NotMapped]
        public IFormFile? TempModelImageObj { get; set; } // 模型圖片檔案
        public string? TempModelImageUrl { get; set; } = string.Empty; // 模型圖片 URL
        public string? TempModelFileName { get; set; } = string.Empty; // 模型檔案名稱
        [NotMapped]
        public IFormFile? TempModelFileObj { get; set; } // 模型檔案
    }
}

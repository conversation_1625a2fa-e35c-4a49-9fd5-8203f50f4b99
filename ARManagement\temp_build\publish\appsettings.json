//{
//  "Logging": {
//    "LogLevel": {
//      "Default": "Information",
//      "Microsoft.AspNetCore": "Warning"
//    }
//  },
//  "AllowedHosts": "*",
//  "ConnectionStrings": {
//    "DefaultConnection": "Server=areditor.c7oa6eyws6yf.us-east-1.rds.amazonaws.com;Port=5432;User Id=postgres;Password=*********;Database=AREditor;"
//  },
//  "DBConfig": {
//    "ConnectionString": "Server=areditor.c7oa6eyws6yf.us-east-1.rds.amazonaws.com;Port=5432;User Id=postgres;Password=*********;Database=AREditor;"
//  },
//  "Kestrel": {
//    "EndPoints": {
//      "Http": {
//        "Url": "http://localhost:5052"
//      }
//    }
//  },
//  "JwtSettings": {
//    "Issuer": "UNTaiwanensis",
//    "SignKey": "U@N#Taiwanensis$@&^",
//    "LifeHour": 87600
//  }
//}

{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  
  // ========== 本地開發環境配置 (本地運行時，解開以下註釋) ==========
  // "DBConfig": {
  //   "ConnectionString": "Server=127.0.0.1;Port=10003;User Id=postgres;Password=*********;Database=AREditor;"
  // },
  
  // ========== 便攜式部署配置 (便攜式運行時使用以下配置) ==========
  "DBConfig": {
    "ConnectionString": "Server=127.0.0.1;Port=5433;User Id=postgres;Password=*********;Database=AREditor;"
  },
  
  "JwtSettings": {
    "Issuer": "UNTaiwanensis",
    "SignKey": "U@N#Taiwanensis$@&^",
    "LifeHour": 8760
  },
  "SMTP": {
    "Server": "smtp.gmail.com",
    "Port": 587,
    "Username": "<EMAIL>",
    "Password": "{SMTP_PASSWORD}",
    "EnableSSL": true,
    "SenderEmail": "<EMAIL>"
  },
  
  // ========== 檔案儲存路徑配置 ==========
  // 本地開發環境路徑 (本地運行時，解開以下註釋)
  // "FileStorageSettings": {
  //   "UploadsFolder": "C:\\Users\\<USER>\\Desktop\\PDFSaveTest"
  // },
  
  // 便攜式部署路徑 (便攜式運行時使用以下配置)
  "FileStorageSettings": {
    "UploadsFolder": "./uploads"
  }
}

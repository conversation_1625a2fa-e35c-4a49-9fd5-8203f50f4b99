# AR 管理系統 - 便攜式部署包

## 🚀 快速部署

### 1. 系統需求
- Windows 10/11
- PostgreSQL（任何版本）
- 4GB RAM

### 2. 部署步驟
1. **安裝 PostgreSQL**
2. **創建資料庫**：`AREditor`（或您自訂的名稱）
3. **執行初始化**：在 PostgreSQL Query Tool 中執行 `init.sql`
4. **雙擊啟動**：`start.bat`
5. **訪問系統**：瀏覽器自動開啟 `http://localhost:8098`

## ⚙️ 當前配置

### 系統設定
- **後端 API**: `http://localhost:8098`
- **前端呼叫**: `http://127.0.0.1:8098`
- **資料庫**: `PostgreSQL on 127.0.0.1:5432`
- **資料庫名**: `AREditor`
- **用戶/密碼**: `postgres/paw123456`

## 🔧 配置修改指南

### 需要修改連線或端口時，只需修改2個檔案：

#### 檔案1：後端配置
**位置：** `ARManagement/appsettings.json`

```json
{
  "Kestrel": {
    "EndPoints": {
      "Http": {
        "Url": "http://localhost:您的端口"
      }
    }
  },
  "DBConfig": {
    "ConnectionString": "Server=您的DB主機;Port=您的DB端口;User Id=用戶名;Password=密碼;Database=資料庫名;"
  }
}
```

#### 檔案2：前端配置
**位置：** `ARManagement/wwwroot/appsetting.js`

```javascript
var apiUrl = "http://127.0.0.1:您的端口";    // 需與後端端口一致
var domain = "localhost";
```

### 修改範例

#### 改端口為8080：
```json
// appsettings.json
"Url": "http://localhost:8080"
```
```javascript
// appsetting.js
var apiUrl = "http://127.0.0.1:8080";
```

#### 改資料庫主機：
```json
// appsettings.json
"ConnectionString": "Server=*************;Port=5433;User Id=myuser;Password=******;Database=MyDB;"
```

## 📝 修改後啟動步驟

1. **儲存檔案**（確保JSON格式正確）
2. **關閉現有服務**（如果正在運行）
3. **雙擊 start.bat**
4. **驗證**：檢查控制台和瀏覽器

## 🗂️ 檔案結構

```
部署包/
├── start.bat                    # 🔥 啟動腳本
├── stop.bat                     # 停止腳本
├── init.sql                     # 資料庫初始化
├── README.md                    # 此說明
└── ARManagement/                # 後端程式
    ├── ARManagement.exe         # 主程式
    ├── appsettings.json         # 🔧 後端配置
    └── wwwroot/
        └── appsetting.js        # 🔧 前端配置
```

## ⚠️ 注意事項

- **JSON格式**：修改 appsettings.json 時確保語法正確
- **端口一致**：前後端端口設定必須一致
- **無需重新打包**：直接修改配置檔案即可
- **管理員權限**：建議以管理員身份執行 start.bat

## 🚨 故障排除

| 問題 | 解決方法 |
|------|----------|
| PostgreSQL連線失敗 | 檢查資料庫服務、連線字串、防火牆 |
| 端口被佔用 | 修改 appsettings.json 中的端口 |
| 前端無法載入 | 檢查 appsetting.js 中的 API URL |
| 權限問題 | 以管理員身份運行 start.bat |

---
**版本：** v1.1 - 支援彈性配置修改  
**更新：** 包含 SPA 路由修復 
main {
  h2 {
    text-align: center;
    color: #1d6faa;
    font-size: 35px;
    padding: 20px;
    font-weight: 700;
    margin-bottom: 30px;
  }
}

label {
  margin-bottom: 0 !important;
  font-weight: 400 !important;
}

.back-page {
  position: absolute; /* 使用絕對定位 */
  top: 80px; /* 根據需要上移，這只是一個例子，您可以根據需要調整 */
  padding-left: 20px; /* 確保它與左側框框的左側對齊 */
  display: flex;
  align-items: center;
  font-weight: 500;
  cursor: pointer;
}

.back-page a {
  font-size: 17px;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

.sidebar {
  width: 100%;
  height: 100vh;
  flex: 1; /* 根據需求，可以調整flex比例 */
  background-color: #373841;
  padding-top: 5px;
  box-sizing: border-box;
  margin-bottom: 0; /* 確保沒有下方邊距 */
  overflow: hidden;
}

.menu-row {
  border: none; /* 確保沒有邊界 */
  margin: 0; /* 確保沒有邊界外的空間 */
  padding: 0; /* 確保沒有內部的空間 */
  height: 85vh;
  padding-left: 0px;
}

.menu-item {
  border: none; /* 確保沒有邊界 */
  margin: 4px; /* 確保沒有邊界外的空間 */
  padding: 0px 2px 0px 5px; /* 只保留水平間距 */
  margin-bottom: 8px;
  border-radius: 5px;
  align-items: center;
  margin-left: 0;
}

.submenu-item {
  display: none;
}

.menu-title:hover {
  color: #ffffff;
  background-color: #266df7;
  transition: background-color 0.3s; /* Transition effect */
  width: 100%;
}

.menu-title {
  background-color: #373841;
  font-size: 16px;
  color: #c2c1c1;
  padding: 6px 15px 6px 15px;
  width: 100%;
  border: none;
  border-radius: 5px;
  transition: background-color 0.3s; /* Transition effect */
  text-align: left;
  cursor: pointer;
}

.menu-title.selected {
  background-color: #266df7; /* 修改選中的背景顏色 */
  color: #ffffff; /* 修改選中的文字顏色 */
}

.submenu-content {
  display: none;
  background-color: #4a4c5b;
  border-radius: 5px;
  font-size: 14px;
  width: 100%;
}

.submenu-content a {
  color: #c2c1c1;
  padding: 10px 20px;
  margin: 0px 0px -5px 0px; /*減少中間空間距離*/
  text-decoration: none;
  display: block;
}

.submenu-content a:hover,
.submenu-content a.item-selected {
  color: #ffffff;
}

.navbar {
  background-color: #373841;
  width: 12%;
  height: 100%;
  background-size: cover; /* 背景圖像將覆蓋整個視窗 */
  padding-left: 0;
  margin-left: 0;
  margin-top: -7px;
  transition: width 0.5s ease; /* 平滑過渡效果 */
}

/* 縮小的 navbar 樣式 */
.navbar.collapsed {
  width: 30px; /* 縮小後的寬度，您可以根據需要調整 */
}

/* 當 navbar 縮小時，隱藏所有文字 */
.navbar.collapsed .menu-title,
.navbar.collapsed .submenu-item {
  display: none;
}

.header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 15px;
  font-size: 16px;
  color: #797878;
  cursor: pointer;
  background-color: white;
}

.header .admin-text:hover {
  color: #242424;
}

.admin-text {
  margin: 0px 15px;
}

.admin-dropdown {
  display: none; /* 預設隱藏 */
  position: absolute;
  background-color: #ffffff;
  min-width: 147px; /* 您可以根據需要調整 */
  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  border-radius: 5px;
  border: 1px solid #d1d1d1;
  margin-top: 3px;
}

.admin-dropdown-item {
  font-size: 14px;
  padding: 6px 16px;
  color: #373841;
  text-decoration: none;
  display: block;
}

.admin-dropdown-item:hover {
  background-color: #ddd;
  color: #373841;
  border-radius: 5px;
  cursor: pointer;
}

.header .user-icon {
  width: 22px;
  height: 16px;
  display: flex;
  justify-content: flex-end;
  margin-left: -4px;
  margin-top: 2.5px;
  float: right;
}

header p {
  font-size: 18px;
  color: #c2c1c1;
  padding: 4px 25px 12px 15px;
  margin-top: 1em;
}

header p:hover {
  color: #ffffff;
  cursor: pointer;
}

.title-area {
  height: 3.1em;
  background-color: #ffffff;
  // width: 84em;
  cursor: default; // 使用 !important 是為了確保這些樣式具有更高的優先級
}

.title-area * {
  cursor: default;
}

.title-area .admin-text,
.title-area #menu-icon,
.title-area .user-icon {
  cursor: pointer;
}

#menu-icon {
  position: absolute; /* 使用絕對定位將元素移到特定位置 */
  top: 10px; /* 距離頂部的距離 */
  // left: 167px; /* 確保它與左側框框的左側對齊 */
  z-index: 900; /* 確保它出現在其他元素之上 */
  margin-left: 15px;
  padding: 0.5rem;
  transition: left 0.5s ease; /* 添加過渡效果 */
}

#menu-icon:hover {
  color: #242424;
}

.title-line {
  position: absolute; /* 使用絕對定位 */
  // top: 7%; /* 設定從頁面頂部中間開始 */
  left: 0; /* 設定從頁面左側開始 */
  width: 100%; /* 設定寬度為全介面 */
  border-top: 1px solid #b7bfcf; /* 設定橫線的樣式和顏色 */
}

.content {
  width: 100%;
  height: 100%;
  background-color: #f7f7f7;
  flex: 1;
}

.content2 {
  padding: 0rem 1.5rem;
  margin-top: -3.9rem;
  background-color: #f7f7f7;
  /* width: 84em; */
}

.list-search {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

.list-search input {
  padding: 5px;
  padding-left: 12px;
  color: #000000;
  border: 2px solid #c3c3c3;
  border-radius: 50px;
  width: 300px;
}

.button {
  padding: 8px 20px; //按鈕內的空間調整 (順序 →上右下左)
  border: none;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px; //按鈕間的距離
  transition: background-color 0.3s; /* Transition effect */
  text-decoration: none;
}

.button-function {
  display: flex;
  flex-direction: column; /* 將方向改為橫向 */
  align-items: flex-end; /* 使元素在垂直方向上居中對齊 */
  justify-content: right; /* 使元素對齊到右側 */
  gap: 0px; /* 設定元素之間的間距 */
  margin-top: 2.5em;
}

.knowledge-btn {
  background-color: #266df7;
  color: white;
  font-size: 16px;
}

.knowledge-btn:hover {
  background-color: #194aa9;
}

.condition-btn {
  background-color: #1fa7af;
  color: white;
  font-size: 16px;
}

.condition-btn:hover {
  background-color: #167479;
}

.button-page {
  display: flex;
  justify-content: center; //位置調整置中
  align-items: center;
  cursor: pointer;
}

.button-page1,
.button-page2 {
  background-color: #266df7;
  color: white;
  margin: 0px 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-bottom: 10px;
  margin-top: 20px;
  padding: 10px 15px;
}

.button-page1:hover,
.button-page2:hover {
  background-color: #194aa9;
}

.button-page span {
  color: #266df7;
  font-size: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.5; //透明度
}

main p {
  font-size: 24px;
  color: #797878;
  text-align: right;
}

/* 針對整個表格的圓角 */
table {
  width: 100%;
  border-collapse: separate !important;
  border-spacing: 0;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #dadada; /* 若需要邊框 */
}

/* 針對表格的上方左右兩個角落圓角 */
table th:first-child {
  border-top-left-radius: 10px;
}

table th:last-child {
  border-top-right-radius: 10px;
}

/* 針對表格的下方左右兩個角落圓角 */
table tr:last-child td:first-child {
  border-bottom-left-radius: 10px;
}

table tr:last-child td:last-child {
  border-bottom-right-radius: 10px;
}

.row {
  background-color: #dddddd;
  border: 1px solid transparent;
  cursor: pointer;
}

/* 定義鼠標懸停的樣式 */
.row:hover {
  background-color: #c3c3c3;
  border: 1px solid #000000;
}

.row:hover td {
  border: 1px solid #9c9c9c;
}

.row.selected td {
  border: 1px solid #9c9c9c;
}

/* 定義已被點擊的行的樣式 */
.row.selected {
  background-color: #c3c3c3;
  border: 1px solid #9c9c9c;
}

.row.clicked {
  background-color: #c3c3c3;
}

table th {
  background-color: #ffffff;
  color: #000000;
  padding: 10px;
  text-align: center;
}

table td {
  text-align: center;
  color: #000000;
  padding: 10px;
  border: 1px solid transparent;
}

table td,
table th {
  border: 1px solid #c3c3c3;
}

.content-wrapper {
  border: 2px solid #b7bfcf;
  border-radius: 20px;
  padding: 10px;
  margin: 1rem 0rem;
}

/* ----------------- Page 2 (addKL)----------------- */
.backPage {
  position: absolute;
  top: 80px;
  padding-left: 20px;
  display: flex;
  align-items: center;
  font-weight: 500;
  cursor: pointer;
}

.backPage a {
  font-size: 17px;
}

.arrow,
.back-text {
  color: #266df7;
  font-size: 16px;
  margin-right: 8px;
  text-decoration: none;
}

.back-text:hover {
  color: #003ebb;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
}

.buttons-container-item {
  position: absolute; /* 絕對定位 */
  padding-top: 2.5em; /* 距離框框上邊的距離，可以根據需要調整 */
  right: 1.5%; /* 距離框框右邊的距離，可以根據需要調整 */
  display: flex;
  gap: 2px;
  top: 6.5vh;
}

.buttons-container {
  display: flex; /* 設為彈性容器 */
  justify-content: space-between; /* 子元素均勻分布 */
  align-items: center; /* 垂直居中對齊 */
  position: absolute; /* 絕對定位 */
  padding-top: 1.5em; /* 距離框框上邊的距離，可以根據需要調整 */
  right: 1.5vw; /* 距離框框右邊的距離，可以根據需要調整 */
  display: block;
  gap: 2px;
  top: 6vh;
}

.btn-save,
.btn-cancel,
.btn-preview {
  display: inline-block; /* 使按鈕可行內展示，且可以設定寬高 */
  font-size: 16px;
  margin-left: 5px;
  padding: 4px 12px;
  border: none;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-save {
  background-color: #266df7;
  color: white;
  border: 2px solid #266df7;
}

.btn-save:hover {
  background-color: #194aa9;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
  border: 2px solid #194aa9;
  color: #fff;
}

.btn-cancel {
  background-color: #f44336;
  color: white;
  border: 2px solid #f44336;
}

.btn-cancel:hover {
  background-color: #be3127;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
  border: 2px solid #be3127;
  color: #fff;
}

.btn-preview {
  background-color: #8f8f8f;
  color: white;
  border: 2px solid #8f8f8f;
}

.btn-preview:hover {
  background-color: rgb(105, 105, 105);
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
  border: 2px solid rgb(105, 105, 105);
  color: #fff;
}

.btn-edit,
.btn-delete,
.btn-pdf {
  display: inline-block; /* 使按鈕可行內展示，且可以設定寬高 */
  font-size: 16px;
  margin-left: 5px;
  padding: 4px 12px;
  border: none;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-edit {
  background-color: white;
  color: #266df7;
  border: 2px solid #266df7;
}

.btn-edit:hover {
  background-color: #266df7;
  color: white;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
}

.btn-delete {
  background-color: white;
  color: #f44336;
  border: 2px solid #f44336;
}

.btn-delete:hover {
  background-color: #f44336;
  color: white;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
}

.btn-pdf {
  background-color: white;
  color: #5e5e5e;
  border: 2px solid #5e5e5e;
}

.btn-pdf:hover {
  background-color: #5e5e5e;
  color: white;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
}

.content-box {
  display: flex;
  width: 100%; /* 确保父容器充满可用空间 */
  gap: 35px; /* 旁邊的空格，根據需要調整 */
  border-radius: 20px;
  padding: 20px;
  height: 100%;
  overflow-y: none; /* 當內容超過容器高度時顯示滾動條 */
}

.content-box-left {
  padding: 8px 20px 8px 20px;
  width: 30em;
  height: 80vh;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
  border: 2px solid #b7bfcf;
  border-radius: 15px;
  background-color: #ffffff;
  top: 0.7em; /* 要釘選的位置，例如在頁面頂部 */
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto; /* 當內容超出容器時顯示垂直滾動條 */
}

.content-box-left::-webkit-scrollbar {
  width: 13px;
  margin-top: 5px;
}

.content-box-left::-webkit-scrollbar-thumb {
  background-color: #8b8b8b;
  border: 3px solid transparent;
  border-radius: 20px;
  background-clip: padding-box;
}

.content-box-left::-webkit-scrollbar-track {
  background-color: #f0f0f0;
  border: 3px solid transparent;
  border-radius: 20px;
  background-clip: padding-box;
}

.content-box-right {
  position: relative; /* 使框框具有相對定位 */
  padding: 20px 10px 10px 20px;
  padding-right: 0px; /* 或更多的像素，視需要而定 */
  width: 100%;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
  border: 2px solid #b7bfcf;
  border-radius: 15px;
  background-color: #ffffff;
  height: 80vh;
  top: 0.7em;
  overflow-y: auto; /* 當內容超出容器時顯示垂直滾動條 */
  box-sizing: border-box; /* 確保 padding 和 border 被包括在元素的總寬度和高度內 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-box-right::-webkit-scrollbar {
  width: 16px;
  margin-top: 5px;
}

.content-box-right::-webkit-scrollbar-thumb {
  background-color: #8b8b8b;
  border: 3px solid transparent;
  border-radius: 20px;
  background-clip: padding-box;
}

.content-box-right::-webkit-scrollbar-track {
  background-color: #f0f0f0;
  border: 3px solid transparent;
  border-radius: 20px;
  background-clip: padding-box;
}

.content-box-left2 {
  padding: 20px;
  width: 30em;
  height: 80vh;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
  border: 2px solid #b7bfcf;
  border-radius: 15px;
  background-color: #ffffff;
  top: 0.7em; /* 要釘選的位置，例如在頁面頂部 */
  overflow-y: auto; /* 當內容超出容器時顯示垂直滾動條 */
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-box-left2::-webkit-scrollbar {
  width: 13px;
  margin-top: 5px;
}

.content-box-left2::-webkit-scrollbar-thumb {
  background-color: #8b8b8b;
  border: 3px solid transparent;
  border-radius: 20px;
  background-clip: padding-box;
}

.content-box-left2::-webkit-scrollbar-track {
  background-color: #f0f0f0;
  border: 3px solid transparent;
  border-radius: 20px;
  background-clip: padding-box;
}

.text-box {
  width: 100%;
  height: 30%;
  border: 1px solid #afafaf;
  border-radius: 5px;
  padding: 5px;
  background-color: #ffffff;
  resize: vertical;
  min-height: 150px;
  max-height: 400px;
  font-size: 16px;
}

// 假設是 global.module.scss 或類似的檔案
.text-area-container {
  position: relative; // 父容器設置為相對定位
  width: 98%;
}

.text-area {
  width: 100%; // 確保 text area 充滿容器寬度
  height: 30%;
}

.color-picker-container {
  position: absolute; // 絕對定位
  top: -2px; // 向上移動10px
  right: 0; // 右側對齊
  width: 3%; // 縮小寬度
  height: 20px; // 縮小高度，保持高度與寬度一致以維持比例
  z-index: 0; // 確保在最上層
}

.color-picker-container-sop {
  position: absolute; // 絕對定位
  top: -2px; // 向上移動10px
  right: 0; // 右側對齊
  width: 24px; // 縮小寬度
  height: 20px; // 縮小高度，保持高度與寬度一致以維持比例
  z-index: 10; // 確保在最上層
}

// 應用自定義 CSS 類 修改 Ant React UI 組件樣式
.custom-color-picker .ant-btn {
  width: 10px !important;
  height: 10px !important;
  padding: 0 !important;

  .anticon {
    font-size: 4px !important;
  }
}

.red-star {
  position: relative; /* 讓偽元素的絕對定位參考此元素 */
}

.red-star::before {
  content: '★'; /* 使用星星符號 */
  position: absolute;
  left: -12px;
  top: 2px;
  color: red; /* 設定顏色為紅色 */
  font-size: 12px; /* 設定字體大小 */
}

.image-box {
  position: relative;
  display: flex;
  flex-direction: row; /* 设置为水平方向排列 */
  flex-wrap: wrap;
  justify-content: flex-start; /* 从左侧开始排列图像 */
  overflow: hidden; /* 這將確保超出框架的圖片部分不會顯示 */
  width: 98%;
  height: 30%;
  border: 1px solid #afafaf;
  border-radius: 5px;
  padding: 5px;
  background-color: #ffffff;
  resize: vertical;
  min-height: 150px;
  max-height: 450px;
  cursor: not-allowed; /* 防止輸入文字 */
}

.uploaded-image {
  max-width: 200px; /* 設定固定的最大寬度 */
  height: 100px; /* 設定固定的高度 */
  display: inline-block; /* 圖片水平排列 */
  vertical-align: top; /* 確保圖片頂部對齊 */
}

.image-box .placeholder-text {
  color: #7e7e7e;
  font-size: 14px;
  position: absolute;
  left: 5px;
  margin-top: 1px;
}

.image-actions button {
  margin-top: 10px;
  border: 0px solid #afafaf;
  border-radius: 10px;
  padding: 5px 8px;
  cursor: pointer;
  transition: background-color 0.3s; /* Transition effect */
  margin-top: 3px;
}

.upload-btn,
.upload-btn-remarks,
.upload-btn-step,
.upload-btn-model,
.upload-btn-tools,
.upload-btn-position {
  background-color: #266df7;
  color: white;
}

.upload-btn:hover,
.upload-btn-remarks:hover,
.upload-btn-step:hover,
.upload-btn-model:hover,
.upload-btn-tools:hover,
.upload-btn-position:hover {
  background-color: #194aa9;
}

.delete-btn,
.delete-btn-remarks,
.delete-btn-step,
.delete-btn-model,
.delete-btn-tools,
.delete-btn-position {
  background-color: #f44336;
  color: white;
  margin-left: 3px;
}

.delete-btn:hover,
.delete-btn-remarks:hover,
.delete-btn-step:hover,
.delete-btn-model:hover,
.delete-btn-tools:hover,
.delete-btn-position:hover {
  background-color: #be3127;
}

// #upload-btn-step,
// #upload-btn-remarks {
//   background-color: #266df7;
//   color: white;
// }

// #upload-btn-step:hover,
// #upload-btn-remarks:hover {
//   background-color: #194aa9;
// }

// #delete-btn-step,
// #delete-btn-remarks {
//   background-color: #f44336;
//   color: white;
// }

// #delete-btn-step:hover,
// #delete-btn-remarks:hover {
//   background-color: #be3127;
// }

/*  --待加入文字顏色盤--
  .color-picker {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background: url("path_to_color_picker_icon.png");
    cursor: pointer;
  } */

/* 定义下拉选择框的基本样式 */
.dropdown {
  width: 100%;
  position: relative;
  display: inline-block;
  border-radius: 5px;
}

/* 修正选择框选项的样式 */
.fault-Info {
  width: 100%;
  padding: 5px;
  border-radius: 5px;
  border: 1px solid #a3a3a3;
  font-size: 14px;
  color: #000;
  background-color: white; /* 确保背景色为白色 */
  position: relative;
}

.form-group {
  margin-bottom: 5px; /* 调整垂直间距的大小 */
}

// antd 的 Select 組件的角變成圓角
:global {
  .ant-select-selector {
    border-radius: 8px !important; // 將邊角設為圓角
    height: 32px !important;
  }

  .ant-select-dropdown {
    border-radius: 8px !important; // 也將下拉選單的邊角設為圓角
  }

  .ant-input {
    border-radius: 8px !important; // 設定 Input 欄位的邊角為圓角
  }

  .input-big {
    height: 80px !important;
  }
}

/* 自定义选择框容器样式 */
.custom-select {
  position: relative;
}

/* 自定义下拉选项列表样式 */
.custom-datalist {
  display: none;
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
  position: absolute;
  /* top: calc(55%); /* 调整下拉选项列表的垂直位置 */
  top: 100%; /* 更改位置以确保下拉菜单紧接在输入框下方 */
  height: 8em;
  left: 0;
  z-index: 1;
  list-style: none;
  padding: 0px;
  background-color: white; /* 设置背景色为白色 */
  max-height: 200px; /* 设置最大高度 */
  overflow-y: auto; /* 超出时显示滚动条 */
  cursor: pointer;
  margin-bottom: 10px;
  bottom: 100%; /* 或根据需要调整 */
}

.custom-datalist.active {
  display: block; /* 當激活時顯示 */
}

/* 下拉选项列表中的每个选项样式 */
.custom-datalist li {
  font-size: medium;
  padding: 6px;
  cursor: pointer;
  background-color: #fff;
  border-bottom: 1px solid #ccc;
  border-radius: 3px;
  color: #000;
  min-height: 20px; /* 最小高度，确保内容可见 */
  line-height: 20px; /* 行高与高度一致，垂直居中对齐文本 */
}

/* 去掉最后一个选项的下边框 */
.custom-datalist li:last-child {
  border-bottom: none;
}

/* 鼠标悬停时选项的样式 */
.custom-datalist li:hover {
  background-color: #d4d4d4;
  transition: background-color 0.3s;
  border: 1px solid #7c7c7c;
}

/* 下拉箭头样式 */
.drop-down-arrow {
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-50%);
  font-size: 14px;
  color: #4b4b4b;
  cursor: pointer; /* 添加鼠标指针样式 */
  transition:
    background-color 0.3s,
    border 0.3s; /* 添加过渡效果 */
}

.showMachine {
  display: flex;
  justify-content: center; /* 將子元素對齊到右邊 */
  /* 確保容器滿寬度展開 */
  width: 100%;
  padding-left: 2px;
}

.btn-showMachine {
  display: inline-block; /* 使按鈕可行內展示，且可以設定寬高 */
  font-size: 16px;
  padding: 2px; /* 可根据按钮的具体尺寸调整 */
  margin-left: 3px;
  margin-top: -5px;
  border: none;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap; /* 防止文本换行 */
  background-color: #ffffff;
  color: #8f8f8f;
  border: 2px solid #8f8f8f;
  width: 100%;
  text-align: center;
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}

.btn-showMachine:hover {
  background-color: rgb(233, 233, 233);
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
  border: 2px solid rgb(105, 105, 105);
}

/* ----------------- Page 3 (SOP)----------------- */

.step,
.add-step {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  height: 70px;
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 10px;
  background-color: #ffffff;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 新增的陰影效果 */
  transition: background-color 0.3s; /* 平滑的背景色過渡 */
}

.step:hover,
.add-step:hover {
  background-color: #46a3c2;
  color: #ffffff;
  transition: background-color 0.3s; /* Transition effect */
}

.step .delete {
  cursor: pointer;
}

.add-step {
  justify-content: center;
}

.step.active {
  background-color: #4ba4c1;
}

.trash-icon {
  width: 23px; // 或您想要的大小
  height: auto;
  margin-left: 5px;
  cursor: pointer;
}

.modal-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 2;
}

.modal {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  padding-left: 0;
  padding-right: 0;
  background-color: #fff;
  width: 30vw;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.5); /* 水平偏移，垂直偏移，模糊距離，顏色 */
}

.modal-condition {
  position: relative;
  top: 50%;
  left: 52%;
  transform: translate(-50%, -50%);
  padding: 20px 0;
  background-color: #fff;
  width: 60%;
  height: 78%;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  flex-flow: row wrap;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); /* 水平偏移，垂直偏移，模糊距離，顏色 */
  overflow: hidden; /* 確保內部的元件不會超出這個視窗 */
}

.modal-title-SOP {
  font-size: 25px;
  margin-bottom: 10px;
}

.modal > *:not(.title-line2) {
  padding-left: 25px;
  padding-right: 25px;
}

.title-line2 {
  padding: 0px;
  border: 0;
  border-top: 1px solid #d3d3d3;
  width: 100%; /* 設定寬度為全介面 */
  left: 0; /* 設定從頁面左側開始 */
  margin-left: 0;
  margin-right: 0;
}

.close-modal-btn {
  position: absolute;
  top: 10px;
  right: 5px;
  background: none;
  color: #757575;
  border: none;
  font-size: 30px;
  cursor: pointer;
}

#account {
  background-color: #d3d3d3; /* 灰色背景 */
  border: 1px solid #ccc;
  padding: 5px;
  width: 100%;
  margin-bottom: 15px;
}

.input-account {
  background-color: #f0f0f0; /* 灰色背景 */
  border: 1px solid #ccc;
  padding: 10px;
  width: 100%;
  border-radius: 5px;
}

.input-field {
  margin-bottom: 15px;
  margin-top: 15px;
}

.input-SOPName {
  background-color: #ffffff;
  border: 1px solid #ccc;
  padding: 10px;
  width: 100%;
  border-radius: 5px;
}

.readonly-field {
  pointer-events: none;
}

.buttons {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
}

.buttons button {
  margin-left: 10px;
  margin-top: 30px;
}

#cancelBtn {
  font-size: 16px;
  background-color: #f44336;
  color: white;
  border: 2px solid #f44336;
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease-in-out;
}

#cancelBtn:hover {
  background-color: #be3127;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
}

#saveBtn {
  font-size: 17px;
  background-color: #266df7;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease-in-out;
}

#saveBtn:hover {
  background-color: #194aa9;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
}

.active-step {
  background-color: #46a3c2;
  color: #ffffff;
}

/* ----------------- Page 4 (預覽新增)----------------- */

.content-box-middle {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 20px;
  margin-top: -10px;
  width: 100%;
  height: 80vh;
  transition: width 0.3s ease-in-out;
  border: 2px solid #b7bfcf;
  border-radius: 15px;
  background-color: #ffffff;
  left: 5px;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-box-middle-bigView {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 20px;
  margin-top: -10px;
  width: 100%;
  transition: width 0.3s ease-in-out;
  border: 2px solid #b7bfcf;
  border-radius: 15px;
  background-color: #ffffff;
  left: 5px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-box-middle::-webkit-scrollbar {
  width: 16px;
  margin-top: 5px;
}

.content-box-middle::-webkit-scrollbar-thumb {
  background-color: #8b8b8b;
  border: 3px solid transparent;
  border-radius: 20px;
  background-clip: padding-box;
}

.content-box-middle::-webkit-scrollbar-track {
  background-color: #f0f0f0;
  border: 3px solid transparent;
  border-radius: 20px;
  background-clip: padding-box;
}

/* 使用 flex 布局来对内容进行水平排列 */
.model,
.tools,
.illustration {
  position: relative;
  display: flex;
  justify-content: space-between; /* 使容器内的项目均匀分布 */
  align-items: flex-start; /* 项目从容器的起始边缘对齐 */
  margin-bottom: 20px; /* 添加底部外边距用于分隔栏目 */
  overflow: hidden; /* 保持原有设置 */
  height: 20em; /* 设置一个固定高度 */
  width: 100%; /* 设置宽度为100% */
}

/* 所有包含圖片的容器都應該包括 box-sizing 屬性 */
.model img,
.tools img,
.illustration img,
.content-section img {
  box-sizing: border-box; /* 確保內邊距和邊框都包含在寬度內 */
  max-width: calc(100% - 10px); /* 計算寬度，留出邊距 */
  height: auto; /* 自動調整高度以保持圖片的比例 */
  object-fit: contain; /* 確保圖片不變形並且完全顯示 */
  margin: 2px; /* 添加上下左右2px的邊距 */
  flex-shrink: 1; /* 允许图像在必要时缩小 */
}

/* 强制子元素在必要时缩小以适应容器宽度 */
.model > *,
.tools > *,
.illustration > *,
.preview-content > * {
  flex-shrink: 1;
  min-width: 0; /* 避免flex项目溢出其容器 */
}

/* ----------------- condition page----------------- */

/* 基本樣式設定 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* 左右兩個區塊的共同特性 */
.left-box-condition,
.right-box-condition {
  flex: 0 0 100%;
  width: 100%;
  margin: 0;
}

.right-box-condition {
  margin-top: 20px;
}

/* 現有的CSS樣式（含優化） */
.left-box-condition,
.right-box-condition {
  flex-flow: row wrap;
  flex: 0 0 48%;
  width: 49%;
  margin: 0 4%;
  height: 80%;
}

.left-box-condition {
  margin-left: 8%;
  position: relative;
  margin-top: 5vh;
}

.left-box-condition p {
  color: rgb(100, 100, 100);
  margin-left: 7px;
  margin-top: 5px;
  margin-bottom: 5px;
  font-size: 18px;
}

.right-box-condition {
  height: 100%;
  margin-left: 51%;
  margin-top: -54.5vh;
  padding: 10px;
}

.box-condition1,
.box-condition2 {
  width: 90%;
  border-radius: 5px;
  overflow: hidden;
}

.box-condition1 {
  height: 80%;
  border: 1px solid #ccc;
  border-bottom: 3px solid #ccc;
}

.box-condition2 {
  width: 100%;
  height: 70%;
  border: none;
}

#search {
  width: 100%;
  padding: 8px;
  margin-bottom: 5px;
  border: 1px solid #a3a3a3;
  background-color: #f0f0f0;
  border-radius: 5px;
  outline: none;
  font-size: 16px;
}

.scroll-box {
  overflow-y: scroll;
  height: 87.8%;
  background-color: #ffffff;
  border: 1px solid #a3a3a3;
  border-radius: 5px;
}

.condition-item {
  border: 1px solid #b9b9b9;
  padding: 3px 0px 0px 10px;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
  color: black;
}

.condition-item:hover {
  background-color: #e4e4e4;
  transition: background-color 0.2s;
}

.icon {
  cursor: grab;
  color: #a0a0a0;
  width: 5%;
  font-size: 25px;
  padding: 0px 25px 0px 0px;
}

.icon:active {
  cursor: grabbing;
}

.modal-title-condition {
  font-size: 25px;
  margin-bottom: 10px;
}

.modal-condition > *:not(.title-line2) {
  padding-left: 25px;
  padding-right: 25px;
}

.buttons-condition {
  position: relative;
  top: -20vh; /* 將按鈕上移 */
  margin-left: 20em;
  right: 3vw;
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}

.form-group-condition {
  display: flex;
  align-items: center; /* 這將確保標籤和欄位在垂直方向上居中對齊 */
  margin-bottom: 16px; /* 调整垂直间距的大小 */
}

.form-group-condition label {
  display: inline-block;
  width: 6vw; /* 這個寬度可以根據您的需求進行調整，以確保所有的標籤都對齊 */
  font-size: 17px;
}

/* 自定义选择框容器样式 */
.custom-select-condition {
  width: calc(100% - 100px); /* 調整寬度，減去<label>的寬度 */
  display: inline-block;
  position: relative;
  width: 60%;
}

/* ----------------- database page----------------- */

.content-wrapper-database table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 5px;
  overflow: hidden;
  border: 1px solid #8f8f8f;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

/* 定義edit-section樣式，方便管理和調整 */
.mark-note {
  display: flex;
  justify-content: space-between; /* 使內容在左右兩端對齊 */
  margin: 0;
}

.mark-text {
  text-align: left;
  font-size: 14px;
  margin: 0; /* 移除默認的邊距 */
  margin-bottom: 3px;
  color: rgb(122, 122, 122);
  text-shadow: 2px 2px 5px #a7a7a7;
}

.mark-note p {
  text-align: left;
  font-size: 14px;
  margin: 0; /* 移除默認的邊距 */
  margin-top: 7px; /* 增加頂部邊距以向下移動文字 */
}

/* 新增放大按鈕樣式 */
.btn-enlarge {
  font-size: 15px;
  margin-top: 5px;
  margin-bottom: 5px; /* 更加縮小下邊距 */
  padding: 0px 4px; /* 縮小上下的內部距離 */
  padding-top: -5px;
  border: none;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition:
    background-color 0.3s,
    width 0.3s ease-in-out;
  background-color: white;
  color: #266df7;
  border: 2px solid #266df7;
}

.btn-enlarge:hover {
  background-color: #011336;
  color: white;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
}

.content-wrapper-database table th:first-child {
  border-top-left-radius: 5px;
}

.content-wrapper-database table th:last-child {
  border-top-right-radius: 5px;
}

.content-wrapper-database table tr:last-child td:first-child {
  border-bottom-left-radius: 5px;
}

.content-wrapper-database table tr:last-child td:last-child {
  border-bottom-right-radius: 5px;
}

.row-title-database {
  background-color: #dddddd; /* 更改背景色 */
}

.row-database {
  background-color: #ffffff; /* 更改背景色 */
  border: 1px solid transparent;
}

.row-title-database th {
  background-color: #ececec;
  color: #000000;
  padding: 10px;
  font-size: 20px; /* 加大文字 */
}

.row-database td {
  font-size: 18px; /* 加大文字 */
  text-align: center;
  color: #000000;
  padding: 10px;
  border: 1px solid transparent;
}

.row-database td,
.row-title-database th {
  border: 1px solid #c3c3c3;
}

/* 移除懸停和點擊效果 */
.row-database:hover,
.row-database.selected,
.row-database.clicked {
  background-color: #ffffff;
  border: 1px solid transparent;
}

.file-title {
  /* 设置内部元素的大小包括内容、填充和边框 */
  box-sizing: border-box;
  transition: box-shadow 0.3s ease;
  overflow: hidden; /* 防止内容溢出 */
}

.file-title2 {
  /* 设置内部元素的大小包括内容、填充和边框 */
  box-sizing: border-box;
  transition: box-shadow 0.3s ease;
  overflow: hidden; /* 防止内容溢出 */
}

.hover-enabled .step1,
.hover-enabled .step2,
.hover-enabled .step3,
.hover-enabled .step4 {
  /* 保持现有的样式设置 */
  background-color: #ffffff; /* 鼠标悬停时的背景颜色 */
  opacity: 1; /* 设置不透明度为全不透明 */
  cursor: pointer; /* 设置光标为指针形状 */
  box-sizing: border-box; /* 确保padding和border都包含在width和height内 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 只針對有 "hover-enabled" 類別的頁面 */
.hover-enabled .step1:hover,
.hover-enabled .step2:hover,
.hover-enabled .step3:hover,
.hover-enabled .step4:hover {
  background-color: #f1f1f1;
  opacity: 1;
  cursor: pointer;
  box-sizing: border-box;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-database {
  position: fixed; /* 之前是relative，改成fixed更符合模态框的特性 */
  top: 50%;
  left: 52%;
  transform: translate(-50%, -50%);
  padding: 0;
  background-color: #fff;
  padding-bottom: 1em;
  width: 60%;
  height: 78%;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); /* 水平偏移，垂直偏移，模糊距離，顏色 */
  overflow: hidden; /* 確保內部的元件不會超出這個視窗 */
}

.modal-title-database {
  font-size: 35px;
  margin-bottom: 20px;
  margin-top: 15px;
  margin-left: 30px;
  margin-right: 55px; /* 设置与关闭按钮的间距 */
  font-weight: normal;
  white-space: normal; /* 允许换行 */
}

.modal-database > *:not(.title-line2) {
  padding-left: 0;
  padding-right: 0;
}

.title-line-button {
  padding: 0px;
  border: 0;
  border-top: 1px solid #d3d3d3;
  width: 100%; /* 設定寬度為全介面 */
  left: 0; /* 設定從頁面左側開始 */
  margin-left: 0;
  margin-right: 0;
  margin-top: -7em;
  margin-bottom: 1em;
}

.buttons-database {
  position: relative;
  margin-top: 2em; /* 將按鈕上移 */
  margin-left: 2em;
  display: flex;
  justify-content: flex-end;
  padding-right: 50px;
}

.database-step-container {
  max-width: 100%; /* 調整為 100% 以使容器占滿整個寬度 */
  margin: 0 auto; /* 確保上下保持自動間距，左右間距為0 */
  padding: 0; /* 移除內邊距 */
  overflow-y: auto;
}

.database-close-modal-btn {
  position: absolute;
  top: 10px;
  right: 40px;
  background: none;
  color: #757575;
  border: none;
  font-size: 30px;
  cursor: pointer;
  z-index: 1001;
}

.section-database {
  position: relative;
  border: none;
  padding: 0px 0px 150px 2px;
  margin: 8px 5px;
  font-weight: bold;
}

.section-title {
  position: relative;
  border: none;
  font-weight: bold;
  margin-left: 8px;
  margin-top: 8px;
  font-size: 18px;
}

.section-database:not(:last-of-type) {
  margin-bottom: 16px;
}

/* 設置欄位容器的最大高度，內容高度超出時自動滾動 */
.section-database {
  max-height: 100em; /* 根據需要調整最大高度 */
  overflow-y: auto;
  overflow: hidden; /* 隐藏溢出的内容 */
}

/* 調整圖片框和文字框的位置 */
.image-section,
.description-section,
.notes-section {
  margin-top: 5px; /* 設置上方間距 */
}

/* 調整圖片和文字的上下對齊 */
.modal-image,
.modal-textarea {
  vertical-align: middle;
}

.modal-textarea {
  display: block; /* 确保 textarea 以块级元素显示 */
  width: 50em; /* 或您希望的固定宽度 */
  height: 12em; /* 或您希望的固定高度 */
  margin-bottom: 0;
  border: none;
}

.modal-textarea::placeholder {
  color: transparent; /* 這將隱藏所有支持的瀏覽器的placeholder文本 */
}

/* 當textarea獲得焦點時，移除外框線 */
.modal-textarea:focus {
  outline: none; /* 這將移除焦點時的外框 */
  /* border: 1px solid #ccc; /* 可選擇設置一個較不明顯的邊框顏色 */
}

.image-section .modal-image,
.notes-section .modal-image {
  display: block; /* 使图片以块级元素显示，确保其在新的一行 */
  height: 200px; /* 设定一个固定的高度 */
  width: auto; /* 宽度自动，保持原始比例 */
  object-fit: contain; /* 保证图片比例正确 */
  margin-bottom: 0;
  margin-top: 0;
  margin-left: 0;
  margin-bottom: 0;
  margin-right: 0;
  padding-right: 0;
}

.delete-image-btn {
  display: none; /* 默认不显示，仅当图片存在时显示 */
  position: absolute; /* 绝对定位，相对于 .image-container 定位 */
  top: 0;
  right: 0;
  transform: translate(50%, -50%); /* 将按钮的一半移出图片外 */
  background-color: #fff; /* 按钮背景色 */
  color: #757575; /* 按钮符号颜色 */
  border: 1px solid #757575; /* 按钮边框 */
  border-radius: 50%; /* 圆形按钮 */
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px; /* 使 "-" 符号垂直居中 */
  cursor: pointer;
  font-size: 16px; /* 设置 "-" 符号大小 */
  font-weight: bold; /* 加粗 "-" 符号 */
  z-index: 10; /* 确保按钮在图片之上 */
}

.image-container-modal {
  position: relative; /* 设置相对定位，用于定位删除按钮 */
  display: inline-block; /* 确保容器是内联块级元素，以便于布局 */
  border: none;
  line-height: 0; /* 移除图片底部的空白间隙 */
  width: auto;
  margin-top: 15px;
  margin-left: 8px;
  margin-right: 5em;
}

.has-image {
  border: 1px solid #a7a7a7; /* 當圖片存在時顯示邊框 */
  border-radius: 3px;
  position: relative; /* 设置相对定位 */
}

.database-add-image {
  display: inline-flex; /* 使用flexbox來對齊內容 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  font-size: 50px; /* "+"號的大小 */
  line-height: 1; /* 這將使得"+"更緊湊 */
  cursor: pointer; /* 鼠標懸停時顯示手型光標 */
  border: 2px solid #266df7; /* 黑色邊框 */
  border-radius: 50%; /* 圓角邊框形成圓形 */
  width: 50px; /* 容器的寬度，根據需要調整 */
  height: 50px; /* 容器的高度，根據需要調整 */
  background-color: #fff; /* 容器的背景色，根據需要調整 */
  color: #266df7; /* "+"號的顏色 */
  margin-right: 10px; /* 右側邊距，根據需要調整 */
  margin-top: 170px; /* 上側邊距，根據需要調整 */
  float: right;
  opacity: 0.5;
}

.database-add-image-remark {
  display: inline-flex; /* 使用flexbox來對齊內容 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  font-size: 50px; /* "+"號的大小 */
  line-height: 1; /* 這將使得"+"更緊湊 */
  cursor: pointer; /* 鼠標懸停時顯示手型光標 */
  border: 2px solid #266df7; /* 黑色邊框 */
  border-radius: 50%; /* 圓角邊框形成圓形 */
  width: 50px; /* 容器的寬度，根據需要調整 */
  height: 50px; /* 容器的高度，根據需要調整 */
  background-color: #fff; /* 容器的背景色，根據需要調整 */
  color: #266df7; /* "+"號的顏色 */
  margin-right: 10px; /* 右側邊距，根據需要調整 */
  margin-top: 195px; /* 上側邊距，根據需要調整 */
  margin-bottom: -30px;
  float: right;
  opacity: 0.5;
}

.database-add-image:hover {
  background-color: #266df7; /* 鼠標懸停時的背景色 */
  color: #ffffff;
  opacity: 1;
}

#addImageBtn:hover,
#addImageBtn-remark:hover {
  /* 应用 hover 状态的样式 */
  cursor: pointer;
  background-color: #266df7; /* 鼠標懸停時的背景色 */
  color: #ffffff;
  opacity: 1;
}

.image-preview {
  display: block;
  width: 100%;
  margin-top: 8px;
}

.database-scrollbar {
  height: 103%; /* 調整以符合需要 */
  overflow-y: scroll;
}

.database-hr {
  margin-top: -9em;
  width: 100%;
}

.image-section img {
  display: block; /* 使圖片獨占一行 */
  margin-top: -10em; /* 設置上方間距為5px */
  margin-bottom: 5em;
  margin-left: 0.5em;
  width: auto;
  height: auto;
}

/* 調整文字區塊的外邊距來對齊標題 */
.description-section textarea,
.notes-section textarea {
  position: relative; /* 或者 'absolute'，取决于布局需求 */
  z-index: 1; /* 将 z-index 设置为高于页面上其他元素的值 */
  margin-top: 0.2em; /* 設置上方間距為5px */
  margin-left: 0.4em;
  width: 92.5%;
  max-width: 100%; /* 最大宽度不超过父元素 */
  height: auto; /* 高度根据内容自动调整 */
  min-height: 14em; /* 最小高度設定，可以根據需要調整 */
  overflow: auto; /* 内容超出時顯示滾動條 */
  box-sizing: border-box; /* 边框和内边距包含在宽度和高度内 */
  resize: none; /* 禁止手动改变大小 */
  font-size: 1.1rem;
}

/* database超出模態框文字內容，自動加上外框線 */
.textarea-overflow {
  border: 1px solid #adadad;
  border-radius: 2px;
}

/* ----------------- Alarm Page ----------------- */
.content-box-left-alarm {
  padding: 0px;
  width: 25vw;
  height: 80vh;
  left: 1em;
  margin-top: 0;
  transition: width 0.3s ease-in-out;
  border-radius: 15px;
  background-color: #ffffff;
  top: 1em;
  position: relative;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
  display: flex; // 添加：使用 flex 布局
  flex-direction: column; // 添加：垂直方向排列
  overflow: hidden; // 修改：防止內容溢出
}

.title-bar {
  flex-shrink: 0; // 添加：防止標題欄縮小
  // ... 其他現有的 title-bar 樣式 ...
}

.menu {
  flex: 1; // 添加：讓選單區域填滿剩餘空間
  overflow: hidden; // 添加：防止內容溢出
}

.alarm-list {
  height: 100%; // 修改：使用完整高度
  overflow-y: auto; // 保持滾動功能
  padding: 10px;
}

// 添加滾動條樣式
.alarm-list::-webkit-scrollbar {
  width: 16px;
  margin-top: 5px;
}

.alarm-list::-webkit-scrollbar-thumb {
  background-color: #8b8b8b;
  border: 3px solid transparent;
  border-radius: 20px;
  background-clip: padding-box;
}

.alarm-list::-webkit-scrollbar-track {
  background-color: #f0f0f0;
  border: 3px solid transparent;
  border-radius: 20px;
  background-clip: padding-box;
}

.content-box-right-alarm {
  position: relative; /* 使框框具有相對定位 */
  padding: 10px;
  margin: 0px 2em 0px 3em;
  width: 100%;
  border: 1px solid #b7bfcf;
  border-radius: 15px;
  background-color: transparent; /* 或者設置為父容器的背景色 */
  height: 80vh;
  top: 1em; /* 要釘選的位置，例如在頁面頂部 */
  overflow: hidden; /* 當內容超出容器時顯示垂直滾動條 */
  box-sizing: border-box; /* 確保 padding 和 border 被包括在元素的總寬度和高度內 */
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.4);
  cursor: pointer;
  z-index: inherit;
}

.content-box-right-alarm:hover {
  background-color: #e6e6e6;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
}

/* 保持特定元素可交互 */
.content-box-right-alarm .interactive-element {
  pointer-events: auto;
  opacity: 1; /* 完全不透明表示該元素是可用的 */
}

.content-box-right-alarm2 {
  position: relative; /* 使框框具有相對定位 */
  padding: 8px 10px 10px 20px;
  padding-right: 0px; /* 或更多的像素，視需要而定 */
  width: 58.8vw;
  left: 4.2em;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
  border: 1px solid #b7bfcf;
  border-radius: 15px;
  background-color: transparent; /* 或者設置為父容器的背景色 */
  height: 80vh;
  top: 1em; /* 要釘選的位置，例如在頁面頂部 */
  overflow: none; /* 當內容超出容器時顯示垂直滾動條 */
  box-sizing: border-box; /* 確保 padding 和 border 被包括在元素的總寬度和高度內 */
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  z-index: inherit;
}

.edit-container {
  position: fixed; /* 固定定位 */
  width: 15.8vw; /* 設定與 .content-box-left-alarm 相同的寬度 */
  top: 15vh; /* 距離框框上邊的距離，可以根據需要調整 */
  left: 22vw;
  display: flex;
  justify-content: flex-end; /* 添加此行以將按鈕對齊到右側 */
  gap: 2px;
}

// .edit-container button {
//   border-radius: 5px;
//   font-size: 13px;
//   background-color: #ffffff; /* 背景顏色 */
//   color: #1d6faa;
//   box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
// }

// .edit-container button:hover {
//   background-color: #c9e3f5; /* 背景顏色 */
//   color: #1d6faa;
//   transition: width 0.5s ease-in-out; /* 平滑過渡效果 */
// }

.edit-container-alarmItem {
  position: absolute; /* 絕對定位 */
  top: 88px; /* 距離框框上邊的距離，可以根據需要調整 */
  left: 26.7em; /* 距離框框右邊的距離，可以根據需要調整 */
  display: flex;
  gap: 2px;
}

.edit-container-alarmItem button {
  border-radius: 5px;
  font-size: 13px;
  background-color: #ffffff; /* 背景顏色 */
  color: #1d6faa;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.edit-container-alarmItem button:hover {
  background-color: #c9e3f5; /* 背景顏色 */
  color: #1d6faa;
  transition: width 0.5s ease-in-out; /* 平滑過渡效果 */
}

.edit-button {
  cursor: pointer; /* 鼠標樣式 */
  color: #1d6faa;
  padding: 3px 5px; /* 按鈕內邊距 */
  border: 1px solid #1d6faa; /* 按鈕邊框樣式 */
  border-radius: 8px;
  font-size: 14px;
  margin-left: 4px;
}

.edit-button:hover,
.save-button:hover {
  background-color: #cfe6f6;
}

.save-button {
  cursor: pointer; /* 鼠標樣式 */
  color: #1d6faa;
  padding: 3px 5px; /* 按鈕內邊距 */
  border: 1px solid #1d6faa; /* 按鈕邊框樣式 */
  border-radius: 8px;
  font-size: 13px;
  margin-left: 4px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  transition: width 0.5s ease-in-out; /* 平滑過渡效果 */
}

.delete-button {
  cursor: pointer; /* 鼠標樣式 */
  color: #aa1d1d;
  padding: 3px 5px; /* 按鈕內邊距 */
  border: 1px solid #aa1d1d; /* 按鈕邊框樣式 */
  border-radius: 8px;
  font-size: 13px;
  margin-left: 4px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  transition: width 0.5s ease-in-out; /* 平滑過渡效果 */
}

.delete-button:hover {
  background-color: #f6cfcf;
}

.edit-button-click {
  cursor: pointer; /* 鼠標樣式 */
  padding: 3px 5px; /* 按鈕內邊距 */
  border: 1px solid 0.1d6faa; /* 按鈕邊框樣式 */
}

#save-button {
  cursor: pointer; /* 鼠標樣式 */
  padding: 3px 5px; /* 按鈕內邊距 */
  border: 1px solid #1d6faa; /* 按鈕邊框樣式 */
}

#newItem-button {
  cursor: pointer; /* 鼠標樣式 */
  padding: 3px 5px; /* 按鈕內邊距 */
  border: 1px solid #1d6faa; /* 按鈕邊框樣式 */
}

.title-bar {
  display: flex; /* 橫排顯示標題和編輯按鈕 */
  justify-content: space-between; /* 空間平均分配 */
  align-items: center; /* 垂直置中 */
  background-color: #1d6faa; /* 背景顏色 */
  padding: 0px 0px 10px 15px; /* 內邊距 */
  margin-top: -5px;
}

.title-bar h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 550;
}

/* 子菜單項目的階梯式縮排 */
.sub-menu-item {
  padding-left: 20px; /* 或者根據需要調整 */
}

.collapsible,
.menu-item-alarm,
.sub-menu-item,
.content-alarm {
  background-color: #ffffff; /* 白色背景 */
}

/* 可點擊的div樣式 */
.collapsible {
  /* 添加樣式以使其看起來像一個按鈕或可點擊的元素 */
  cursor: pointer;
  padding: 5px 0px 0px 10px;
  color: #1d6faa;
  background-color: #ffffff;
  font-size: 15px;
}

/* 當前樣式 */
.active .content-alarm {
  display: block;
}

/* 箭頭樣式 */
.arrow {
  display: inline-block;
  transition: transform 0.2s; /* 平滑轉場效果 */
  /* 使旋轉軸心位於箭頭的中心 */
  transform-origin: center;
  line-height: 1; /* 確保箭頭與文字在同一行高 */
  vertical-align: middle; /* 垂直對齊 */
  margin-right: 5px; /* 與文字間距 */
  margin-bottom: 4px;
  cursor: pointer; /* 顯示指針光標 */
  color: #6089a7;
  font-size: 20px;
}

/* 向下的箭頭 */
.arrow.down {
  transform: rotate(90deg); /* 當展開時，圖片旋轉為向下 */
}

/* 更改箭頭方向為向右 */
.arrow-right {
  /* ... */
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
}

/* 圓點樣式和指針效果 */
.final-item {
  cursor: pointer; /* 指針效果 */
  background-color: #ffffff;
  padding-left: 40px; /* 或者根據需要調整 */
  color: #1d6faa;
}

.final-item .dot {
  height: 6px;
  width: 6px;
  background-color: #1d6faa;
  border-radius: 50%;
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 5px;
  vertical-align: middle;
}

/* 展開狀態下箭頭向下 */
.active > .collapsible > .arrow-right {
  /* ... */
  transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
}

/* 为最顶层的collapsible添加hover效果 */
.menu-item-alarm > .collapsible:hover,
.menu-item-alarm > .collapsible:hover span {
  color: #252525;
  cursor: pointer;
  transition: color 0.3s;
}

/* 为第二层级的collapsible添加hover效果 */
.menu-item-alarm .sub-menu-item > .collapsible:hover,
.menu-item-alarm .sub-menu-item > .collapsible:hover span {
  color: #252525;
  cursor: pointer;
  transition: color 0.3s;
}

/* 为final-item及其内部的dot添加hover效果 */
.menu-item-alarm .content-alarm .final-item:hover,
.menu-item-alarm .content-alarm .final-item:hover .dot {
  color: #252525;
  cursor: pointer;
  transition: color 0.3s;
}

/* 当final-item处于hover状态时，也改变其内部dot的背景色或边框色 */
.menu-item-alarm .content-alarm .final-item:hover .dot {
  background-color: #252525;
  border-color: #252525;
  transition:
    background-color 0.3s,
    border-color 0.3s;
}

/* 定义被点击的final-item的active类样式 */
.menu-item-alarm .content-alarm .final-item.active,
.menu-item-alarm .content-alarm .final-item.active .dot {
  background-color: #252525;
  border-color: #252525;
  color: #252525;
}

.menu-item-alarm {
  cursor: none;
}

.buttons-alarm {
  border: 1px solid #8f8f8f;
  padding: 1px 6px;
  padding-top: 6px;
  border-radius: 5px;
  background-color: #ffffff;
  z-index: 1;
  margin-bottom: 4px;
  margin-right: 2em;
}

.btn-new {
  display: inline-block; /* 使按鈕可行內展示，且可以設定寬高 */
  font-size: 16px;
  padding: 3px 12px; /* 可根据按钮的具体尺寸调整 */
  margin: 0;
  border: none;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap; /* 防止文本换行 */
  background-color: #ffffff;
  color: #8f8f8f;
  border: 2px solid #8f8f8f;
  width: 150px;
  text-align: center;
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}

.btn-new:hover {
  background-color: rgb(233, 233, 233);
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
  border: 2px solid rgb(105, 105, 105);
}

/* 下拉箭头样式 */
.drop-down-arrow-alarm {
  margin-left: 0px;
  position: relative;
  top: -10px; /* 微调上移，数值根据实际情况调整 */
  transform: translateY(-50%);
  font-size: 14px;
  color: #4b4b4b;
  cursor: pointer; /* 添加鼠标指针样式 */
  transition:
    background-color 0.3s,
    border 0.3s; /* 添加过渡效果 */
}

.alarm-add-machine {
  display: inline-flex; /* 使用flexbox來對齊內容 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  font-size: 22px; /* "+"號的大小 */
  line-height: 1; /* 這將使得"+"更緊湊 */
  cursor: pointer; /* 鼠標懸停時顯示手型光標 */
  border: 2px solid #949494; /* 黑色邊框 */
  border-radius: 50%; /* 圓角邊框形成圓形 */
  width: 22px; /* 容器的寬度，根據需要調整 */
  height: 22px; /* 容器的高度，根據需要調整 */
  background-color: #fff; /* 容器的背景色，根據需要調整 */
  color: #949494; /* "+"號的顏色 */
  margin-left: -22px;
  margin-top: 13px;
  float: right;
}

.alarm-add-machine:hover {
  background-color: #d8d8d8; /* 鼠標懸停時的背景色 */
  opacity: 1;
}

.machine-Info {
  width: 100%;
  padding: 7px;
  border-radius: 5px;
  border: 1px solid #ccc;
  font-size: 16px;
  color: #000;
  background-color: white; /* 确保背景色为白色 */
  position: relative;
}

.buttons-machine {
  position: relative;
  top: 0em; /* 將按鈕上移 */
  margin-left: 0em;
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
  width: 100%;
}

.modal-title-machine {
  font-size: 25px;
  margin-bottom: 10px;
}

.input-machineModel {
  background-color: #ffffff;
  border: 1px solid #ccc;
  padding: 7px;
  width: 100%;
  border-radius: 5px;
  font-size: 15px;
}

.input-machineModel::placeholder {
  font-size: 16px; /* 或您希望的大小 */
  margin-top: 1px;
}

.button.btn-new {
  display: inline-block;
  padding: 3px 12px; /* 可根据按钮的具体样式进行调整 */
  font-weight: bold;
  cursor: pointer;
  transition: width 0.3s; /* 添加宽度的过渡效果 */
  margin-right: 10px;
}

.custom-datalist-alarm {
  // display: none; /* 默认隐藏 */
  top: 0;
  left: 0;
  list-style: none; /* 去掉列表的默认样式 */
  padding: 0; /* 去掉列表的内边距 */
  margin: 0; /* 去掉列表的外边距 */
  font-weight: bold;
  color: #8f8f8f;
  overflow-y: auto; /* 啟用垂直滾動條 */
  max-height: 100px; /* 設置最大高度，可根據需要調整 */
  text-align: center;
}

/* 鼠标悬停效果 */
.custom-datalist-alarm li:hover {
  border-radius: 5px;
  cursor: pointer; /* 鼠标指针样式为手型 */
  background-color: rgb(233, 233, 233);
  color: #8f8f8f;
  border: 2px solid #8f8f8f;
  border-radius: 5px;
  font-weight: bold;
}

.custom-datalist-alarm.active {
  display: block; /* 当添加 active 类时显示 */
}

.custom-datalist-alarm li {
  width: 150px;
  overflow: hidden;
  text-decoration: none;
  padding: 0px 10px; /* 根据需要调整列表项的内边距 */
  margin-bottom: 3px;
  cursor: pointer; /* 鼠标指针样式为手型 */
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 显示省略号 */
  text-decoration: none;
}

/* 定義選中替換項目的顏色 */
.replaced-content {
  color: #266df7; /* 設置顏色為紅色，您可以根據需要更改顏色 */
}

/* ----------------- addNewMachine -------------*/
#number-popup {
  position: absolute;
  border: 1px solid #000;
  padding: 3px;
  background-color: #fff;
}

.content-box-right-alarm2 {
  position: relative;
  /* 其他样式 */
}

.number-popup-style {
  display: none; /* 初始状态隐藏 */
  position: absolute; /* 绝对定位对于父级元素 */
  top: 0; /* 顶部对齐 */
  left: calc(100% + 19px); /* 左侧与 "+" 按钮的右侧贴齐，保留15px的间隙 */
  width: 35px; /* 宽度小于中心栏位 */
  right: 50%; /* 定位到 "+" 按钮的中心 */
  height: auto; /* 高度自动 */
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 0;
  background-color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 可选：添加阴影以提升视觉效果 */
}

.arrow-btn {
  display: block;
  width: 100%; /* 按钮宽度填满弹出窗口 */
  height: 5px; /* 缩小按钮高度 */
  line-height: 5px; /* 调整行高以垂直居中箭头符号 */
  background-color: transparent; /* 去除箭头后的灰色 */
  border: none;
  cursor: pointer;
}

#number-input {
  width: 100%; /* 输入框宽度填满弹出窗口 */
  height: 20px; /* 缩小输入框高度 */
  line-height: 20px; /* 调整行高以垂直居中文本 */
  text-align: center;
  border: none; /* 去除数字框框的边框 */
  margin: 2px 0; /* 调整输入框的外边距 */
}

.popup-visible {
  visibility: visible !important; /* 重要: 确保覆盖其他样式 */
}

/* ----------------- alarm模態框 ----------------*/

/* ----------------- 心智圖 --------------------- */

/* 禁用心智图内所有元素的点击事件 */
#mindmap-container * {
  pointer-events: none;
}

.mindmap {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.mindmap > * {
  max-height: 100%;
  max-width: 100%;
}

.node {
  display: inline-block; /* 以行内块显示 */
  justify-content: center;
  align-items: center;
  position: relative;
  border: 1px solid #000; /* 边框 */
  padding: 12px 16px; /* 内填充 */
  border-radius: 6px; /* 边框圆角 */
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2); /* 阴影效果 */
}

.node.central-node {
  position: relative; /* 確保父級元素是相對定位 */
  border: 2px solid #0d64c0;
  background-color: #0d64c0; /* 中心节点背景色 */
  color: #fff; /* 中心节点文本色 */
  font-size: 16px; /* 中心节点字体大小 */
  font-weight: 600;
  cursor: pointer;
}

.node.level-1 {
  border: 2px solid #015ab9;
  background-color: #ffffff; /* 第一级分支的背景色 */
  padding: 10px 14px; /* 内填充稍小 */
  font-size: 14px; /* 字体大小稍小 */
  cursor: pointer;
}

.node.level-2 {
  border: 2px solid #5e70a3;
  background-color: #5e70a3; /* 第一级分支的背景色 */
  color: #ffffff;
  padding: 8px 12px; /* 内填充再小一些 */
  font-size: 12px; /* 字体大小再小一些 */
  cursor: pointer;
}

.node.level-3 {
  border: 1px solid #2e4688;
  background-color: #dfe4f3; /* 第一级分支的背景色 */
  padding: 6px 10px; /* 内填充更小 */
  font-size: 10px; /* 字体大小更小 */
  cursor: pointer;
}

.node.level-3:hover {
  background-color: #afb7ce; /* 第一级分支的背景色 */
  transition: visibility 0.2 ease-in-out; /* 添加过渡效果 */
}

.mind-map-page .node.level-3:hover {
  background-color: #dfe4f3; /* 重置为原始背景色 */
  transition: none; /* 移除过渡效果 */
}

.add-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid #015ab9; /* 边框 */
  background-color: #ffffff; /* Example color */
  color: #015ab9; /* Text color */
  position: absolute;
  top: 50%;
  cursor: pointer;
  z-index: 2;
  transform: translate(0, -50%);
  font-weight: 550;
  visibility: hidden; /* 默认设置为不可见 */
  transition: visibility 0.2s ease-in-out; /* 添加过渡效果 */
}

.add-btn:hover {
  background-color: #c0dbf8;
}

.delete-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 15px;
  border-radius: 50%;
  border: 2px solid #be0000;
  background-color: #ffffff;
  color: #be0000;
  position: absolute;
  cursor: pointer;
  font-weight: 550;
  visibility: hidden; /* 默认隐藏 */
}

.delete-btn:hover {
  visibility: visible; /* 鼠标悬停时显示 */
}

.connection-line:hover + .delete-btn {
  visibility: visible; /* 连接线悬停时显示 */
}

.node:hover .add-btn {
  visibility: visible; /* 鼠标悬停时设置为可见 */
}

.add-btn.left {
  left: 0;
  transform: translate(-100%, -50%);
}

.add-btn.right {
  right: 0;
  transform: translate(100%, -50%);
}

.connection-line {
  position: absolute;
  height: 1px;
  cursor: pointer;
}

.content-box-middle-alarm {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative; /* 使框框具有相對定位 */
  padding: 8px 10px 10px 20px;
  padding-right: 0px; /* 或更多的像素，視需要而定 */
  width: 95%;
  left: 3em;
  transition: width 0.3s ease-in-out; /* 平滑過渡效果 */
  border: 1px solid #b7bfcf;
  border-radius: 15px;
  background-color: transparent; /* 或者設置為父容器的背景色 */
  height: 80vh;
  top: 1em; /* 要釘選的位置，例如在頁面頂部 */
  overflow: auto;
  box-sizing: border-box; /* 確保 padding 和 border 被包括在元素的總寬度和高度內 */
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
  cursor: pointer;
}

.buttons-container-mindMap {
  position: absolute; /* 絕對定位 */
  right: 3.3%; /* 距離框框右邊的距離，可以根據需要調整 */
  display: flex;
  gap: 2px;
  top: 14vh;
}

/* ----------------- Branch create ----------------- */

/* 基本樣式設定 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

.modal-overlay-alarm {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 2;
}

.modal-condition-alarm {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px 0;
  background-color: #fff;
  width: 65%;
  height: 78%;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  flex-flow: row wrap;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); /* 水平偏移，垂直偏移，模糊距離，顏色 */
  overflow: hidden; /* 確保內部的元件不會超出這個視窗 */
}

.modal-condition-alarm > *:not(.title-line2) {
  padding-left: 25px;
  padding-right: 25px;
}

.left-box-condition-alarm {
  flex-flow: row wrap;
  position: relative;
  margin-top: 4.5vh;
  width: 30vw;
  height: 80%;
  margin-left: 2vw;
}

.left-box-condition-alarm p {
  color: rgb(100, 100, 100);
  margin-left: 0.5vw;
  margin-bottom: 0.2vh;
  font-size: 18px;
}

/* 現有的CSS樣式（含優化） */
.right-box-condition-alarm {
  flex-flow: row wrap;
  width: 60%;
  height: 100%;
  margin-left: 27vw;
  margin-top: -53.8vh;
  overflow-y: scroll;
}

.box-condition1 {
  height: 80%;
  border: 1px solid #ccc;
  border-radius: 5px;
  border-bottom: 3px solid #ccc;
}

.box-condition-alarm2 {
  height: 46.2vh;
  width: 32.5vw;
  padding: 15px 10px 0px 20px;
  border: 1px solid #a3a3a3;
  border-radius: 3px;
  overflow-y: scroll;
}

#search {
  width: 100%;
  padding: 8px;
  margin-bottom: 5px;
  border: 1px solid #a3a3a3;
  background-color: #f0f0f0;
  border-radius: 5px;
  outline: none;
  font-size: 16px;
}

.scroll-box {
  overflow-y: scroll;
  height: 87.8%;
  background-color: #ffffff;
  border: 1px solid #a3a3a3;
  border-radius: 5px;
}

.condition-item {
  border: 1px solid #b9b9b9;
  padding: 3px 0px 0px 10px;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
  color: black;
}

.condition-item:hover {
  background-color: #e4e4e4;
  transition: background-color 0.2s;
}

.icon {
  cursor: grab;
  color: #a0a0a0;
  width: 5%;
  font-size: 25px;
  padding: 0px 25px 0px 0px;
}

.icon:active {
  cursor: grabbing;
}

.modal-title-branch {
  font-size: 25px;
  margin-bottom: 10px;
}

.form-group-condition {
  display: flex;
  align-items: center; /* 這將確保標籤和欄位在垂直方向上居中對齊 */
  margin-bottom: 16px; /* 调整垂直间距的大小 */
}

.branchItem {
  margin-left: -4.7vw;
}

/* 自定义选择框容器样式 */
.custom-select-alarm1 {
  width: calc(100% - 100px); /* 調整寬度，減去<label>的寬度 */
  display: inline-block;
  position: relative;
  width: 9vw;
}

.custom-select-alarm2 {
  width: calc(100% - 80px); /* 調整寬度，減去<label>的寬度 */
  display: inline-block;
  position: relative;
  width: 15vw;
  padding-left: 3px;
}

.buttons-condition-alarm {
  position: relative;
  right: 1vw;
  top: 3.5vh;
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}

.edit-container-branch {
  position: fixed; /* 固定定位 */
  top: 14vh; /* 距離框框上邊的距離，可以根據需要調整 */
  padding-right: 3.8vw;
  width: 50%;
  right: 0; /* 設定與 .content-box-left-alarm 左邊距相同的值 */
  display: flex;
  justify-content: flex-end; /* 添加此行以將按鈕對齊到右側 */
}

.editBranch-button {
  border-radius: 5px;
  font-size: 15px;
  font-weight: 550;
  background-color: #ffffff; /* 背景顏色 */
  color: #1daaa3;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
  margin-left: 3px;
}

.editBranch-button:hover {
  color: #ffffff; /* 背景顏色 */
  background-color: #1daaa3;
  transition: width 0.5s ease-in-out; /* 平滑過渡效果 */
}

.editBranch-button {
  cursor: pointer; /* 鼠標樣式 */
  padding: 1px 3px; /* 按鈕內邊距 */
  border: 2px solid #1daaa3; /* 按鈕邊框樣式 */
  right: 0;
}

.saveBranch-button {
  border-radius: 5px;
  font-size: 15px;
  font-weight: 550;
  background-color: #ffffff; /* 背景顏色 */
  color: #266df2;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.saveBranch-button:hover {
  color: #ffffff; /* 背景顏色 */
  background-color: #266df2;
  transition: width 0.5s ease-in-out; /* 平滑過渡效果 */
}

.saveBranch-button {
  cursor: pointer; /* 鼠標樣式 */
  padding: 1px 3px; /* 按鈕內邊距 */
  border: 2px solid #266df2; /* 按鈕邊框樣式 */
  right: 0;
}

.cancelBranch-button {
  border-radius: 5px;
  font-size: 15px;
  font-weight: 550;
  background-color: #ffffff; /* 背景顏色 */
  color: #f44336;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.cancelBranch-button:hover {
  color: #ffffff; /* 背景顏色 */
  background-color: #f44336;
  transition: width 0.5s ease-in-out; /* 平滑過渡效果 */
}

.cancelBranch-button {
  cursor: pointer; /* 鼠標樣式 */
  padding: 1px 3px; /* 按鈕內邊距 */
  border: 2px solid #f44336; /* 按鈕邊框樣式 */
  right: 0;
}

#editBranchButtons button:first-child {
  margin-right: 3px; /* 在取消按鈕的右側添加間距 */
}

.red-star-branch {
  position: relative; /* 讓偽元素的絕對定位參考此元素 */
}

.red-star-branch::before {
  content: '★'; /* 使用星星符號 */
  position: absolute;
  left: -12px;
  top: 2px;
  color: red; /* 設定顏色為紅色 */
  font-size: 12px; /* 設定字體大小 */
}

.minus-icon {
  display: inline-flex; /* 使用flexbox來對齊內容 */
  position: absolute;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  font-size: 22px; /* "+"號的大小 */
  line-height: 1; /* 這將使得"+"更緊湊 */
  cursor: pointer; /* 鼠標懸停時顯示手型光標 */
  border: 1px solid #f44336; /* 黑色邊框 */
  border-radius: 50%; /* 圓角邊框形成圓形 */
  width: 15px; /* 容器的寬度，根據需要調整 */
  height: 15px; /* 容器的高度，根據需要調整 */
  background-color: #3f3f3f; /* 容器的背景色，根據需要調整 */
  color: #f44336; /* "+"號的顏色 */
  left: 0; /* 靠近最左邊 */
  transform: translate(0, -50%); /* 確保完全居中 */
  margin-top: 11px;
  padding-bottom: 2px;
  float: right;
}

.minus-icon:hover {
  background-color: #f1b3af; /* 鼠標懸停時的背景色 */
  opacity: 2;
}

/* 適當調整間距 */
.box-condition-alarm2 .form-group-condition {
  position: relative; /* 如果需要的話 */
  padding-left: 20px; /* 根據實際情況調整，為 minus-icon 騰出空間 */
}
/* ------------------ 100%網站介面設定 ---------------- */
.content-box-left,
.content-box-left2,
.content-box-right,
.content-box-right-alarm,
.content-box-right-alarm2,
.content-box-middle,
.content-box-middle-alarm,
.content-box-left-alarm {
  height: 76vh;
  top: 0;
}

.content-box-left,
.content-box-left2 {
  width: 25vw;
}

/* ----------------- All @media ----------------- */

/* 媒体查询示例 */
@media (min-width: 1500px) {
  .scroll-box {
    height: 90.8%;
  }

  .content-box-left,
  .content-box-left2 {
    width: 15vw;
  }

  .content-box-right {
    width: 100%;
  }

  .content-box-right-alarm,
  .content-box-right-alarm2 {
    width: 61.3vw; /* 在小屏幕上占据更多空间 */
  }

  .content-box-left,
  .content-box-left2,
  .content-box-right,
  .content-box-right-alarm,
  .content-box-right-alarm2,
  .content-box-middle,
  .content-box-middle-alarm,
  .content-box-left-alarm {
    height: 81vh;
  }

  .content-box-left-alarm {
    padding: 0px;
    width: 22vw; /* 使用視口寬度的百分比 */
  }

  .content-box-right-alarm {
    position: relative; /* 使框框具有相對定位 */
    width: 100%;
  }

  .content-box-middle {
    // width: 84.1vw;
    width: 100%;
  }

  .edit-container {
    position: absolute;
    top: 14vh;
    left: 16vw;
    right: 0;
  }

  .form-group-condition {
    margin-bottom: 30px; /* 调整垂直间距的大小 */
  }

  .buttons-container {
    margin-right: 0vw; /* 距離框框右邊的距離，可以根據需要調整 */
    padding-top: 1.2em; /* 距離框框上邊的距離，可以根據需要調整 */
  }

  .buttons-container-item {
    top: 4.5vh;
  }

  .edit-container {
    top: 12vh; /* 距離框框上邊的距離，可以根據需要調整 */
  }

  .buttons-container-mindMap {
    top: 10.5vh;
  }

  .alarm-add-machine {
    margin-top: 15px;
  }

  .modal {
    width: 22vw;
  }

  .left-box-condition-alarm {
    margin-top: 6vh;
    width: 30vw;
    height: 80%;
    margin-left: 2vw;
  }

  .right-box-condition-alarm {
    margin-left: 27vw;
    margin-top: -55.5vh;
  }

  .box-condition-alarm2 {
    height: 46.8vh;
    width: 32.9vw;
    padding: 20px 0px 0px 15px;
    overflow-y: scroll;
  }

  .edit-container-branch {
    top: 13.5vh; /* 距離框框上邊的距離，可以根據需要調整 */
  }

  .form-group-condition label {
    width: 5vw; /* 這個寬度可以根據您的需求進行調整，以確保所有的標籤都對齊 */
  }

  .btn-showMachine {
    width: 10vw;
    margin-left: 5px;
    padding-left: 2px;
  }
}

.custom-select-rounded {
  .ant-select-selector {
    border-radius: 8px !important; // 设置为你想要的圆角大小
  }
}

.image-container {
  display: flex; /* 使圖片水平排列 */
  align-items: flex-start; /* 對齊圖片頂部 */
  flex-wrap: wrap; /* 允許多行，如果需要 */
}

.uploaded-image {
  max-width: 150px; /* 最大寬度限制 */
  max-height: 150px; /* 最大高度限制 */
}

@media print {
  // body {
  //     width: 100%;
  //     max-width: 780px; /* 大约A4纸张宽度的像素值，考虑打印边距 */
  //     font-size: 90%; /* 根据需要调整字体大小 */
  // }
  body,
  .page-outline {
    height: auto; // 确保容器高度自适应内容
    overflow: hidden; // 避免内容溢出造成页面扩展
    padding-bottom: 0; // 减少底部留白
  }
  /* 调整或隐藏不适合打印的元素 */
  .non-printable {
    display: none;
  }

  .prepare-pdf {
    padding-top: 5px; // 细微调整以避免头部内容被切割
    margin-bottom: -10px; // 减少底部不必要的空白
  }

  /* 定义用于PDF生成的样式 */
  .prepare-pdf .page-outline {
    border: none !important; /* 移除外边线 */
  }

  .file-title,
  .step1 {
    margin-top: 5px; // 微调顶部边距以防文字下移
    padding: 0; // 移除不必要的内边距
  }

  /* 确保容器具有固定尺寸 */
  .page-outline {
    width: 595pt; /* A4纸张宽度 */
    height: 842pt; /* A4纸张高度 */
    box-sizing: border-box; /* 包括padding和border在内计算宽度和高度 */
  }
}

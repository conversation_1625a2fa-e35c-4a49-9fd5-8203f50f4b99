{"name": "armanagementfront", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/colors": "^7.0.2", "@ant-design/icons": "^5.3.6", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^5.15.19", "@mui/material": "^5.15.19", "@react-pdf/renderer": "^3.4.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "admin-lte": "^3.2.0", "antd": "^5.17.0", "axios": "^1.7.7", "classnames": "^2.5.1", "dagre": "^0.8.5", "docx": "^8.5.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "i18next": "^22.4.15", "i18next-http-backend": "^2.2.0", "immer": "^10.1.1", "jsmind": "^0.8.4", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.3", "jwt-decode": "^4.0.0", "linkifyjs": "^4.1.3", "lucide-react": "^0.419.0", "mocha": "^0.8.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^1.6.8", "react-debounce-input": "^3.3.0", "react-dom": "^18.2.0", "react-highlight-words": "^0.20.0", "react-i18next": "^12.2.2", "react-linkify": "^1.0.0-alpha", "react-markdown": "^9.0.1", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-string-replace": "^1.1.0", "react-syntax-highlighter": "^15.5.0", "react-to-print": "^2.15.1", "react-toastify": "^9.1.3", "reactflow": "^11.10.4", "rehype-raw": "^7.0.0", "remark-parse": "^11.0.0", "sass": "^1.71.1", "semantic-ui-css": "^2.5.0", "semantic-ui-react": "^2.1.5", "simple-react-validator": "^1.6.2", "styled-components": "^5.3.9", "tailwind": "^4.0.0", "universal-cookie": "^4.0.4", "web-vitals": "^2.1.4", "zustand": "^4.5.2"}, "scripts": {"start": "cross-env PORT=3001 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "prettify": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss}\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-transform-private-property-in-object": "^7.24.7", "cross-env": "^7.0.3", "eslint-config-react-app": "^7.0.1", "jest-editor-support": "^31.0.1", "prettier": "^3.4.2"}, "prettier": {"semi": true, "singleQuote": true, "printWidth": 80, "tabWidth": 2, "trailingComma": "es5"}}
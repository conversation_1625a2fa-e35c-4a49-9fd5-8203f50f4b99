# 基礎運行時映像，使用 .NET 8.0
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app

# 構建環境，使用 .NET 8.0 SDK
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 單獨複製項目文件進行依賴還原，以便利用 Docker 層緩存
COPY ["ARManagement/ARManagement.csproj", "ARManagement/"]
COPY ["Models/Models.csproj", "Models/"]
RUN dotnet restore "ARManagement/ARManagement.csproj"

# 複製剩餘源代碼，並進行構建
COPY . .
WORKDIR "/src/ARManagement"
RUN dotnet build "ARManagement.csproj" -c Release -o /app/build

# 發佈應用到 /app/publish 文件夾
FROM build AS publish
RUN dotnet publish "ARManagement.csproj" -c Release -o /app/publish

# 最終運行環境
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# 創建 PDF 備份目錄
RUN mkdir -p /app/CustomerPDF_BackUp && chmod 777 /app/CustomerPDF_BackUp

# 暴露必要端口（80 和 443）
EXPOSE 80
EXPOSE 443

# 執行應用
ENTRYPOINT ["dotnet", "ARManagement.dll"]

# 部署設定檔案保護清單

## 🚨 不能覆蓋的檔案快速清單

**以下檔案絕對不能覆蓋：**

1. `arfront_main\public\appsetting.js` - 前端API連線設定
2. `arfront_main\nginx.conf` - Nginx伺服器設定
3. `arfront_main\Dockerfile` - 前端Docker建構設定
4. `ARManagement\appsettings.json` - 後端應用程式主要設定
5. `ARManagement\appsettings.Development.json` - 開發環境設定
6. `ARManagement\Dockerfile` - 後端Docker建構設定
7. `init.sql` - 資料庫初始化腳本
8. `default.conf` - Nginx代理伺服器設定
9. `Dockerfile-backend` - 後端專用Dockerfile
10. `docker_images\*.tar` - 所有Docker映像檔案
11. `部屬注意事項.txt` - 部署流程說明
12. `下載字體檔案代碼.txt` - 字體檔案下載指令
13. `專案修改說明書.md` - 專案變更記錄
14. `更新內容.txt` - 更新內容記錄

---

## 📋 概述
本文件列出在將開發環境檔案複製到部署目錄時，**絕對不能覆蓋**的重要設定檔案。這些檔案包含部署環境專用的設定，覆蓋後會導致Docker容器無法正常運行。

**部署目錄路徑：** `C:\Users\<USER>\Desktop\DockerGUI_webar_removeMail\webar_project`

---

## 🚨 絕對不能覆蓋的檔案清單

### 🌐 前端設定檔案

#### 1. API設定檔案
- **檔案路徑：** `arfront_main\public\appsetting.js`
- **用途：** 前端API連線設定
- **重要設定：**
  ```javascript
  var apiUrl = 'http://localhost:10002';
  var domain = 'localhost';
  ```
- **⚠️ 注意：** 部署環境使用Docker內部網路設定，不可改為開發環境的設定

#### 2. Nginx設定檔案
- **檔案路徑：** `arfront_main\nginx.conf`
- **用途：** 前端伺服器路由設定
- **重要設定：** Docker容器內的Nginx伺服器設定

#### 3. 前端Docker設定
- **檔案路徑：** `arfront_main\Dockerfile`
- **用途：** 前端Docker映像建構設定
- **重要設定：** 容器建構流程與端口設定

---

### 🔧 後端設定檔案

#### 4. 主要應用設定
- **檔案路徑：** `ARManagement\appsettings.json`
- **用途：** 後端應用程式主要設定
- **重要設定：**
  ```json
  {
    "DBConfig": {
      "ConnectionString": "Server=db;Port=5432;User Id=postgres;Password=*********;Database=AREditor;"
    },
    "SMTP": {
      "Password": "{SMTP_PASSWORD}"
    }
  }
  ```
- **⚠️ 注意：** 資料庫連線使用Docker service名稱 `db`

#### 5. 開發環境設定
- **檔案路徑：** `ARManagement\appsettings.Development.json`
- **用途：** 開發環境專用設定

#### 6. 後端Docker設定
- **檔案路徑：** `ARManagement\Dockerfile`
- **檔案路徑：** `Dockerfile-backend`
- **用途：** 後端Docker映像建構設定

---

### 🗄️ 資料庫設定檔案

#### 7. 資料庫初始化腳本
- **檔案路徑：** `init.sql`
- **用途：** PostgreSQL資料庫初始化
- **重要設定：** 所有資料表建立語法、初始資料
- **⚠️ 注意：** 包含完整的資料庫結構，覆蓋會導致資料庫初始化失敗

---

### 🐳 Docker相關檔案

#### 8. 伺服器設定
- **檔案路徑：** `default.conf`
- **用途：** Nginx代理伺服器設定

#### 9. Docker映像檔案
- **目錄路徑：** `docker_images\`
- **檔案列表：**
  - `postgres.tar`
  - `webar-backend-linux.tar`
  - `webar-backend.tar`
  - `webar-frontend-linux.tar`
  - `webar-frontend.tar`
- **用途：** 預先建立的Docker映像檔案

---

### 📝 說明文件

#### 10. 部署指南
- **檔案路徑：** `部屬注意事項.txt`
- **用途：** 部署流程說明與注意事項

#### 11. 字體設定
- **檔案路徑：** `下載字體檔案代碼.txt`
- **用途：** 字體檔案下載指令

#### 12. 專案文件
- **檔案路徑：** `專案修改說明書.md`
- **檔案路徑：** `更新內容.txt`
- **用途：** 專案變更記錄

---

## ✅ 可以安全覆蓋的檔案類型

### 前端可覆蓋檔案
- `src\` 目錄下的所有React元件
- `src\components\` 所有元件檔案
- `src\pages\` 所有頁面檔案
- `src\styles\` 所有樣式檔案
- `src\utils\` 工具函數
- `src\contexts\` Context檔案
- `src\zustand\` 狀態管理檔案
- `package.json` (需注意依賴版本)

### 後端可覆蓋檔案
- `Controllers\` 目錄下的所有控制器
- `Models\` 目錄下的所有模型檔案
- `BaseRepository\` 資料存取層
- `Helpers\` 工具類別
- 業務邏輯相關的.cs檔案

---

## 🔄 安全覆蓋操作流程

### 步驟 1：備份重要設定檔案
```bash
# 建議建立備份資料夾
mkdir backup_config
# 複製重要設定檔案到備份資料夾
copy "arfront_main\public\appsetting.js" "backup_config\"
copy "ARManagement\appsettings.json" "backup_config\"
copy "init.sql" "backup_config\"
```

### 步驟 2：執行選擇性複製
1. **前端檔案複製**
   - 僅複製 `src\` 目錄下的檔案
   - 保留 `public\appsetting.js`
   - 保留 `Dockerfile` 和 `nginx.conf`

2. **後端檔案複製**
   - 複製 `Controllers\`、`Models\`、`Helpers\` 等業務邏輯目錄
   - 保留 `appsettings.json`
   - 保留 `Dockerfile`

### 步驟 3：驗證設定檔案
確認以下關鍵設定未被變更：
- [ ] 前端API URL設定正確
- [ ] 後端資料庫連線字串使用Docker service名稱
- [ ] Docker設定檔案完整
- [ ] init.sql檔案未被覆蓋

### 步驟 4：測試部署
```bash
cd C:\Users\<USER>\Desktop\DockerGUI_webar_removeMail\webar_project
docker compose down
docker compose build --no-cache
docker compose up -d
```

---

## ⚠️ 重要提醒

1. **環境差異**
   - 開發環境使用具體IP位址和端口
   - 部署環境使用Docker內部網路和service名稱

2. **端口設定一致性**
   - 前端 `appsetting.js` 的端口必須與 docker-compose.yaml 對應
   - 後端設定的端口必須與Docker expose設定一致

3. **資料庫連線**
   - 部署環境：`Server=db` (Docker service名稱)
   - 開發環境：`Server=localhost` 或具體IP

4. **檔案路徑**
   - 部署環境的檔案儲存路徑可能與開發環境不同
   - 確認 `FileStorageSettings` 路徑設定

---

## 📞 緊急處理

如果不小心覆蓋了重要設定檔案：

1. **立即停止Docker服務**
   ```bash
   docker compose down
   ```

2. **從備份資料夾還原設定檔案**
   ```bash
   copy "backup_config\appsetting.js" "arfront_main\public\"
   copy "backup_config\appsettings.json" "ARManagement\"
   ```

3. **重新建構並啟動**
   ```bash
   docker compose build --no-cache
   docker compose up -d
   ```

---

**文件建立時間：** 2025-01-28 15:00  
**適用專案：** WebAR Management System  
**維護人員：** 開發團隊  

> 💡 **提示：** 建議在每次大規模檔案更新前，都先檢查此清單，確保部署環境的穩定性。

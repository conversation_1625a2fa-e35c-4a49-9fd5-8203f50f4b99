@charset "UTF-8";
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

h2 {
  margin: 0; /* 增加下邊距來與下方內容分隔 */
  padding: 10px 0 0 0 !important;
}

.container-gpt {
  display: flex; /* 使用flex佈局 */
  justify-content: center;
  border-radius: 10px;
  margin-bottom: 50px; /* 預留的底部空間 */
}

.content-wrapper-gpt {
  border: 2px solid #b7bfcf;
  border-radius: 20px;
  padding: 5px;
  margin: 2% auto; /* 上下左右邊距設為視口寬度的 2%，自動居中 */
  height: 78vh;
  width: 96%;
}

/* 左側聊天區域的樣式 */
.chat-area {
  width: 20%; /* 占總寬度的30% */
  margin: 10px 5px 10px 10px; /* 外邊距為10px */
  padding: 0px; /* 內邊距為10px */
  // border: 1px solid #ddd; /* 邊框樣式 */
  border: none;
  display: flex;
  flex-direction: column; /* 子元素垂直排列 */
  height: calc(78vh - 30px); /* 計算整體高度，減去上下外邊距 */
}

/* 右側GPT回應區域的樣式 */
.gpt-response-area {
  position: relative;
  width: 80%; /* 占總寬度的70% */
  margin: 10px; /* 外邊距為10px */
  padding: 0px; /* 內邊距為10px */
  // border: 1px solid #ddd; /* 邊框樣式 */
  border: none;
  display: flex;
  flex-direction: column; /* 子元素垂直排列 */
  height: calc(78vh - 30px); /* 計算整體高度，減去上下外邊距 */
  overflow-y: auto; /* 允許垂直方向上的滾動 */
}

/* 聊天輸入框的樣式 */
.chat-input {
  width: 100%; /* 寬度與父元素一致 */
  resize: none; /* 禁止調整大小 */
  border-radius: 3px;
  padding: 8px; /* 內部文本的間距（包括 placeholder） */
  box-sizing: border-box; /* 確保 padding 不會影響到總寬高 */
  overflow-y: auto; /* 添加滾動條 */
  font-size: 16px;
  border: 1px solid #000000; /* 邊框樣式 */
}

/* 控制按鈕的容器樣式，將按鈕垂直排列 */
.chat-controls {
  display: flex;
  flex-direction: column;
  padding: 0; /* 去除內邊距 */
  justify-content: space-between; /* 平均分配子元素 */
  flex-grow: 1; /* 讓 .chat-controls 填滿所有剩餘空間 */
}

/* 鼠標悬停样式 */
.chat-controls > *:hover {
  background-color: #d8d8d8;
  transition: background-color 0.5s ease; /* 修改過渡效果屬性 */
}

/* 公用樣式設置給 Select 元件和 Button */
.chat-controls > * {
  flex-grow: 1; /* 讓所有子元件均分剩餘空間 */
  width: 100%; /* 寬度與父元素一致 */
  min-height: 6vh; /* 設定最小高度 */
  text-align: center; /* 文字置中 */
  border-radius: 5px;
  border: solid 1px black;
  margin: 5px 0px 0px 0px; /* 調整上下邊距 */
}

/* 用戶問題展示區的樣式 */
.user-question {
  background-color: #f7f7f7; /* 背景色 */
  padding: 2px 10px; /* 內邊距 */
  margin-bottom: 10px; /* 與下一個元素的間距 */
  height: 6em;
  border-radius: 5px;
  border: 1px solid #ddd; /* 邊框樣式 */
  overflow-y: auto; /* 超出部分顯示滾動條 */
  justify-content: space-between; /* 平均分配子元素 */
}

.user-question p {
  color: black;
  font-size: 16px;
  text-align: left; /* 文字對齊左側 */
  margin: 5px 0px;
}

.user-question:hover {
  border: 1px solid #29b0ff; /* 邊框樣式 */
  transition: border-color 0.5s; /* Transition effect */
}

/* GPT回應展示區的樣式 */
.gpt-response {
  background-color: #f7f7f7; /* 背景色 */
  padding: 5px 5px 5px 15px; /* 內邊距 */
  flex: 1; /* 占滿所有可用空間 */
  overflow-y: auto; /* 超出部分顯示滾動條 */
  border-radius: 5px;
  border: 1px solid #cbcbcb; /* 邊框樣式 */
}

.gpt-response p {
  padding: 0px; /* 內邊距 */
  margin: 0px 0px;
  color: black;
  text-align: left; /* 文字對齊左側 */
  font-size: 16px;
}

// 添加列表元素的樣式，以確保正確對齊
.gpt-response ul {
  list-style-position: inside;
  padding-left: 0; // 移除預設的內邊距以保持對齊
}

.gpt-response li {
  text-align: left; // 確保文字對齊左側
  margin-left: 16px; // 或根據您的需求調整，以實現所需的對齊
}

// 添加標題的樣式，以使其更顯眼
.gpt-response h1 {
  font-size: 30px; // 或根據您的需求調整
  font-weight: bold; // 使文字顯示為粗體
}

.gpt-response:hover {
  border: 1px solid #29b0ff; /* 邊框樣式 */
  transition: border-color 0.5s; /* Transition effect */
}

.gptLabel {
  font-size: 16px;
  text-align: left; /* 文字對齊左側 */
  margin: 0;
}

.gptContent {
  font-size: 16px;
  text-align: left; /* 文字對齊左側 */
} /*# sourceMappingURL=GPT.css.map */

// 自定義 h2 標題的樣式
.custom-header {
  font-weight: bold; // 粗體
  font-size: 1.5em; // 較大的字體大小
  // 可以根據需求增加更多樣式，例如邊距、顏色等
}

.image {
  width: 40%; /* 讓圖片寬度填滿容器，並保持原始比例 */
  // height: 27.6vh; /* 高度自動調整以保持圖片比例 */
  height: 40%;
  border-radius: 10px;
  border: 1px solid grey;
  margin: 0px 5px 0px 5px;
  padding: 0;
}

.gpt-response-area {
  // 定義文字區域的樣式
  .gpt-response {
    // 連結樣式
    a {
      color: #137bf1; // 深藍色的色碼
      text-decoration: underline; // 顯示下劃線

      &:hover {
        color: #0257b8; // 深藍色的色碼
        text-decoration: underline; // 滑鼠移過時顯示下劃線
      }
    }
  }
}

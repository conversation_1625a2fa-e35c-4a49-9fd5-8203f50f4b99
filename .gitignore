# ===========================================
# WebAR_removeEmail 專案 .gitignore
# 包含 ASP.NET Core + React.js
# ===========================================

# ===== .NET Core / C# =====
## Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

## Visual Studio files
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs

## Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

## Benchmark Results
BenchmarkDotNet.Artifacts/

## .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

## StyleCop
StyleCopReport.xml

## Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

## Entity Framework
*.edmx.diagram
*.edmx.sql

# ===== Node.js / React =====
## Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

## Production build
build/
dist/

## Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

## IDE files
.vscode/
.idea/
*.swp
*.swo

## OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===== 專案特定排除 =====
## 已編譯的靜態檔案 (保留原始碼)
ARManagement/bin/
ARManagement/obj/
Models/bin/
Models/obj/

## React build 結果
armanagementfront/build/
armanagementfront/node_modules/

## 已發布的檔案
ARManagement/wwwroot/upload/*/
!ARManagement/wwwroot/upload/web.config

## 暫存PDF檔案
ARManagement/StoredPdfs/*.pdf

## 系統生成的壓縮檔
*.tar
*.zip

## 向量資料庫檔案 (chromadb)
armanagementfront/systex/

# ===== 保留重要文件 =====
## 確保這些檔案被包含
!appsettings.json
!appsettings.Development.json
!init.sql
!專案修改說明書.md
!docker-compose.yaml
!default.conf
!ARManagement/Front/
!armanagementfront/public/
!armanagementfront/src/

## 保留配置檔案
!*.config
!*.json
!*.md

# ===== 安全性相關 =====
## 排除敏感資訊
*.key
*.pem
*.p12
*.pfx
appsettings.Production.json

## 排除環境變數檔案
.env*
!.env.example 
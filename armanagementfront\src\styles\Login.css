/* 登入頁面專用樣式 - 柔和配色與動畫效果 */

/* 整體背景設計 */
.login-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 背景波浪動畫 */
.login-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(ellipse at 20% 50%, rgba(103, 126, 234, 0.08) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(118, 75, 162, 0.08) 0%, transparent 50%),
    radial-gradient(ellipse at 40% 80%, rgba(143, 164, 211, 0.08) 0%, transparent 50%);
  animation: waveFloat 15s ease-in-out infinite;
  pointer-events: none;
}

.login-page::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(45deg, transparent 30%, rgba(103, 126, 234, 0.05) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(118, 75, 162, 0.05) 50%, transparent 70%);
  background-size: 400px 400px, 300px 300px;
  animation: waveSlide 20s linear infinite;
  pointer-events: none;
}

@keyframes waveFloat {
  0%, 100% { 
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  33% { 
    transform: translateY(-15px) scale(1.02);
    opacity: 0.8;
  }
  66% { 
    transform: translateY(-5px) scale(0.98);
    opacity: 0.7;
  }
}

@keyframes waveSlide {
  0% { 
    background-position: 0% 0%, 100% 100%;
  }
  50% { 
    background-position: 100% 50%, 0% 50%;
  }
  100% { 
    background-position: 0% 0%, 100% 100%;
  }
}

/* 登入盒子容器 */
.login-box {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  position: relative;
  z-index: 1;
}

/* 主卡片樣式 */
.login-box .card {
  background: rgba(248, 250, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  max-width: 720px;
  width: 100%;
  animation: cardSlideIn 0.8s ease-out;
  transition: all 0.3s ease;
}

.login-box .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片頭部 */
.login-box .card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px 20px 0 0;
  padding: 20px 25px;
  text-align: center;
  border: none;
}

.login-box .card-header h1 {
  color: white;
  font-weight: 500;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: 1px;
  font-size: 22px !important;
  transition: all 0.3s ease;
}

.login-box .card:hover .card-header h1 {
  text-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.2),
    0 0 15px rgba(255, 255, 255, 0.3);
  letter-spacing: 1.5px;
  transform: scale(1.02);
}

/* 卡片主體 */
.login-box .card-body {
  padding: 50px;
}

.login-box-msg {
  font-size: 18px;
  color: #667eea;
  text-align: center;
  margin-bottom: 35px;
  font-weight: 400;
}

/* 輸入框組合樣式 */
.custom-input-group {
  margin-bottom: 35px;
  position: relative;
}

.custom-input-group .form-control {
  border: 2px solid #e3e9f0;
  border-radius: 12px;
  padding: 18px 55px 18px 20px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: rgba(252, 253, 255, 0.9);
  color: #4c5aa0 !important;
}

.custom-input-group .form-control::placeholder {
  color: #8fa4d3;
}

.custom-input-group .form-control:focus {
  border-color: #8fa4d3;
  box-shadow: 0 0 0 0.2rem rgba(143, 164, 211, 0.25);
  background: white;
  transform: translateY(-1px);
}

.custom-input-group .input-group-text {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #8fa4d3;
  z-index: 5;
  transition: all 0.3s ease;
}

.custom-input-group .form-control:focus + .input-group-append .input-group-text {
  color: #667eea;
  transform: translateY(-50%) scale(1.1);
}

/* 錯誤訊息樣式 */
.custom-error-message {
  background: linear-gradient(135deg, #ffb3ba 0%, #ffdfdf 100%);
  color: #8b2635;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  margin-top: 8px;
  animation: errorSlideIn 0.3s ease-out;
  border-left: 4px solid #d67982;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 登入按鈕樣式 */
.custom-login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  padding: 15px 40px;
  font-size: 18px;
  font-weight: 500;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 80px;
}

.custom-login-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.custom-login-btn:hover:before {
  left: 100%;
}

.custom-login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.custom-login-btn:active {
  transform: translateY(0);
}

.custom-login-btn:disabled {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: none;
  box-shadow: none;
  opacity: 0.8;
}

/* Spinner 動畫樣式 */
.custom-spinner {
  animation: customSpin 1s linear infinite;
}

@keyframes customSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 響應式設計 */
@media (max-width: 576px) {
  .login-box {
    padding: 15px;
  }
  
  .login-box .card {
    margin: 10px;
  }
  
  .login-box .card-body {
    padding: 20px;
  }
  
  .login-box .card-header h1 {
    font-size: 20px;
  }
}

/* Toast 訊息美化 */
.Toastify__toast {
  border-radius: 16px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  padding: 16px 20px !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  min-height: auto !important;
  margin-bottom: 8px !important;
}

.Toastify__toast--success {
  background: linear-gradient(135deg, rgba(134, 239, 172, 0.85) 0%, rgba(187, 247, 208, 0.85) 100%) !important;
  color: #15803d !important;
  border-left: 4px solid #22c55e !important;
  box-shadow: 0 12px 30px rgba(34, 197, 94, 0.25) !important;
}

.Toastify__toast--error {
  background: linear-gradient(135deg, rgba(252, 165, 165, 0.85) 0%, rgba(254, 202, 202, 0.85) 100%) !important;
  color: #dc2626 !important;
  border-left: 4px solid #ef4444 !important;
  box-shadow: 0 12px 30px rgba(239, 68, 68, 0.25) !important;
}

.Toastify__toast-icon {
  width: 24px !important;
  height: 24px !important;
  margin-right: 12px !important;
}

.Toastify__close-button {
  color: inherit !important;
  opacity: 0.7 !important;
  transition: opacity 0.3s ease !important;
}

.Toastify__close-button:hover {
  opacity: 1 !important;
}

.Toastify__progress-bar {
  height: 4px !important;
  border-radius: 0 0 16px 16px !important;
  opacity: 0.8 !important;
}

.Toastify__progress-bar--success {
  background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%) !important;
}

.Toastify__progress-bar--error {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%) !important;
}

/* 悬停效果 */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* 漸入動畫 */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 輸入框聚焦動畫 */
.input-focus-animation {
  position: relative;
}

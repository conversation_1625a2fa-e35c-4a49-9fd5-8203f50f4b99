import React, { useMemo } from 'react';
import {
  Document,
  Page,
  Text,
  View,
  Image,
  StyleSheet,
  Font,
} from '@react-pdf/renderer';

// // 註冊字體
// Font.register({
//   family: 'NotoSansCJK',
//   src: '/fonts/NotoSansTC-Regular.ttf',
//   format: 'truetype',
// });

// 使用本地字體
Font.register({
  family: 'NotoSansCJK',
  src: '/fonts/NotoSansTC-Regular.otf', // 使用本地路徑
});

// 備用字體設置（如果需要的話）
Font.register({
  family: 'Fallback',
  src: '/fonts/NotoSansTC-Regular.otf', // 使用相同的字體作為備用
});

// 工具函數
const safeJsonParse = (jsonString) => {
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    return jsonString;
  }
};

// 修改 extractFileName 函數
const extractFileName = (path) => {
  if (!path) return '';

  // 如果是 JSON 字符串，先解析它
  let processedPath = path;
  try {
    if (
      typeof path === 'string' &&
      (path.startsWith('[') || path.startsWith('['))
    ) {
      processedPath = JSON.parse(path)[0]; // 取第一個元素
    }
  } catch (e) {
    processedPath = path;
  }

  // 如果是數組，取第一個元素
  if (Array.isArray(processedPath)) {
    processedPath = processedPath[0];
  }

  // 移除路徑，只保留文件名
  return processedPath.split('\\').pop().split('/').pop();
};

// 修改 formatSteps 函數，使其與 PDFContent.js 一致
const formatSteps = (content) => {
  if (!content) return [];

  const parts = [];
  let lastIndex = 0;

  // 更新正則表達式，包括星號(*)、括號內編號、數字序號後的點以及英文段落結束後緊跟的中文字符開始
  const regex =
    /(\*\s)|\(\d+\)\s|(?<!remark\s)(?<!Remark\s)(?<!Illustration\s)(?<!illustration\s)(?<!敘述\s)(?<!備註\s)(?<!part\s)(\d+\.)\s(?![\d-])|[A-Za-z0-9]\.(?=[\u4e00-\u9fa5])/g;

  let match;
  while ((match = regex.exec(content)) !== null) {
    let index = match.index;
    if (
      match[0].includes('*') ||
      match[0].includes('(') ||
      (match[0].match(/\d+\./) &&
        !content
          .substring(lastIndex, index)
          .match(
            /(remark|Remark|Illustration|illustration|敘述|備註|part)\s\d+\.$/i
          ))
    ) {
      if (index !== 0) {
        parts.push(content.substring(lastIndex, index).trim());
        lastIndex = index;
      }
    } else if (match[0].match(/[A-Za-z0-9]\.(?=[\u4e00-\u9fa5])/)) {
      parts.push(content.substring(lastIndex, index + 1).trim());
      lastIndex = index + 1;
    }
  }
  parts.push(content.substring(lastIndex).trim());

  return parts.filter((part) => part.trim() !== '');
};

// 移除原有的 ForceLineBreak 組件，替換為 AutoWrapText
const AutoWrapText = ({ text, style }) => {
  const words = useMemo(() => {
    if (!text) return [];

    // 分割文本，保持原有的換行
    return text
      .split(
        /(?<=[\u4e00-\u9fa5])|(?=[\u4e00-\u9fa5])|(?<=\s)|(?=\s)|(?<=[@#$%^&*])|(?=[@#$%^&*])|(?<=\w{10})(?=\w)/g
      )
      .filter((word) => word.trim().length > 0);
  }, [text]);

  if (!text) return null;

  return (
    <View style={[styles.autoWrapContainer, style]}>
      {words.map((word, index) => {
        // 檢查是否是編號或包含 mm 的數字
        const isNumber = /^(\d+\.|[\(（]\d+[\)）])(?!\d+\.\d+)/.test(word);
        const hasMM = /(?:\d+\.?\d*|\(\d+\.?\d*\))(?:\s)?mm/.test(word);

        return (
          <Text
            key={index}
            style={[
              styles.wordSpan,
              isNumber || hasMM ? { color: 'red' } : null,
            ]}
          >
            {word}
          </Text>
        );
      })}
    </View>
  );
};

// 樣式定義
const styles = StyleSheet.create({
  page: {
    padding: '10mm',
    backgroundColor: '#ffffff',
    fontFamily: 'NotoSansCJK',
  },
  pageOutline: {
    border: '1pt solid black',
    paddingTop: '3mm',
    minHeight: '60mm',
    height: 'auto',
    maxHeight: '277mm',
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
    overflow: 'hidden',
    flex: 1,
  },
  // 標題區域
  fileTitle: {
    marginBottom: '0',
    minHeight: '48mm', // 增加最小高度
    // borderBottom: '1pt solid black',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '3mm',
  },
  titleSection: {
    width: '100%', // 控制標題區域寬度
  },
  // 標題區域
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    justifyContent: 'center',
    alignContent: 'center',
    textAlign: 'center',
    marginLeft: '40%', // 向右偏移以達到完整置中
    padding: '5mm',
  },
  infoBox: {
    marginTop: '2mm',
  },
  infoText: {
    fontSize: 10,
    marginBottom: '1mm',
    paddingLeft: '3mm',
  },
  logo: {
    width: '80mm', // 加大 LOGO
    height: '32mm',
    marginLeft: 'auto', // 靠右顯示
    margin: '0mm 3mm 0mm 3mm',
    border: '1pt solid #c0c0c0',
    borderRadius: '3mm',
  },

  // SOP名稱區域
  sopContainer: {
    borderTop: '1pt solid black', // 移除頂部邊框
    borderBottom: 'none',
    padding: '1mm',
  },
  sopSection: {
    fontSize: 14,
    textAlign: 'center',
  },
  // Section 標題樣式
  sectionHeader: {
    marginTop: '0',
    marginBottom: '0',
  },
  sectionLabelContainer: {
    borderTop: '1pt solid black',
    borderBottom: '1pt solid black',
    padding: '1mm 0',
  },
  // 機型、工具和維修部位的標籤統一樣式
  sectionLabel: {
    fontSize: 11,
    paddingLeft: '3mm',
  },

  // 機型區域
  // For Model 和 SOP 名稱之間的間距調整
  modelSection: {
    marginTop: '5mm',
    marginBottom: '5mm',
    justifyContent: 'center', // 垂直置中
    alignItems: 'center', // 水平置中
    borderBottom: 'none', // 移除底部邊框
  },
  modelImage: {
    width: '60%', // 縮小左右兩邊
    height: '45mm',
    objectFit: 'contain',
    border: '1pt solid #c0c0c0', // 更淺的灰色
    borderRadius: '1mm',
    padding: '0.5mm',
    marginLeft: 'auto',
    marginRight: 'auto',
  },

  // 工具區域
  toolsSection: {
    marginTop: '5mm',
  },
  toolsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: '60mm', // 增加整體高度
    paddingLeft: '3mm',
  },
  toolsImagesContainer: {
    width: '65%',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: '1mm',
  },
  toolsNamesContainer: {
    width: '32%',
    borderLeft: '1pt solid black',
    paddingLeft: '1mm', // 增加左邊距
  },
  toolItem: {
    width: '15%', // 一排6個
    height: '40mm', // 增加圖片高度
    marginBottom: '5mm',
    marginTop: '6mm',
    position: 'relative',
    overflow: 'hidden', // 確保內容不會溢出
  },
  toolImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    border: '1pt solid #c0c0c0',
    borderRadius: '1mm',
    padding: '0.5mm',
  },
  toolNameLabel: {
    color: 'red',
    fontSize: 10,
    paddingBottom: '1mm',
  },
  toolName: {
    marginBottom: '2mm',
  },
  toolNameText: {
    color: 'black', // 檔案名稱為黑色
    fontSize: 10,
    paddingBottom: '1mm',
  },
  // 維修部位說明區域
  illustrationSection: {
    marginTop: '5mm',
  },
  illustrationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: '90mm', // 增加整體高度
    paddingLeft: '3mm',
  },
  // 維修部位說明區域
  illustrationImagesContainer: {
    width: '65%',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: '2mm',
    maxHeight: '90mm', // 限制最多兩排
    marginTop: '3mm',
  },
  illustrationNamesContainer: {
    width: '35%',
    paddingLeft: '1mm',
    display: 'flex',
    justifyContent: 'center', // 垂直置中
    paddingVertical: '3mm', // 上下間距
  },
  illustrationItem: {
    width: '30%', // 調整為3個
    height: '40mm', // 增加圖片高度
    marginBottom: '0mm',
    position: 'relative',
    overflow: 'hidden', // 確保內容不會溢出
  },
  illustrationImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    border: '1pt solid #c0c0c0',
    borderRadius: '1mm',
    padding: '0.5mm',
  },
  // SOP步驟頁面
  stepContainer: {
    borderTop: '1pt solid black',
    marginTop: '0',
    flex: 1,
    display: 'flex',
  },
  stepContent: {
    flexDirection: 'row',
    height: '85mm', // 稍微提高，讓2個Step可以舒適共享
    marginTop: '0',
    marginBottom: '0',
    flex: 1,
  },
  stepImageSection: {
    width: '30%',
    borderRight: '1pt solid black',
    padding: '2mm',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: 'auto',
    flex: 1,
  },
  stepImage: {
    width: 'auto',
    height: 'auto',
    maxWidth: '95%',
    maxHeight: '95%',
    objectFit: 'contain',
    border: '1pt solid #c0c0c0',
    borderRadius: '1mm',
    padding: '1mm',
  },
  stepDescriptionSection: {
    width: '35%',
    borderRight: '1pt solid black',
    padding: '2mm',
    display: 'flex',
    flexDirection: 'column',
    height: 'auto',
    flex: 1,
    paddingBottom: '3mm',
  },
  stepRemarkSection: {
    width: '35%',
    padding: '2mm',
    display: 'flex',
    flexDirection: 'column',
    height: 'auto',
    flex: 1,
  },
  stepTitle: {
    fontSize: 10,
    marginBottom: '2mm',
    fontWeight: 'bold',
  },
  // 工具和維修部位共用樣式
  itemLabel: {
    position: 'absolute',
    top: 1,
    right: 1,
    color: 'black',
    fontSize: 8,
    border: '1pt solid red',
    padding: '1mm',
    backgroundColor: 'white',
    borderRadius: '1mm',
  },
  // 工具和維修部位的容器樣式
  sectionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: '3mm',
  },
  imagesContainer: {
    width: '65%',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: '2mm',
  },
  namesContainer: {
    width: '35%',
    paddingLeft: '1mm',
    display: 'flex',
    justifyContent: 'center',
    paddingVertical: '3mm',
  },

  // 新增外層容器樣式
  stepHeaderContainer: {
    height: '8mm',
    borderBottom: '1pt solid black',
    display: 'flex',
    justifyContent: 'center',
  },
  // Step 樣式
  stepHeader: {
    fontSize: 11,
    paddingLeft: '3mm',
  },
  stepText: {
    fontSize: 10,
    width: '100%',
    maxWidth: '100%',
    lineHeight: 1.6,
  },

  stepTextContainer: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
    marginTop: '2mm',
  },
  remarkImage: {
    width: 'auto',
    height: 'auto',
    maxWidth: '95%', // 增大圖片寬度，更充分利用空間
    maxHeight: '40mm', // 增加高度上限
    objectFit: 'contain',
    marginTop: '1mm', // 減少與文字的間距
    border: '1pt solid #c0c0c0',
    borderRadius: '1mm',
    padding: '1mm',
  },

  // 添加 AutoWrapText 相關樣式
  autoWrapContainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    width: '100%',
    padding: '1mm',
  },

  wordSpan: {
    fontSize: 10,
    lineHeight: 1.3,
    marginRight: '1pt',
  },

  stepSectionLarge: {
    height: '100%',
    overflow: 'auto',
  },

  stepContentExtended: {
    flexDirection: 'row',
    height: '120mm', // 中等內容容器
    marginTop: '0',
    marginBottom: '0',
    flex: 1,
  },

  stepContentMediumLong: {
    flexDirection: 'row',
    height: '150mm', // 中長內容容器，解決Step 5類型溢出問題
    marginTop: '0',
    marginBottom: '0',
    flex: 1,
  },

  stepContentLong: {
    flexDirection: 'row',
    height: '180mm', // 長內容容器
    marginTop: '0',
    marginBottom: '0',
    flex: 1,
  },

  stepContentFullPage: {
    flexDirection: 'row',
    height: '225mm',
    marginTop: '0',
    marginBottom: '0',
    flex: 1,
  },

  remarkTextContainer: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1, // 佔據可用空間
    overflow: 'visible', // 允許內容自然展示
  },

  remarkImageContainer: {
    alignItems: 'center',
    justifyContent: 'flex-start', // 靠近文字
    marginTop: '3mm', // 與文字保持適當間距
    maxHeight: '45mm', // 增加圖片區域高度，配合更大的圖片
    width: '100%',
  },
});

// 在 PDFDocument 組件中的渲染部分
const PDFDocument = ({ knowledgeInfo, SOPData }) => {
  // 獲取Logo圖片URL的函數
  const getLogoImageUrl = () => {
    try {
      if (knowledgeInfo?.knowledgeBaseLogoImage) {
        const logoImages = JSON.parse(knowledgeInfo.knowledgeBaseLogoImage);
        if (logoImages && logoImages.length > 0) {
          return logoImages[0]; // 使用第一張Logo圖片
        }
      }
    } catch (error) {
      console.error('Error parsing logo image:', error);
    }
    // 如果沒有自定義Logo，使用預設Logo
    return require('../Company Logo.jpg');
  };

  // 改善的內容長度檢測，支援動態分頁策略
  const checkStepContentLength = (illustrationText, remarkText, hasRemarkImage) => {
    const illLength = illustrationText?.length || 0;
    const remarkLength = remarkText?.length || 0;
    const totalLength = illLength + remarkLength;
    
    // 計算換行數量
    const illNewLines = (illustrationText?.match(/\n/g) || []).length;
    const remarkNewLines = (remarkText?.match(/\n/g) || []).length;
    
    // 圖片佔用空間評估
    const imageSpace = hasRemarkImage ? 150 : 0;
    
    // 綜合內容評分
    const contentScore = totalLength + (illNewLines + remarkNewLines) * 15 + imageSpace;
    
    // 調試信息
    console.log(`Content Analysis - Score: ${contentScore}, illLength: ${illLength}, remarkLength: ${remarkLength}, hasImage: ${hasRemarkImage}`);
    
    return {
      totalLength,
      contentScore,
      // 針對Step 5溢出問題調整閾值，確保2個Step一頁的合理分配
      isShortContent: contentScore <= 250,     // 短內容：85mm容器（約100字以內）
      isMediumContent: contentScore > 250 && contentScore <= 350,    // 中等內容：120mm容器（約150-250字）
      isMediumLongContent: contentScore > 350 && contentScore <= 600, // 中長內容：150mm容器（約250-450字）
      isLongContent: contentScore > 600 && contentScore <= 900,      // 長內容：180mm容器（約500-650字）
      needsFullPage: contentScore > 900,       // 超長內容：225mm全頁（約700字+）
      hasImageConflict: remarkLength > 200 && hasRemarkImage,
      layoutMode: contentScore <= 250 ? 'short' :
                  contentScore <= 350 ? 'medium' :
                  contentScore <= 600 ? 'mediumLong' :
                  contentScore <= 900 ? 'long' : 'fullPage'
    };
  };

  // 改善的動態分頁邏輯
  const paginatedSOPData = useMemo(() => {
    const result = [];
    if (!SOPData) return result;

    let currentPage = [];
    let currentHeight = 48;
    const maxPageHeight = 277;

    const shouldStartNewPage = (currentSteps, nextStep) => {
      if (currentSteps.length === 0) return false;
      
      const nextStepAnalysis = checkStepContentLength(
        nextStep.soP2Message, 
        nextStep.soP2Remark, 
        !!nextStep.soP2RemarkImage
      );

      // 計算當前頁面已有內容的總評分
      const currentPageScore = currentSteps.reduce((total, step) => {
        const stepAnalysis = checkStepContentLength(
          step.soP2Message, 
          step.soP2Remark, 
          !!step.soP2RemarkImage
        );
        return total + stepAnalysis.contentScore;
      }, 0);

      // 調試信息
      console.log(`Step分頁決策 - 當前Steps數量: ${currentSteps.length}, 當前頁評分: ${currentPageScore}, 下一Step分類: ${nextStepAnalysis.layoutMode}, 評分: ${nextStepAnalysis.contentScore}`);

      // 超長內容：必須開新頁
      if (nextStepAnalysis.needsFullPage && currentSteps.length > 0) {
        console.log('觸發超長內容開新頁');
        return true;
      }
      
      // 智能空間評估：檢查當前頁+下一步是否會超出容量
      const totalScore = currentPageScore + nextStepAnalysis.contentScore;

      // 如果總評分超過700分，開新頁（調整為更合理的容量限制）
      if (totalScore > 700) {
        console.log(`觸發容量限制開新頁 - 總評分: ${totalScore}`);
        return true;
      }

      // 長內容：必須獨佔頁面
      if (nextStepAnalysis.isLongContent && currentSteps.length > 0) {
        console.log('觸發長內容開新頁（獨佔頁面）');
        return true;
      }

      // 中長內容：最多2個Step共享一頁
      if (nextStepAnalysis.isMediumLongContent && currentSteps.length >= 2) {
        console.log('觸發中長內容開新頁（最多2個Step）');
        return true;
      }

      // 中等內容：最多2個Step共享一頁
      if (nextStepAnalysis.isMediumContent && currentSteps.length >= 2) {
        console.log('觸發中等內容開新頁');
        return true;
      }
      
      // 短內容：最多3個Step共享一頁
      if (nextStepAnalysis.isShortContent && currentSteps.length >= 3) {
        console.log('觸發短內容開新頁');
        return true;
      }

      console.log('不需要開新頁，繼續當前頁');
      return false;
    };

    for (let i = 0; i < SOPData.length; i++) {
      const step = SOPData[i];
      const stepAnalysis = checkStepContentLength(
        step.soP2Message, 
        step.soP2Remark, 
        !!step.soP2RemarkImage
      );

      // 超長內容：需要全頁
      if (stepAnalysis.needsFullPage) {
        if (currentPage.length > 0) {
          result.push({
            steps: currentPage,
            height: currentHeight,
            layoutMode: 'standard'
          });
          currentPage = [];
        }
        result.push({
          steps: [step],
          height: maxPageHeight,
          layoutMode: 'fullPage',
          isFullPage: true,
        });
        currentHeight = 48;
        continue;
      }

      // 移除長內容強制獨佔的邏輯，讓所有內容都通過shouldStartNewPage來決定

      // 檢查是否需要開始新頁
      if (shouldStartNewPage(currentPage, step)) {
        // 判斷當前頁是否有長內容，決定layoutMode
        const hasLongContent = currentPage.some(pageStep => {
          const analysis = checkStepContentLength(
            pageStep.soP2Message, 
            pageStep.soP2Remark, 
            !!pageStep.soP2RemarkImage
          );
          return analysis.isLongContent;
        });
        
        result.push({
          steps: currentPage,
          height: currentHeight,
          layoutMode: hasLongContent ? 'extended' : 'standard'
        });
        currentPage = [];
        currentHeight = 48;
      }

      currentPage.push(step);
      
      // 根據Step類型決定高度增量 - 支援新的內容分類
      if (stepAnalysis.isLongContent) {
        currentHeight += 188; // 180mm step + 8mm header = 188mm (長內容)

        // 長內容添加後立即結束當前頁面，防止後續步驟溢出
        result.push({
          steps: currentPage,
          height: currentHeight,
          layoutMode: 'long'
        });
        currentPage = [];
        currentHeight = 48;
      } else if (stepAnalysis.isMediumLongContent) {
        currentHeight += 158; // 150mm step + 8mm header = 158mm (中長內容)

        // 中長內容最多2個Step一頁，檢查是否需要開新頁
        if (currentPage.length >= 2) {
          result.push({
            steps: currentPage,
            height: currentHeight,
            layoutMode: 'mediumLong'
          });
          currentPage = [];
          currentHeight = 48;
        }
      } else if (stepAnalysis.isMediumContent) {
        currentHeight += 128; // 120mm step + 8mm header = 128mm (中等內容)
      } else {
        currentHeight += 93; // 85mm step + 8mm header = 93mm (短內容)
      }
    }

    // 處理最後一頁
    if (currentPage.length > 0) {
      // 判斷最後一頁的內容類型，決定layoutMode
      let layoutMode = 'standard';

      const hasLongContent = currentPage.some(step => {
        const analysis = checkStepContentLength(
          step.soP2Message,
          step.soP2Remark,
          !!step.soP2RemarkImage
        );
        return analysis.isLongContent;
      });

      const hasMediumLongContent = currentPage.some(step => {
        const analysis = checkStepContentLength(
          step.soP2Message,
          step.soP2Remark,
          !!step.soP2RemarkImage
        );
        return analysis.isMediumLongContent;
      });

      const hasMediumContent = currentPage.some(step => {
        const analysis = checkStepContentLength(
          step.soP2Message,
          step.soP2Remark,
          !!step.soP2RemarkImage
        );
        return analysis.isMediumContent;
      });

      // 根據內容類型決定layoutMode
      if (hasLongContent) {
        layoutMode = 'long';
      } else if (hasMediumLongContent) {
        layoutMode = 'mediumLong';
      } else if (hasMediumContent) {
        layoutMode = 'extended';
      }

      result.push({
        steps: currentPage,
        height: currentHeight,
        layoutMode: layoutMode
      });
    }

    return result;
  }, [SOPData]);

  // 修改步驟內容渲染邏輯，支援動態佈局
  const StepContent = ({ sop, layoutMode = 'standard' }) => {
    // 根據佈局模式決定使用的樣式
    const contentStyle = layoutMode === 'fullPage'
      ? styles.stepContentFullPage
      : layoutMode === 'long'
      ? styles.stepContentLong
      : layoutMode === 'mediumLong'
      ? styles.stepContentMediumLong
      : layoutMode === 'extended'
      ? styles.stepContentExtended
      : styles.stepContent;

    return (
      <View style={contentStyle}>
        <View style={styles.stepImageSection}>
          {sop.soP2Image && (
            <Image style={styles.stepImage} src={sop.soP2Image} />
          )}
        </View>

        <View style={styles.stepDescriptionSection}>
          <Text style={styles.stepTitle}>Illustration(步驟說明)：</Text>
          <AutoWrapText text={sop.soP2Message} />
        </View>

        <View style={styles.stepRemarkSection}>
          <View style={styles.remarkTextContainer}>
            <Text style={styles.stepTitle}>Remark(備註補充)：</Text>
            <AutoWrapText text={sop.soP2Remark} />
            {sop.soP2RemarkImage && (
              <View style={styles.remarkImageContainer}>
                <Image style={styles.remarkImage} src={sop.soP2RemarkImage} />
              </View>
            )}
          </View>
        </View>
      </View>
    );
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.pageOutline}>
          {/* 標題區域 */}
          <View style={styles.fileTitle}>
            <View style={styles.headerContainer}>
              <View style={styles.titleSection}>
                <Text style={styles.title}>Trouble Shooting</Text>
                <View style={styles.infoBox}>
                  <Text style={styles.infoText}>
                    File No : {knowledgeInfo.knowledgeBaseFileNo}
                  </Text>
                  <Text style={styles.infoText}>
                    Error Code : {knowledgeInfo.knowledgeBaseAlarmCode}
                  </Text>
                </View>
              </View>
              <Image style={styles.logo} src={getLogoImageUrl()} />
            </View>
            <View style={styles.sopContainer}>
              <Text style={styles.sopSection}>
                SOP名稱: {knowledgeInfo.knowledgeBaseSOPName}
              </Text>
            </View>
          </View>

          {/* For Model 機型 */}
          <View style={styles.sectionHeader}>
            <View style={styles.sectionLabelContainer}>
              <Text style={styles.sectionLabel}>
                For Model 機型:{' '}
                {knowledgeInfo.knowledgeBaseModelImageNames &&
                  extractFileName(knowledgeInfo.knowledgeBaseModelImageNames)}
              </Text>
            </View>
            <View style={styles.modelSection}>
              {knowledgeInfo.knowledgeBaseModelImage &&
                safeJsonParse(knowledgeInfo.knowledgeBaseModelImage).map(
                  (item, idx) => (
                    <Image key={idx} style={styles.modelImage} src={item} />
                  )
                )}
            </View>
          </View>

          {/* Use Tools */}
          <View style={styles.sectionHeader}>
            <View style={styles.sectionLabelContainer}>
              <Text style={styles.sectionLabel}>Use Tools(使用工具圖片) :</Text>
            </View>
            <View style={styles.toolsContainer}>
              <View style={styles.toolsImagesContainer}>
                {knowledgeInfo.knowledgeBaseToolsImage &&
                  safeJsonParse(knowledgeInfo.knowledgeBaseToolsImage).map(
                    (item, idx) => (
                      <View key={idx} style={styles.toolItem}>
                        <Image style={styles.toolImage} src={item} />
                        <Text style={styles.itemLabel}>
                          {['A', 'B', 'C', 'D', 'E', 'F'][idx]}
                        </Text>
                      </View>
                    )
                  )}
              </View>
              <View style={styles.namesContainer}>
                {knowledgeInfo.knowledgeBaseToolsImageNames &&
                  safeJsonParse(knowledgeInfo.knowledgeBaseToolsImageNames).map(
                    (name, idx) => (
                      <Text key={idx} style={styles.toolName}>
                        <Text style={styles.toolNameLabel}>
                          {['A', 'B', 'C', 'D', 'E', 'F'][idx]}:
                        </Text>
                        <Text style={styles.toolNameText}>
                          {extractFileName(name)}
                        </Text>
                      </Text>
                    )
                  )}
              </View>
            </View>
          </View>

          {/* Illustration 區域 */}
          <View style={styles.sectionHeader}>
            <View style={styles.sectionLabelContainer}>
              <Text style={styles.sectionLabel}>
                Illustration(維修部位說明) :
              </Text>
            </View>
            <View style={styles.illustrationContainer}>
              <View style={styles.illustrationImagesContainer}>
                {knowledgeInfo.knowledgeBasePositionImage &&
                  safeJsonParse(knowledgeInfo.knowledgeBasePositionImage).map(
                    (item, idx) => (
                      <View key={idx} style={styles.illustrationItem}>
                        <Image style={styles.illustrationImage} src={item} />
                        <Text style={styles.itemLabel}>
                          {['A', 'B', 'C', 'D', 'E', 'F'][idx]}
                        </Text>
                      </View>
                    )
                  )}
              </View>
              <View style={styles.illustrationNamesContainer}>
                {knowledgeInfo.knowledgeBasePositionImageNames &&
                  safeJsonParse(
                    knowledgeInfo.knowledgeBasePositionImageNames
                  ).map((name, idx) => (
                    <Text key={idx} style={styles.toolName}>
                      <Text style={styles.toolNameLabel}>
                        {['A', 'B', 'C', 'D', 'E', 'F'][idx]}:
                      </Text>
                      <Text style={styles.toolNameText}>
                        {extractFileName(name)}
                      </Text>
                    </Text>
                  ))}
              </View>
            </View>
          </View>
        </View>
      </Page>

      {/* SOP步驟頁面 */}
      {paginatedSOPData.map((page, pageIndex) => (
        <Page key={pageIndex} size="A4" style={styles.page}>
          <View
            style={{
              ...styles.pageOutline,
              height: `${page.height}mm`,
            }}
          >
            {/* 標題區域 */}
            <View style={styles.fileTitle}>
              <View style={styles.headerContainer}>
                <View style={styles.titleSection}>
                  <Text style={styles.title}>Trouble Shooting</Text>
                  <View style={styles.infoBox}>
                    <Text style={styles.infoText}>
                      File No : {knowledgeInfo.knowledgeBaseFileNo}
                    </Text>
                    <Text style={styles.infoText}>
                      Error Code : {knowledgeInfo.knowledgeBaseAlarmCode}
                    </Text>
                  </View>
                </View>
                <Image
                  style={styles.logo}
                  src={getLogoImageUrl()}
                />
              </View>
              <View style={styles.sopContainer}>
                <Text style={styles.sopSection}>
                  SOP名稱: {knowledgeInfo.knowledgeBaseSOPName}
                </Text>
              </View>
            </View>

            {/* 步驟內容 */}
            {page.steps.map((sop, idx) => (
              <View key={idx} style={styles.stepContainer}>
                <View style={styles.stepHeaderContainer}>
                  <Text style={styles.stepHeader}>Step {sop.soP2Step}</Text>
                </View>
                <StepContent
                  sop={sop}
                  layoutMode={page.layoutMode || 'standard'} // 傳遞佈局模式
                />
              </View>
            ))}
          </View>
        </Page>
      ))}
    </Document>
  );
};

export default PDFDocument;

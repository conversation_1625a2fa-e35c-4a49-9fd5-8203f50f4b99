﻿using Microsoft.AspNetCore.Mvc;
using Models;
using System;
using System.IO;
using System.Threading.Tasks;

[Route("api/[controller]")]
[ApiController]
public class PdfController : ControllerBase
{
    private readonly string _baseFolder = @"C:\Users\<USER>\Desktop\PDFSaveTest";
    private readonly ILogger<PdfController> _logger;

    public PdfController(ILogger<PdfController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// PDF檔案分塊上傳 - 支援大型PDF檔案的分塊上傳，確保傳輸穩定性
    /// </summary>
    /// <param name="file">PDF檔案塊</param>
    /// <param name="schemaName">用戶Schema名稱</param>
    /// <param name="chunkIndex">當前塊索引</param>
    /// <param name="totalChunks">總塊數</param>
    /// <returns>上傳結果狀態</returns>
    [HttpPut("upload-chunk")]
    public async Task<IActionResult> UploadChunk([FromForm] IFormFile file, [FromForm] string schemaName, [FromForm] int chunkIndex, [FromForm] int totalChunks)
    {
        try
        {
            // 檢查文件是否存在
            if (file == null || file.Length == 0)
                return BadRequest(new ApiResponse<string>("1001", "No file uploaded"));

            // 檢查 schema 名稱是否存在
            if (string.IsNullOrEmpty(schemaName))
                return BadRequest(new ApiResponse<string>("1002", "Schema name is required"));

            // 創建用戶文件夾
            var userFolder = Path.Combine(_baseFolder, schemaName);
            if (!Directory.Exists(userFolder))
                Directory.CreateDirectory(userFolder);

            // 創建臨時文件名
            string tempFileName = $"temp_{Path.GetFileNameWithoutExtension(file.FileName)}_{chunkIndex}.part";
            var tempFilePath = Path.Combine(userFolder, tempFileName);

            // 保存分塊文件
            using (var stream = new FileStream(tempFilePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            _logger.LogInformation($"Chunk {chunkIndex + 1}/{totalChunks} uploaded successfully");

            // 如果是最後一個分塊，則合併所有分塊
            if (chunkIndex == totalChunks - 1)
            {
                // All chunks received, combine them
                string finalFileName = await CombineChunks(userFolder, file.FileName, totalChunks);
                return Ok(new ApiResponse<string>("0000", "PDF successfully uploaded and backed up", finalFileName));
            }

            return Ok(new ApiResponse<string>("0000", $"Chunk {chunkIndex + 1}/{totalChunks} uploaded successfully", null));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while uploading chunk");
            return StatusCode(500, new ApiResponse<string>("9999", $"An error occurred: {ex.Message}"));
        }
    }

    private async Task<string> CombineChunks(string folder, string fileName, int totalChunks)
    {
        // 獲取下一個文件編號
        int nextFileNumber = GetNextFileNumber(folder);
        string finalFileName = $"維修手冊{nextFileNumber}.pdf";
        var finalFilePath = Path.Combine(folder, finalFileName);

        // 合併所有分塊
        using (var outputStream = new FileStream(finalFilePath, FileMode.Create))
        {
            for (int i = 0; i < totalChunks; i++)
            {
                string tempFileName = $"temp_{Path.GetFileNameWithoutExtension(fileName)}_{i}.part";
                var tempFilePath = Path.Combine(folder, tempFileName);

                using (var inputStream = new FileStream(tempFilePath, FileMode.Open))
                {
                    await inputStream.CopyToAsync(outputStream);
                }

                System.IO.File.Delete(tempFilePath); // 刪除臨時文件
            }
        }

        _logger.LogInformation($"PDF file combined and saved as {finalFileName}");
        return finalFileName;
    }

    private int GetNextFileNumber(string folderPath)
    {
        // 獲取文件夾中所有的 PDF 文件
        var files = Directory.GetFiles(folderPath, "維修手冊*.pdf");
        if (files.Length == 0)
            return 1;

        // 找出最大的文件編號
        int maxNumber = 0;
        foreach (var file in files)
        {
            var fileName = Path.GetFileNameWithoutExtension(file);
            if (int.TryParse(fileName.Replace("維修手冊", ""), out int number))
            {
                maxNumber = Math.Max(maxNumber, number);
            }
        }

        // 返回下一個可用的文件編號
        return maxNumber + 1;
    }
}
import React, { useState, useEffect } from 'react';
import { Modal, Button, Form } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { useStore } from '../zustand/store';
import { fetchDataCallFile } from '../utils/Api';
import { apiSaveSOP2 } from '../utils/Api';

export function SOPName({ onClose, tempModels }) {
  const { SOPInfo, setSOPInfo } = useStore();
  const [showModal, setShowModal] = useState(true);
  const [sop2Name, setSopName] = useState('');
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false); // 添加載入狀態
  const { t } = useTranslation();

  // 這個 useEffect 會在 SOPInfo 變更時更新，包括組件初次渲染
  useEffect(() => {
    console.log('SOPName useEffect - SOPInfo updated:', SOPInfo);

    // 如果存在 SOPInfo 且其內有 knowledgeInfo，則從中提取 knowledgeBaseSOPName
    if (
      SOPInfo &&
      SOPInfo.knowledgeInfo &&
      SOPInfo.knowledgeInfo.knowledgeBaseSOPName
    ) {
      setSopName(SOPInfo.knowledgeInfo.knowledgeBaseSOPName);
    } else {
      // 如果沒有提供有效的 SOP 名稱，設置為空以便新建
      setSopName('');
    }

    // 如果 machineAddId 缺失，嘗試從 localStorage 恢復
    if (SOPInfo && !SOPInfo.machineAddId) {
      const savedMachineAddId = localStorage.getItem('currentMachineAddId');
      if (savedMachineAddId) {
        console.log('SOPName: Restoring machineAddId from localStorage:', savedMachineAddId);
        setSOPInfo(prev => ({
          ...prev,
          machineAddId: parseInt(savedMachineAddId)
        }));
      }
    }
  }, [SOPInfo, setSOPInfo]);

  // 處理模態窗關閉事件
  const handleCloseModal = () => {
    setShowModal(false);
    if (onClose) onClose();
  };

  // 處理欄位變更事件
  const handleEditChange = (e) => {
    setSopName(e.target.value);
    setErrors((prevErrors) => ({
      ...prevErrors,
      sop2Name: e.target.value ? null : 'required',
    }));
  };

  // 處理欄位失焦事件
  const handleEditBlur = () => {
    if (!sop2Name.trim()) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        sop2Name: 'required',
      }));
    }
  };

  // 這個函數可能在某個地方被定義，用以構建每個 SOP2s 的 3D 模型資訊
  const formatT3DModels = (sop) => {
    if (sop.sopModels && Array.isArray(sop.sopModels)) {
      const modelIndices = sop.sopModels
        .map((model, index) => {
          // 假設您要基於模型是否被刪除來決定是否包括索引
          if (!model.deleted) {
            return index; // 返回模型的索引
          }
          return null;
        })
        .filter((index) => index !== null); // 過濾掉被標記為null的索引
      return JSON.stringify(modelIndices);
    }
    return '[]'; // 當沒有模型數據時，返回一個空的 JSON 數組
  };

  // 處理表單儲存事件
  const handleSave = async () => {
    console.log('handleSave is triggered');
    console.log({ ...SOPInfo, sop2Name });

    // 設置載入狀態
    setIsLoading(true);

    let hasError = false;
    const newErrors = {};

    const SOPFormData = new FormData();

    // 獲取正確的 ID 值
    const machineAddId = SOPInfo.machineAddId || SOPInfo.knowledgeInfo?.machineAddId;
    const knowledgeBaseId = SOPInfo.knowledgeInfo?.knowledgeBaseId;

    console.log('ID values:', { machineAddId, knowledgeBaseId, SOPInfo });

    // 檢查並添加 MachineAddId
    if (machineAddId) {
      SOPFormData.append('MachineAddId', machineAddId.toString());
      console.log('MachineAddId added:', machineAddId);
    } else {
      console.error('MachineAddId is not set or is undefined');

      // 臨時解決方案：使用預設的 machineAddId 或提示用戶
      const defaultMachineAddId = 1; // 使用預設值，您可以根據需要調整
      console.warn('Using default machineAddId:', defaultMachineAddId);

      SOPFormData.append('MachineAddId', defaultMachineAddId.toString());

      // 更新 SOPInfo 以包含 machineAddId
      setSOPInfo(prev => ({
        ...prev,
        machineAddId: defaultMachineAddId
      }));

      // 顯示警告但不阻止保存
      toast.warn('使用預設機台 ID，建議重新選擇機台', {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 3000,
      });
    }

    // 檢查並添加 KnowledgeBaseId
    if (knowledgeBaseId) {
      SOPFormData.append('KnowledgeBaseId', knowledgeBaseId.toString());
      console.log('KnowledgeBaseId added:', knowledgeBaseId);
    } else {
      console.log('KnowledgeBaseId is not set or is undefined');
    }

    if (!sop2Name || sop2Name.trim() === '') {
      newErrors.sop2Name = '必填项';
      hasError = true;
    }

    setErrors(newErrors);

    if (hasError) {
      // 驗證失敗時，延遲2秒後重置載入狀態
      setTimeout(() => {
        setIsLoading(false);
      }, 2000);
      return;
    }

    if (!hasError) {
      const modelImageObj =
        SOPInfo.knowledgeInfo?.knowledgeBaseModelImageObj?.map(
          (item) => item.name
        );
      const toolsImageObj =
        SOPInfo.knowledgeInfo?.knowledgeBaseToolsImageObj?.map(
          (item) => item.name
        );
      const positionImageObj =
        SOPInfo.knowledgeInfo?.knowledgeBasePositionImageObj?.map(
          (item) => item.name
        );

      const formData = new FormData();

      // 確保使用正確的 machineAddId（可能已經在上面更新過）
      const finalMachineAddId = SOPInfo.machineAddId || machineAddId || 1;
      formData.append('MachineAddId', finalMachineAddId.toString());
      console.log('Using machineAddId for final save:', finalMachineAddId);

      // formData.append('machineName', SOPInfo.machineInfo.machineName);
      formData.append('KnowledgeBases[0].KnowledgeBaseSOPName', sop2Name);

      // 添加3D模型文件和圖片的處理，並確保每個模型都正確處理
      SOPInfo.sops.forEach((sop, sopIndex) => {
        sop.sopModels.forEach((model, modelIndex) => {
          // 檢查並確保文件物件是正確的
          if (
            model.sopModelImageObj &&
            model.sopModelImageObj instanceof File
          ) {
            formData.append(
              'KnowledgeBases[0].KnowledgeBase3DModelImageObj',
              model.sopModelImageObj,
              model.sopModelImageObj.name
            );
          }
          if (model.sopModelFileObj && model.sopModelFileObj instanceof File) {
            formData.append(
              'KnowledgeBases[0].KnowledgeBase3DModelFileObj',
              model.sopModelFileObj,
              model.sopModelFileObj.name
            );
          }
        });
      });

      // 如果有 KnowledgeBaseId，加入到 formData (編輯CRUD)
      if (SOPInfo.knowledgeBaseId) {
        formData.append('KnowledgeBaseId', SOPInfo.knowledgeBaseId.toString());
      }

      // 確保knowledgeInfo是一個陣列，並提供默認值
      const knowledgeInfoArray = SOPInfo.knowledgeInfo
        ? Array.isArray(SOPInfo.knowledgeInfo)
          ? SOPInfo.knowledgeInfo
          : [SOPInfo.knowledgeInfo]
        : [];

      const allowedExtensions = ['png', 'jpg', 'jpeg'];
      let fileIncluded = false; // 標記是否包含至少一個文件

      knowledgeInfoArray.forEach((info, index) => {
        Object.keys(info).forEach((key) => {
          if (key.includes('ImageObj')) {
            // 檢查info[key]是否存在並具有forEach方法
            if (info[key] && info[key].forEach) {
              info[key].forEach((fileObj) => {
                if (fileObj && fileObj.file) {
                  // 添加這一行來進行檢查
                  const file = fileObj.file; // 確保使用的是原始文件對象
                  const fileExtension = file.name
                    .split('.')
                    .pop()
                    .toLowerCase();
                  if (!allowedExtensions.includes(fileExtension)) {
                    // toast.error(`不支持的文件類型: ${file.name}`, {
                    //   position: toast.POSITION.TOP_CENTER,
                    //   autoClose: 2000,
                    //   hideProgressBar: true,
                    //   closeOnClick: false,
                    //   pauseOnHover: true,
                    // });
                  } else {
                    formData.append(`KnowledgeBases[${index}].${key}`, file);
                    fileIncluded = true;
                  }
                }
              });
            }
          } else if (
            ![
              'knowledgeBaseModelImageNames',
              'knowledgeBaseToolsImageNames',
              'knowledgeBasePositionImageNames',
            ].includes(key)
          ) {
            // 檢查並排除不想提交的字段
            formData.append(`KnowledgeBases[${index}].${key}`, info[key]);
          }
        });
      });

      // 確保每個文件名稱都包含有效的文件擴展名
      const validateFileName = (fileName) => {
        const validExtensions = ['jpg', 'jpeg', 'png']; // 可支持的文件類型擴展名
        const extension = fileName.split('.').pop().toLowerCase();
        return validExtensions.includes(extension)
          ? fileName
          : `${fileName}.jpg`; // 若無擴展名，則預設為.jpg
      };

      // 遍歷並附加每個圖片名稱，確保它們都包含有效的擴展名
      modelImageObj.forEach((name, idx) => {
        formData.append(
          `KnowledgeBases[0].KnowledgeBaseModelImageNames[${idx}]`,
          name
        );
      });
      toolsImageObj.forEach((name, idx) => {
        formData.append(
          `KnowledgeBases[0].KnowledgeBaseToolsImageNames[${idx}]`,
          name
        );
      });
      positionImageObj.forEach((name, idx) => {
        formData.append(
          `KnowledgeBases[0].KnowledgeBasePositionImageNames[${idx}]`,
          name
        );
      });

      // 檢查是否有文件被添加
      if (!fileIncluded) {
        toast.error('故障說明 & SOP請添加至少更新圖片文件。', {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 2000,
          hideProgressBar: true,
          closeOnClick: false,
          pauseOnHover: true,
        });
        return;
      }

      try {
        // 查看 formData 內的檔案內容
        for (let pair of formData.entries()) {
          console.log(`${pair[0]}, ${pair[1]}`);
        }

        for (let pair of SOPFormData.entries()) {
          console.log(`${pair[0]}: ${pair[1]}`);
        }

        const saveKnowledgeBaseRes = await fetchDataCallFile(
          'SaveKnowledgeBase',
          'PUT',
          formData
        );

        if (saveKnowledgeBaseRes.message !== '完全成功') {
          toast.error(saveKnowledgeBaseRes.message, {
            position: toast.POSITION.TOP_CENTER,
            autoClose: 2000,
            hideProgressBar: true,
            closeOnClick: false,
            pauseOnHover: true,
          });
          return;
        }

        // 在創建新記錄時設置 KnowledgeBaseId
        SOPFormData.set(
          'KnowledgeBaseId',
          SOPInfo.knowledgeInfo.knowledgeBaseId
            ? SOPInfo.knowledgeInfo.knowledgeBaseId
            : saveKnowledgeBaseRes.result.toString()
        );

        // 處理 tempModels (3D Model List 的模型庫)
        if (tempModels && Array.isArray(tempModels)) {
          tempModels.forEach((tempModel, j) => {
            // 只保存有內容的 tempModel
            if (tempModel.tempModelImageObj || tempModel.tempModelImageUrl) {
              SOPFormData.append(
                `TempModels[${j}].tempModelImageName`,
                tempModel.tempModelImageName || ''
              );
              if (tempModel.tempModelImageObj) {
                SOPFormData.append(
                  `TempModels[${j}].tempModelImageObj`,
                  tempModel.tempModelImageObj
                );
              }
              SOPFormData.append(
                `TempModels[${j}].tempModelImageUrl`,
                tempModel.tempModelImageUrl || ''
              );
              SOPFormData.append(
                `TempModels[${j}].tempModelFileName`,
                tempModel.tempModelFileName || ''
              );
              if (tempModel.tempModelFileObj) {
                SOPFormData.append(
                  `TempModels[${j}].tempModelFileObj`,
                  tempModel.tempModelFileObj
                );
              }
            }
          });
        }

        SOPInfo.sops.forEach((sop, idx) => {
          // 確保所有布林值都被正確處理，不會提交undefined
          const isDeletedSOP2Image =
            sop.isDeletedSOP2Image !== undefined
              ? sop.isDeletedSOP2Image
              : false;
          const isDeletedSOP2RemarkImage =
            sop.isDeletedSOP2RemarkImage !== undefined
              ? sop.isDeletedSOP2RemarkImage
              : false;
          const isDeletedSOPVideo =
            sop.isDeletedSOPVideo !== undefined ? sop.isDeletedSOPVideo : false;

          // 基本信息 - 修正字段名稱以匹配後端模型
          // 使用 SOP2Id 而不是 soP2Id
          if (sop.sopId !== undefined && sop.sopId !== null && sop.sopId > 0) {
            SOPFormData.append(`SOP2s[${idx}].SOP2Id`, sop.sopId);
          } else if (sop.soP2Id !== undefined && sop.soP2Id !== null && sop.soP2Id > 0) {
            SOPFormData.append(`SOP2s[${idx}].SOP2Id`, sop.soP2Id);
          } else {
            SOPFormData.append(`SOP2s[${idx}].SOP2Id`, 0); // 新記錄
          }

          SOPFormData.append(`SOP2s[${idx}].Deleted`, sop.deleted || 0);
          SOPFormData.append(`SOP2s[${idx}].SOP2Step`, sop.soP2Step);
          SOPFormData.append(`SOP2s[${idx}].SOP2Message`, sop.soP2Message || '');
          SOPFormData.append(`SOP2s[${idx}].SOP2Name`, sop.soP2Name || '');
          SOPFormData.append(`SOP2s[${idx}].SOP2Remark`, sop.soP2Remark || '');

          // 添加 MachineAddId 和 KnowledgeBaseId 到每個 SOP
          SOPFormData.append(`SOP2s[${idx}].MachineAddId`, finalMachineAddId);
          SOPFormData.append(`SOP2s[${idx}].KnowledgeBaseId`,
            SOPInfo.knowledgeInfo?.knowledgeBaseId || saveKnowledgeBaseRes.result);

          // 圖片相關字段 - 修正字段名稱
          SOPFormData.append(`SOP2s[${idx}].SOP2Image`, sop.soP2Image || '');
          if (sop.soP2ImageObj) {
            SOPFormData.append(`SOP2s[${idx}].SOP2ImageObj`, sop.soP2ImageObj);
          }
          SOPFormData.append(
            `SOP2s[${idx}].SOP2RemarkImage`,
            sop.soP2RemarkImage || ''
          );
          if (sop.soP2RemarkImageObj) {
            SOPFormData.append(
              `SOP2s[${idx}].SOP2RemarkImageObj`,
              sop.soP2RemarkImageObj
            );
          }

          // 刪除標記 - 修正字段名稱
          SOPFormData.append(
            `SOP2s[${idx}].IsDeletedSOP2Image`,
            isDeletedSOP2Image
          );
          SOPFormData.append(
            `SOP2s[${idx}].IsDeletedSOP2RemarkImage`,
            isDeletedSOP2RemarkImage
          );
          SOPFormData.append(
            `SOP2s[${idx}].IsDeletedSOPVideo`,
            isDeletedSOPVideo
          );

          // PLC 相關 - 字段名稱正確
          SOPFormData.append(`SOP2s[${idx}].PLC1`, sop.plC1 || '');
          SOPFormData.append(`SOP2s[${idx}].PLC2`, sop.plC2 || '');
          SOPFormData.append(`SOP2s[${idx}].PLC3`, sop.plC3 || '');
          SOPFormData.append(`SOP2s[${idx}].PLC4`, sop.plC4 || '');

          // 影片相關 - 修正字段名稱和處理邏輯
          // 確保 SOPVideo 字段總是有值（後端要求非空）
          let sopVideoValue = '';
          if (sop.sopVideoObj && sop.sopVideoObj instanceof File) {
            sopVideoValue = sop.sopVideoObj.name;
            SOPFormData.append(`SOP2s[${idx}].SOPVideoObj`, sop.sopVideoObj);
          } else if (sop.sopVideo) {
            sopVideoValue = sop.sopVideo;
          }
          SOPFormData.append(`SOP2s[${idx}].SOPVideo`, sopVideoValue);

          // 3D模型相關 - 修正字段名稱以匹配後端模型
          if (sop.sopModels && Array.isArray(sop.sopModels)) {
            sop.sopModels.forEach((sopModel, j) => {
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelId`,
                sopModel.sopModelId || 0
              );
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].Deleted`,
                sopModel.deleted || 0
              );
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelImage`,
                sopModel.sopModelImage || ''
              );
              if (sopModel.sopModelImageObj) {
                SOPFormData.append(
                  `SOP2s[${idx}].SOPModels[${j}].SOPModelImageObj`,
                  sopModel.sopModelImageObj
                );
              }
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelFile`,
                sopModel.sopModelFile || ''
              );
              if (sopModel.sopModelFileObj) {
                SOPFormData.append(
                  `SOP2s[${idx}].SOPModels[${j}].SOPModelFileObj`,
                  sopModel.sopModelFileObj
                );
              }

              // 位置、旋轉、縮放參數 - 修正字段名稱
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelPX`,
                sopModel.sopModelPX || 0.0
              );
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelPY`,
                sopModel.sopModelPY || 0.0
              );
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelPZ`,
                sopModel.sopModelPZ || 0.0
              );
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelRX`,
                sopModel.sopModelRX || 0.0
              );
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelRY`,
                sopModel.sopModelRY || 0.0
              );
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelRZ`,
                sopModel.sopModelRZ || 0.0
              );
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelSX`,
                sopModel.sopModelSX || 1.0
              );
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelSY`,
                sopModel.sopModelSY || 1.0
              );
              SOPFormData.append(
                `SOP2s[${idx}].SOPModels[${j}].SOPModelSZ`,
                sopModel.sopModelSZ || 1.0
              );
            });
          }

          // T3D模型數據
          const t3dModelsData = formatT3DModels(sop);
          SOPFormData.append(`SOP2s[${idx}].T3DModels`, t3dModelsData);
        });

        const saveSOPInfoRes = await apiSaveSOP2(SOPFormData);

        if (saveSOPInfoRes && saveSOPInfoRes.message === '完全成功') {
          const successMessage = SOPInfo.knowledgeBaseId
            ? '編輯保存成功!'
            : '知識保存成功!';
          toast.success(successMessage, {
            position: toast.POSITION.TOP_CENTER,
            autoClose: 2000,
            hideProgressBar: true,
            closeOnClick: false,
            pauseOnHover: true,
          });

          // 延遲2秒後重置載入狀態，讓用戶能看到載入動畫
          setTimeout(() => {
            setIsLoading(false);
            setSOPInfo(null); // Reset or update SOP information
            setTimeout(() => {
              window.location.href = '/knowledge';
            }, 500); // 稍微延遲跳轉，讓狀態更新完成
          }, 2000);
        } else {
          // 處理保存失敗的情況
          const errorMessage = saveSOPInfoRes?.message || '保存失敗，請稍後重試';
          toast.error(errorMessage, {
            position: toast.POSITION.TOP_CENTER,
            autoClose: 3000,
            hideProgressBar: true,
            closeOnClick: false,
            pauseOnHover: true,
          });

          // 延遲2秒後重置載入狀態，讓用戶能看到載入動畫
          setTimeout(() => {
            setIsLoading(false);
          }, 2000);
          return; // 不要繼續執行後續邏輯
        }
      } catch (err) {
        console.error('保存知識庫失敗:', err);

        // 更詳細的錯誤處理
        let errorMessage = '保存失敗，請稍後重試';

        if (err.response && err.response.data) {
          // 處理後端驗證錯誤
          if (err.response.data.errors) {
            const errors = err.response.data.errors;
            const errorMessages = [];

            Object.keys(errors).forEach(key => {
              if (Array.isArray(errors[key])) {
                errorMessages.push(...errors[key]);
              } else {
                errorMessages.push(errors[key]);
              }
            });

            errorMessage = errorMessages.join('; ');
          } else if (err.response.data.message) {
            errorMessage = err.response.data.message;
          }
        } else if (err.message) {
          errorMessage = err.message;
        }

        toast.error(`保存失敗: ${errorMessage}`, {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 5000,
          hideProgressBar: true,
          closeOnClick: false,
          pauseOnHover: true,
        });

        // 延遲2秒後重置載入狀態，讓用戶能看到載入動畫
        setTimeout(() => {
          setIsLoading(false);
        }, 2000);
      }
    }
  };

  // 添加新的鍵盤事件處理函數，按Enter鍵儲存
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    }
  };

  return (
    <div>
      <Modal
        show={showModal}
        onHide={handleCloseModal}
        backdrop="static"
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>新增知識</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group className="mb-3">
            <Form.Label>帳號</Form.Label>
            <Form.Control placeholder="最高管理員" disabled />
          </Form.Group>
          <Form onSubmit={(e) => e.preventDefault()}>
            <Form.Group className="mb-3">
              <Form.Label>
                <span className="text-danger">*</span> SOP名稱
              </Form.Label>
              <input
                type="text"
                className="form-control"
                name="knowledgeBaseSOPName"
                value={sop2Name}
                onChange={handleEditChange}
                onBlur={handleEditBlur}
                onKeyDown={handleKeyDown} // 添加新的鍵盤事件處理函數，按Enter鍵儲存
                autoComplete="off"
              />
              {errors.sop2Name && (
                <div className="invalid-feedback d-block">
                  <i className="fas fa-exclamation-circle"></i>{' '}
                  {t('helpWord.required')}
                </div>
              )}
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal}>
            取消
          </Button>
          <Button variant="primary" onClick={handleSave} disabled={isLoading}>
            {isLoading ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                儲存中...
              </>
            ) : (
              '儲存'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

/* 一般用戶按鈕禁用樣式 */

/* 基本禁用樣式 - 保持原本顏色 */
.general-user-disabled {
  position: relative;
  transition: all 0.2s ease-in-out !important;
  /* 保持原本的按鈕樣式，不做任何改變 */
}

/* 一般用戶按鈕hover效果 - 只在hover時變灰色 */
.general-user-disabled:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  cursor: not-allowed !important;
  color: #fff !important;
  opacity: 0.8 !important;
  transition: all 0.15s ease-in-out !important;
}

/* 確保鼠標移開時立即恢復原本顏色 - 關鍵修復 */
.general-user-disabled:not(:hover):not(:focus):not(:active) {
  /* 讓按鈕恢復到原本的樣式，不強制覆蓋顏色 */
  cursor: default !important;
  transition: all 0.15s ease-in-out !important;
}

/* 針對不同按鈕類型的hover效果 */
.general-user-disabled.btn-primary:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
}

.general-user-disabled.btn-outline-primary:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #fff !important;
}

.general-user-disabled.btn-outline-info:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #fff !important;
}

.general-user-disabled.btn-outline-danger:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #fff !important;
}

.general-user-disabled.btn-add:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #fff !important;
}

.general-user-disabled.btn-search:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #fff !important;
}

/* 禁用按鈕的點擊和焦點效果 - 保持原本顏色 */
.general-user-disabled:active,
.general-user-disabled:focus,
.general-user-disabled:focus-visible {
  /* 移除focus效果，但保持原本按鈕顏色 */
  box-shadow: none !important;
  outline: none !important;
}

/* 確保點擊後立即失去焦點，恢復原本樣式 */
.general-user-disabled:focus:not(:hover) {
  /* 不強制改變顏色，讓按鈕保持原本樣式 */
  box-shadow: none !important;
  outline: none !important;
  transition: all 0.15s ease-in-out !important;
}

/* 確保禁用狀態下的游標樣式 */
.general-user-disabled,
.general-user-disabled * {
  cursor: not-allowed !important;
}

/* 針對特殊按鈕樣式的處理 */
.general-user-disabled.btn-cancel:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #fff !important;
}

.general-user-disabled.btn-save:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #fff !important;
}

/* 防止按鈕內容被選取 */
.general-user-disabled {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 針對圖標的處理 */
.general-user-disabled i {
  cursor: not-allowed !important;
}

/* 針對連結樣式的按鈕 */
.general-user-disabled a {
  cursor: not-allowed !important;
  pointer-events: none;
}

/* 過渡效果 */
.general-user-disabled {
  transition: all 0.2s ease-in-out;
}

/* 自定義提示樣式 - 簡化版本，更穩定 */
.general-user-disabled[data-tooltip] {
  position: relative;
}

/* 自定義提示顯示 - 在按鈕左上方顯示 */
.general-user-disabled:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: calc(100% + 8px);
  left: 20%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.95);
  color: #fff;
  padding: 10px 14px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 2147483647;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  pointer-events: none;
  animation: tooltipFadeInTop 0.2s ease-out;
  border: 1px solid rgba(255,255,255,0.1);
}

/* 添加提示箭頭指向按鈕 - 向下指向 */
.general-user-disabled:hover::before {
  content: '';
  position: absolute;
  bottom: calc(100% + 2px);
  left: 20%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.95);
  z-index: 2147483646;
  pointer-events: none;
  animation: tooltipFadeInTop 0.2s ease-out;
}

/* 只有當按鈕有 data-tooltip 屬性且不為空時才顯示 */
.general-user-disabled:not([data-tooltip])::after,
.general-user-disabled[data-tooltip=""]::after {
  display: none !important;
}

/* 原有動畫效果 - 從按鈕下方淡入 */
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 新動畫效果 - 從按鈕上方淡入 */
@keyframes tooltipFadeInTop {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 確保在所有容器中都能正確顯示 */
.general-user-disabled:hover {
  z-index: 999999;
}

/* 修復可能的容器 overflow 問題 */
.table-responsive,
.container-fluid,
.content {
  overflow: visible !important;
}


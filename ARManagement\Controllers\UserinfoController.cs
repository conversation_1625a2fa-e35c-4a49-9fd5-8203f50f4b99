﻿using ARManagement.BaseRepository.Interface;
using ARManagement.Helpers;
using Microsoft.AspNetCore.Mvc;
using Models;
using System.Diagnostics;
using System.Text;
using System.Text.RegularExpressions;
using System.Security.Cryptography;


namespace ARManagement.Controllers
{
    public class UserinfoController : MyBaseApiController
    {
        private readonly IBaseRepository _baseRepository;
        private readonly ResponseCodeHelper _responseCodeHelper;
        public UserinfoController(
            IBaseRepository baseRepository,
            ResponseCodeHelper responseCodeHelper)
        {
            _baseRepository = baseRepository;
            _responseCodeHelper = responseCodeHelper;
        }

        private static bool CheckVerificationCode(string email, string code)
        {
            if (_verificationCodes.TryGetValue(email, out var info))
            {
                return info.Code == code && DateTime.Now <= info.Expiry;
            }
            return false;
        }

        /// <summary>
        /// 發送郵箱驗證碼 - 向指定郵箱發送驗證碼，用於郵箱驗證
        /// </summary>
        /// <param name="email">目標郵箱地址</param>
        /// <returns>發送結果狀態</returns>
        [HttpPost("send-verification-code")]
        public ActionResult SendVerificationCode([FromBody] string email)
        {
            bool sent = SendVerificationCodeInternal(email);
            if (!sent)
            {
                return BadRequest("Unable to send verification code.");
            }
            return Ok("Verification code sent successfully.");
        }

        // Dictionary to hold email and verification code with expiry
        private static Dictionary<string, (string Code, DateTime Expiry)> _verificationCodes = new();

        private bool SendVerificationCodeInternal(string email)
        {
            var code = GenerateRandomCode();
            _verificationCodes[email] = (Code: code, Expiry: DateTime.Now.AddMinutes(10));  // 10 minutes expiry
                                                                                            // Implement actual email sending logic here with `code`
            return true; // Assume the email is always successfully sent.
        }

        private static string GenerateRandomCode()
        {
            var random = new Random();
            return random.Next(1000, 9999).ToString();  // Generates a 4-digit code
        }


        /// <summary>
        /// 驗證郵箱驗證碼 - 檢查用戶輸入的驗證碼是否正確有效
        /// </summary>
        /// <param name="email">郵箱地址</param>
        /// <param name="code">驗證碼</param>
        /// <returns>驗證結果</returns>
        [HttpPost("verify-email")]
        public ActionResult VerifyEmail(string email, string code)
        {
            bool isValid = CheckVerificationCode(email, code);
            if (!isValid)
            {
                return BadRequest("Invalid or expired verification code.");
            }
            return Ok("Email verified successfully.");
        }

        // 加密方法整合到Controller中
        private static string EncryptPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLowerInvariant();
            }
        }


        /// <summary>
        /// 取得個人用戶資訊 - 獲取當前登入用戶的詳細個人資料
        /// </summary>
        /// <returns>用戶個人資訊</returns>
        [HttpGet]
        public async Task<ActionResult<ApiResult<Userinfo>>> MyUserData()
        {
            ApiResult<Userinfo> apiResult = new ApiResult<Userinfo>(jwtToken.Token);

            try
            {
                #region 判斷Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                //將密碼移除
                myUser.UserPassword = string.Empty;

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                apiResult.Result = myUser;
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                exceptionMsg = ex.ToString();
                stackTrace = new StackTrace(ex);
            }

            return Ok(apiResult);
        }

        /// <summary>
        /// 用戶列表管理 - 根據篩選條件獲取系統中所有用戶的資訊列表
        /// </summary>
        /// <param name="post">篩選條件參數，包含關鍵字和權限條件</param>
        /// <returns>符合條件的用戶列表</returns>
        [HttpPost]
        public async Task<ActionResult<ApiResult<List<Userinfo>>>> GetAllUserinfoByFilter(PostUserinfoFilter post)
        {
            ApiResult<List<Userinfo>> apiResult = new ApiResult<List<Userinfo>>(jwtToken.Token);

            try
            {
                #region 判斷Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 判斷帳號是否被刪除或失效
                if (myUser == null)
                {
                    apiResult.Code = "1003"; //該帳號已被刪除或失效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                var where = $@"""Deleted"" = 0";

                if (!string.IsNullOrEmpty(post.Keyword))
                {
                    where += @" AND (""UserName"" LIKE CONCAT('%', @Keyword ,'%') OR ""UserAccount"" LIKE CONCAT('%', @Keyword ,'%') )";
                }

                var userinfos = await _baseRepository.GetAllAsync<Userinfo>("Userinfo", where, new { Keyword = post.Keyword }, "\"UserId\" ASC");

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                apiResult.Result = userinfos;
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                exceptionMsg = ex.ToString();
                stackTrace = new StackTrace(ex);
            }

            return Ok(apiResult);
        }

        /// <summary>
        /// 取得單一用戶資訊 - 根據用戶ID獲取指定用戶的詳細資料
        /// </summary>
        /// <param name="post">包含用戶ID的請求參數</param>
        /// <returns>用戶詳細資訊</returns>
        [HttpPost]
        public async Task<ActionResult<ApiResult<Userinfo>>> GetOneUserinfo(PostId post)
        {
            ApiResult<Userinfo> apiResult = new ApiResult<Userinfo>(jwtToken.Token);

            try
            {
                #region 判斷Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 判斷帳號是否被刪除或失效
                if (myUser == null)
                {
                    apiResult.Code = "1003"; //該帳號已被刪除或失效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                var where = $@"""UserId"" = @UserId";

                var userinfo = await _baseRepository.GetOneAsync<Userinfo>("Userinfo", where, new { UserId = post.Id });

                if (userinfo == null)
                {
                    apiResult.Code = "4001"; //資料庫或是實體檔案不存在
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }

                if (userinfo.Deleted == (byte)DeletedDataEnum.True)
                {
                    apiResult.Code = "4002"; //此資料已被刪除
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                apiResult.Result = userinfo;
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                exceptionMsg = ex.ToString();
                stackTrace = new StackTrace(ex);
            }

            return Ok(apiResult);
        }

        /// <summary>
        /// 新增用戶資訊 - 建立新的系統用戶帳號和基本資料
        /// </summary>
        /// <param name="post">新用戶的完整資料</param>
        /// <returns>建立結果狀態</returns>
        [HttpPut]
        public async Task<ActionResult<ApiResult<int>>> AddUserinfo(PostAddUserinfo post)
        {
            ApiResult<int> apiResult = new ApiResult<int>(jwtToken.Token);

            try
            {
                #region 判斷Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 判斷帳號是否為系統管理員
                if ((myUser.UserLevel & ((byte)UserLevelEnum.Admin | (byte)UserLevelEnum.Expert)) == 0)
                {
                    apiResult.Code = "3002"; //您不具有新增的權限
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 欄位驗證

                #region 必填欄位
                if (string.IsNullOrEmpty(post.UserName) ||
                    string.IsNullOrEmpty(post.UserAccount) ||
                    string.IsNullOrEmpty(post.UserPaw) ||
                    string.IsNullOrEmpty(post.UserAgainPaw))
                {
                    apiResult.Code = "2003"; //有必填欄位尚未填寫
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 欄位長度
                if (post.UserName.Length > 50)
                {
                    apiResult.Code = "2005"; //輸入文字字數不符合長度規範
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }

                if (post.UserAccount.Length > 50)
                {
                    apiResult.Code = "2005"; //輸入文字字數不符合長度規範
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 合法欄位
                if (!Enum.IsDefined(typeof(UserLevelEnum), post.UserLevel))
                {
                    apiResult.Code = "2004"; //不合法的欄位
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 2次密碼是否相同
                if (post.UserPaw != post.UserAgainPaw)
                {
                    apiResult.Code = "2002"; //密碼與確認密碼不一致
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 帳號是否存在
                var where = $@"""UserAccount"" = @UserAccount";

                var userinfo = await _baseRepository.GetOneAsync<Userinfo>("Userinfo", where, new { UserAccount = post.UserAccount });

                if (userinfo != null)
                {
                    apiResult.Code = "2001"; //該帳號已存在
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #endregion

                //密碼加密
                EDFunction edFunction = new EDFunction();
                //var paw = edFunction.GetSHA256Encryption(post.UserPaw);

                // 密碼加密，使用 SHA256 哈希方法
                string paw = ComputeSha256Hash(post.UserPaw);

                //新增用戶
                Dictionary<string, object> addUserinfo_Dict = new Dictionary<string, object>()
                {
                    { "@CompanyId", 1},
                    { "@UserName", post.UserName},
                    { "@UserAccount", post.UserAccount},
                    { "@UserPassword", paw},
                    { "@UserLevel", post.UserLevel},
                    { "@Creator", myUser.UserId},
                };

                var userId = await _baseRepository.AddOneByCustomTable(addUserinfo_Dict, "Userinfo", "UserId");

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                apiResult.Result = userId;
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                exceptionMsg = ex.ToString();
                stackTrace = new StackTrace(ex);
            }

            return Ok(apiResult);
        }

        /// <summary>
        /// 修改用戶資訊 - 更新指定用戶的基本資料和權限設定
        /// </summary>
        /// <param name="post">用戶更新資料</param>
        /// <returns>更新結果狀態</returns>
        [HttpPut]
        public async Task<ActionResult<ApiResult<int>>> EditUserinfo(PostEditUserinfo post)
        {
            ApiResult<int> apiResult = new ApiResult<int>(jwtToken.Token);

            try
            {
                #region 判斷Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 判斷帳號是否為系統管理員
                if ((myUser.UserLevel & ((byte)UserLevelEnum.Admin | (byte)UserLevelEnum.Expert)) == 0)
                {
                    apiResult.Code = "3003"; //您不具有修改的權限
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 欄位驗證

                #region 必填欄位
                if (string.IsNullOrEmpty(post.UserName))
                {
                    apiResult.Code = "2003"; //有必填欄位尚未填寫
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 欄位長度
                if (post.UserName.Length > 50)
                {
                    apiResult.Code = "2005"; //輸入文字字數不符合長度規範
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 合法欄位
                if (!Enum.IsDefined(typeof(UserLevelEnum), post.UserLevel))
                {
                    apiResult.Code = "2004"; //不合法的欄位
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 使用者是否存在
                var where = $@"""UserId"" = @UserId";

                var userinfo = await _baseRepository.GetOneAsync<Userinfo>("Userinfo", where, new { UserId = post.UserId });

                if (userinfo == null)
                {
                    apiResult.Code = "4001"; //資料庫或是實體檔案不存在
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }

                if (userinfo.Deleted == (byte)DeletedDataEnum.True)
                {
                    apiResult.Code = "4002"; //此資料已被刪除
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #endregion

                //更新用戶
                Dictionary<string, object> updateUserinfo_Dict = new Dictionary<string, object>()
                {
                    { "UserId", userinfo.UserId},
                    { "@UserName", post.UserName},
                    { "@UserLevel", post.UserLevel},
                    { "@Updater", myUser.UserId},
                    { "@UpdateTime", DateTime.Now},
                };

                await _baseRepository.UpdateOneByCustomTable(updateUserinfo_Dict, "Userinfo", "\"UserId\" = @UserId");

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                exceptionMsg = ex.ToString();
                stackTrace = new StackTrace(ex);
            }

            return Ok(apiResult);
        }

        /// <summary>
        /// 用戶修改密碼 - 用戶自行更改登入密碼
        /// </summary>
        /// <param name="post">包含舊密碼和新密碼的請求參數</param>
        /// <returns>密碼修改結果</returns>
        [HttpPut]
        public async Task<ActionResult<ApiResult<int>>> UserinfoChangePaw(PostUserinfoChangePaw post)
        {
            ApiResult<int> apiResult = new ApiResult<int>(jwtToken.Token);

            try
            {
                #region 判斷Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 欄位驗證

                #region 必填欄位
                if (string.IsNullOrEmpty(post.NewPaw) ||
                    string.IsNullOrEmpty(post.AgainPaw))
                {
                    apiResult.Code = "2003"; //有必填欄位尚未填寫
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 密碼長度是否合法
                if (post.NewPaw.Length < 6 || post.NewPaw.Length > 30)
                {
                    apiResult.Code = "2005"; //輸入文字字數不符合長度規範
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 密碼是否合法
                //判斷是否有不合法的特殊符號
                var tempMailTooken = post.NewPaw;

                //取代字串
                tempMailTooken = Regex.Replace(tempMailTooken, "\\d", "");
                tempMailTooken = Regex.Replace(tempMailTooken, "[A-Z]", "");
                tempMailTooken = Regex.Replace(tempMailTooken, "[a-z]", "");
                tempMailTooken = Regex.Replace(tempMailTooken, "[,.~!@#$%^&*_+\\-=]", "");

                if (tempMailTooken.Length > 0)
                {
                    apiResult.Code = "2004"; //不合法的欄位
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 2次密碼是否相同
                if (post.NewPaw != post.AgainPaw)
                {
                    apiResult.Code = "2002"; //密碼與確認密碼不一致
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 使用者是否存在
                var where = $@"""UserId"" = @UserId";

                var userinfo = await _baseRepository.GetOneAsync<Userinfo>("Userinfo", where, new { UserId = post.UserId });

                if (userinfo == null)
                {
                    apiResult.Code = "4001"; //資料庫或是實體檔案不存在
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }

                if (userinfo.Deleted == (byte)DeletedDataEnum.True)
                {
                    apiResult.Code = "4002"; //此資料已被刪除
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #endregion

                //修改密碼
                EDFunction edFunction = new EDFunction();
                //var paw = edFunction.GetSHA256Encryption(post.NewPaw);

                // 密碼加密，使用 SHA256 哈希方法
                string paw = ComputeSha256Hash(post.NewPaw);

                //更新用戶
                Dictionary<string, object> updateUserinfo_Dict = new Dictionary<string, object>()
                {
                    { "UserId", userinfo.UserId},
                    { "@UserPassword", paw},
                    { "@Updater", myUser.UserId},
                    { "@UpdateTime", DateTime.Now},
                };

                await _baseRepository.UpdateOneByCustomTable(updateUserinfo_Dict, "Userinfo", "\"UserId\" = @UserId");

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                exceptionMsg = ex.ToString();
                stackTrace = new StackTrace(ex);
            }

            return Ok(apiResult);
        }

        /// <summary>
        /// 刪除用戶資訊 - 移除指定用戶的帳號和相關資料
        /// </summary>
        /// <param name="post">包含用戶ID的請求參數</param>
        /// <returns>刪除結果狀態</returns>
        [HttpDelete]
        public async Task<ActionResult<ApiResult<int>>> DeleteUserinfo(PostId post)
        {
            ApiResult<int> apiResult = new ApiResult<int>(jwtToken.Token);

            try
            {
                #region 判斷Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 判斷帳號是否為系統管理員
                if ((myUser.UserLevel & ((byte)UserLevelEnum.Admin | (byte)UserLevelEnum.Expert)) == 0)
                {
                    apiResult.Code = "3004"; //您不具有刪除的權限
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 使用者是否存在
                var where = $@"""UserId"" = @UserId";

                var userinfo = await _baseRepository.GetOneAsync<Userinfo>("Userinfo", where, new { UserId = post.Id });

                if (userinfo == null)
                {
                    apiResult.Code = "4001"; //資料庫或是實體檔案不存在
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }

                if (userinfo.Deleted == (byte)DeletedDataEnum.True)
                {
                    apiResult.Code = "4002"; //此資料已被刪除
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 不可以刪除自己
                if (userinfo.UserId == myUser.UserId)
                {
                    apiResult.Code = "1005"; //無權限刪除自己帳號
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                await _baseRepository.DeleteOne(userinfo.UserId, "Userinfo", "\"UserId\"", myUser.UserId);

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                exceptionMsg = ex.ToString();
                stackTrace = new StackTrace(ex);
            }

            return Ok(apiResult);
        }

        /// <summary>
        /// 管理員變更密碼 - 管理員為用戶重設登入密碼
        /// </summary>
        /// <param name="post">包含用戶ID和新密碼的請求參數</param>
        /// <returns>密碼變更結果</returns>
        [HttpPut]
        public async Task<ActionResult<ApiResult<int>>> ChangePaw(PostChangePaw post)
        {
            ApiResult<int> apiResult = new ApiResult<int>(jwtToken.Token);

            try
            {
                #region 判斷Token是否過期或無效
                if (tokenExpired)
                {
                    apiResult.Code = "1001"; //Token過期或無效
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 欄位驗證

                #region 必填欄位
                if (string.IsNullOrEmpty(post.OldPaw) ||
                    string.IsNullOrEmpty(post.NewPaw) ||
                    string.IsNullOrEmpty(post.AgainPaw))
                {
                    apiResult.Code = "2003"; //有必填欄位尚未填寫
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 判斷舊密碼是否正確
                EDFunction edFunction = new EDFunction();
                var userPaw = edFunction.GetSHA256Encryption(post.OldPaw);

                if (userPaw != myUser.UserPassword)
                {
                    apiResult.Code = "1006"; //舊密碼錯誤
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 密碼長度是否合法
                if (post.NewPaw.Length < 6 || post.NewPaw.Length > 30)
                {
                    apiResult.Code = "2005"; //輸入文字字數不符合長度規範
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 密碼是否合法
                //判斷是否有不合法的特殊符號
                var tempMailTooken = post.NewPaw;

                //取代字串
                tempMailTooken = Regex.Replace(tempMailTooken, "\\d", "");
                tempMailTooken = Regex.Replace(tempMailTooken, "[A-Z]", "");
                tempMailTooken = Regex.Replace(tempMailTooken, "[a-z]", "");
                tempMailTooken = Regex.Replace(tempMailTooken, "[,.~!@#$%^&*_+\\-=]", "");

                if (tempMailTooken.Length > 0)
                {
                    apiResult.Code = "2004"; //不合法的欄位
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #region 2次密碼是否相同
                if (post.NewPaw != post.AgainPaw)
                {
                    apiResult.Code = "2002"; //密碼與確認密碼不一致
                    apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                    return Ok(apiResult);
                }
                #endregion

                #endregion

                //var paw = edFunction.GetSHA256Encryption(post.NewPaw);
                // 密碼加密，使用直接的 SHA256 方法替代原來的 EDFunction
                string paw = ComputeSha256Hash(post.NewPaw);

                //更新用戶
                Dictionary<string, object> updateUserinfo_Dict = new Dictionary<string, object>()
                {
                    { "UserId", myUser.UserId},
                    { "@UserPassword", paw},
                    { "@Updater", myUser.UserId},
                    { "@UpdateTime", DateTime.Now},
                };

                await _baseRepository.UpdateOneByCustomTable(updateUserinfo_Dict, "Userinfo", "\"UserId\" = @UserId");

                apiResult.Code = "0000";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
            }
            catch (Exception ex)
            {
                apiResult.Code = "9999";
                apiResult.Message = _responseCodeHelper.GetResponseCodeString(apiResult.Code);
                exceptionMsg = ex.ToString();
                stackTrace = new StackTrace(ex);
            }

            return Ok(apiResult);
        }

        // ComputeSha256Hash 方法
        private static string ComputeSha256Hash(string rawData)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(rawData));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2")); // 保持以十六進制格式生成字符串
                }
                return builder.ToString();
            }
        }
    }
}

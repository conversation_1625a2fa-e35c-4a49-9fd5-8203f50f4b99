-- 平台資料庫建立腳本(Table script Query Tool使用)

-- Create sequences first
CREATE SEQUENCE IF NOT EXISTS public."EmailVerifications_id_seq";
CREATE SEQUENCE IF NOT EXISTS public.vendors_id_seq;
CREATE SEQUENCE IF NOT EXISTS public.vendors_userid_seq;
CREATE SEQUENCE IF NOT EXISTS public.debug_log_id_seq;
CREATE SEQUENCE IF NOT EXISTS public.error_log_id_seq;
CREATE SEQUENCE IF NOT EXISTS public.test_table_id_seq;
CREATE SEQUENCE IF NOT EXISTS public."SOPModel_SOPModelId_seq";

-- Create tables
CREATE TABLE IF NOT EXISTS public."Apilog"
(
    "ApiLogId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "Apiurl" character varying(1000) COLLATE pg_catalog."default",
    "ApiMathod" character varying(10) COLLATE pg_catalog."default",
    "UserId" integer,
    "RequestData" text COLLATE pg_catalog."default",
    "ResponseData" text COLLATE pg_catalog."default",
    "IPAddress" character varying(15) COLLATE pg_catalog."default",
    "Line" integer,
    "ExceptionMsg" text COLLATE pg_catalog."default",
    "CreatedTime" time without time zone DEFAULT now(),
    CONSTRAINT "Apilog_pkey" PRIMARY KEY ("ApiLogId")
);

ALTER TABLE IF EXISTS public."Apilog" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."EmailVerifications"
(
    id integer NOT NULL DEFAULT nextval('public."EmailVerifications_id_seq"'::regclass),
    email character varying(255) COLLATE pg_catalog."default" NOT NULL,
    verification_code character varying(255) COLLATE pg_catalog."default" NOT NULL,
    verification_code_expiry timestamp without time zone NOT NULL,
    is_verified boolean DEFAULT false,
    "Creator" integer NOT NULL DEFAULT 1,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    CONSTRAINT "EmailVerifications_pkey" PRIMARY KEY (id)
);

ALTER TABLE IF EXISTS public."EmailVerifications" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."Machine"
(
    "MachineId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "Deleted" smallint DEFAULT 0,
    "CompanyId" integer NOT NULL,
    "MachineCode" character varying(100) COLLATE pg_catalog."default" NOT NULL,
    "MachineName" character varying(100) COLLATE pg_catalog."default" NOT NULL,
    "MachineSpec" character varying(100) COLLATE pg_catalog."default" NOT NULL,
    "MachineImage" character varying(45) COLLATE pg_catalog."default",
    "MachineFile" character varying(45) COLLATE pg_catalog."default",
    "Creator" integer NOT NULL,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    CONSTRAINT "Machine_pkey" PRIMARY KEY ("MachineId")
);

ALTER TABLE IF EXISTS public."Machine" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."MachineAdd"
(
    "MachineAddId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "Deleted" smallint NOT NULL DEFAULT 0,
    "MachineType" character varying(100) COLLATE pg_catalog."default" NOT NULL,
    "ModelSeries" character varying(100) COLLATE pg_catalog."default" NOT NULL,
    "MachineModel" character varying(100) COLLATE pg_catalog."default",
    "Creator" integer NOT NULL,
    "CreatedTime" timestamp without time zone DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    "CompanyId" integer,
    "MachineAddCode" character varying(100) COLLATE pg_catalog."default",
    "MachineName" character varying(100) COLLATE pg_catalog."default" NOT NULL,
    "MachineImage" character varying(100) COLLATE pg_catalog."default",
    CONSTRAINT "MachineAdd_pkey" PRIMARY KEY ("MachineAddId")
);

ALTER TABLE IF EXISTS public."MachineAdd" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."KnowledgeBase"
(
    "KnowledgeBaseId" integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "Deleted" smallint DEFAULT 0,
    "KnowledgeBaseDeviceType" character varying(100) COLLATE pg_catalog."default",
    "KnowledgeBaseDeviceParts" character varying(100) COLLATE pg_catalog."default",
    "KnowledgeBaseRepairItems" character varying(100) COLLATE pg_catalog."default",
    "KnowledgeBaseRepairType" character varying(100) COLLATE pg_catalog."default",
    "KnowledgeBaseFileNo" character varying(50) COLLATE pg_catalog."default",
    "KnowledgeBaseAlarmCode" character varying(50) COLLATE pg_catalog."default",
    "KnowledgeBaseSpec" character varying(100) COLLATE pg_catalog."default",
    "KnowledgeBaseSystem" character varying(100) COLLATE pg_catalog."default",
    "KnowledgeBaseProductName" character varying(100) COLLATE pg_catalog."default",
    "KnowledgeBaseAlarmCause" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBaseAlarmDesc" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBaseAlarmOccasion" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBaseModelImage" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBaseToolsImage" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBasePositionImage" character varying(1000) COLLATE pg_catalog."default",
    "Creator" integer NOT NULL,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    "MachineAddId" integer NOT NULL,
    "MachineName" character varying(100) COLLATE pg_catalog."default",
    "KnowledgeBaseModelImageNames" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBaseToolsImageNames" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBasePositionImageNames" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBaseSOPName" character varying(100) COLLATE pg_catalog."default",
    "KnowledgeBase3DModelImage" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBase3DModelFile" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBase3DModelList" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBaseLogoImage" character varying(1000) COLLATE pg_catalog."default",
    "KnowledgeBaseLogoImageNames" character varying(1000) COLLATE pg_catalog."default",
    CONSTRAINT "KnowledgeBase_pkey" PRIMARY KEY ("KnowledgeBaseId"),
    CONSTRAINT "FK_MachineAddId" FOREIGN KEY ("MachineAddId")
        REFERENCES public."MachineAdd" ("MachineAddId") MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

ALTER TABLE IF EXISTS public."KnowledgeBase" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."MachineAlarm"
(
    "MachineAlarmId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "Deleted" smallint DEFAULT 0,
    "MachineId" integer NOT NULL,
    "MachineAlarmCode" character varying(50) COLLATE pg_catalog."default" NOT NULL,
    "MachineAlarmAbstract" character varying(1000) COLLATE pg_catalog."default" NOT NULL,
    "Creator" integer NOT NULL,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    CONSTRAINT "MachineAlarm_pkey" PRIMARY KEY ("MachineAlarmId")
);

ALTER TABLE IF EXISTS public."MachineAlarm" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."MachineDevice"
(
    "MachineDeviceId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "Deleted" smallint DEFAULT 0,
    "MachineId" integer NOT NULL,
    "MachineDeviceControlerModel" character varying(100) COLLATE pg_catalog."default",
    "MachineDeviceServerIP" character varying(20) COLLATE pg_catalog."default",
    "MachineDeviceServerPort" integer,
    "MachineDeviceMachineIP" character varying(20) COLLATE pg_catalog."default",
    "Creator" integer NOT NULL,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    CONSTRAINT "MachineDeviceId_pkey" PRIMARY KEY ("MachineDeviceId")
);

ALTER TABLE IF EXISTS public."MachineDevice" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."MachineIOT"
(
    "MachineIOTId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "Deleted" smallint DEFAULT 0,
    "MachineId" integer NOT NULL,
    "MachineIOTDeviceName" character varying(1000) COLLATE pg_catalog."default" NOT NULL,
    "MachineIOTMQTTBroker" character varying(1000) COLLATE pg_catalog."default" NOT NULL,
    "MachineIOTClientId" character varying(1000) COLLATE pg_catalog."default" NOT NULL,
    "MachineIOTUserName" character varying(50) COLLATE pg_catalog."default" NOT NULL,
    "MachineIOTPassword" character varying(1000) COLLATE pg_catalog."default" NOT NULL,
    "Creator" integer NOT NULL,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    CONSTRAINT "MachineIOT_pkey" PRIMARY KEY ("MachineIOTId")
);

ALTER TABLE IF EXISTS public."MachineIOT" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."MachineIOTTopic"
(
    "TopicId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "Deleted" smallint DEFAULT 0,
    "MachineIOTId" integer NOT NULL,
    "TopicValue" character varying(1000) COLLATE pg_catalog."default" NOT NULL,
    "Creator" integer NOT NULL,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    CONSTRAINT "MachineIOTTopic_pkey" PRIMARY KEY ("TopicId")
);

ALTER TABLE IF EXISTS public."MachineIOTTopic" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."SOP"
(
    "SOPId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "Deleted" smallint DEFAULT 0,
    "MachineAlarmId" integer NOT NULL,
    "SOPStep" integer NOT NULL,
    "SOPMessage" character varying(1000) COLLATE pg_catalog."default",
    "SOPImage" character varying(45) COLLATE pg_catalog."default",
    "SOPVideo" character varying(45) COLLATE pg_catalog."default",
    "SOPPLC1" character varying(10) COLLATE pg_catalog."default",
    "SOPPLC2" character varying(10) COLLATE pg_catalog."default",
    "SOPPLC3" character varying(10) COLLATE pg_catalog."default",
    "SOPPLC4" character varying(10) COLLATE pg_catalog."default",
    "Creator" integer NOT NULL,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    CONSTRAINT "SOP_pkey" PRIMARY KEY ("SOPId")
);

ALTER TABLE IF EXISTS public."SOP" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."SOP2"
(
    "Deleted" smallint DEFAULT 0,
    "SOP2Step" integer,
    "SOP2Message" character varying(1000) COLLATE pg_catalog."default",
    "SOP2Image" character varying(50) COLLATE pg_catalog."default" DEFAULT 'image_value'::character varying,
    "SOP2Remark" character varying(1000) COLLATE pg_catalog."default",
    "SOP2RemarkImage" character varying(50) COLLATE pg_catalog."default",
    "SOP2Name" character varying(100) COLLATE pg_catalog."default",
    "Creator" integer,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    "SOP2Id" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "MachineAddId" integer,
    "KnowledgeBaseId" integer,
    "PLC1" character varying(100) COLLATE pg_catalog."default",
    "PLC2" character varying(100) COLLATE pg_catalog."default",
    "PLC3" character varying(100) COLLATE pg_catalog."default",
    "PLC4" character varying(100) COLLATE pg_catalog."default",
    "T3DModels" text COLLATE pg_catalog."default" DEFAULT '[]'::jsonb,
    "SOPVideo" character varying(45) COLLATE pg_catalog."default",
    CONSTRAINT "SOP2_pkey" PRIMARY KEY ("SOP2Id"),
    CONSTRAINT fk_machineadd FOREIGN KEY ("MachineAddId")
        REFERENCES public."MachineAdd" ("MachineAddId") MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

ALTER TABLE IF EXISTS public."SOP2" OWNER to postgres;


CREATE TABLE IF NOT EXISTS public."SOPModel"
(
    "Deleted" smallint DEFAULT 0,
    "SOPId" integer,
    "SOPModelImage" character varying(45) COLLATE pg_catalog."default",
    "SOPModelFile" character varying(45) COLLATE pg_catalog."default",
    "SOPModelPX" double precision DEFAULT 0.0,
    "SOPModelPY" double precision DEFAULT 0.0,
    "SOPModelPZ" double precision DEFAULT 0.0,
    "SOPModelRX" double precision DEFAULT 0.0,
    "SOPModelRY" double precision DEFAULT 0.0,
    "SOPModelRZ" double precision DEFAULT 0.0,
    "SOPModelSX" double precision DEFAULT 0.0,
    "SOPModelSY" double precision DEFAULT 0.0,
    "SOPModelSZ" double precision DEFAULT 0.0,
    "IsCommon" smallint DEFAULT 0,
    "Creator" integer,
    "CreatedTime" timestamp without time zone DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    "SOPModelId" integer NOT NULL DEFAULT nextval('public."SOPModel_SOPModelId_seq"'::regclass),
    CONSTRAINT "SOPModel_pkey" PRIMARY KEY ("SOPModelId")
);

ALTER TABLE IF EXISTS public."SOPModel" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."Userinfo"
(
    "UserId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
    "Deleted" smallint DEFAULT 0,
    "CompanyId" integer NOT NULL,
    "UserName" character varying(50) COLLATE pg_catalog."default" NOT NULL,
    "UserAccount" character varying(50) COLLATE pg_catalog."default" NOT NULL,
    "UserPassword" character varying(256) COLLATE pg_catalog."default" NOT NULL,
    "UserLevel" smallint DEFAULT 0,
    "Creator" integer NOT NULL,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    CONSTRAINT "Userinfo_pkey" PRIMARY KEY ("UserId"),
    CONSTRAINT "UserAccount_unique" UNIQUE ("UserAccount")
);

ALTER TABLE IF EXISTS public."Userinfo" OWNER to postgres;

CREATE TABLE IF NOT EXISTS public."Vendors"
(
    id integer NOT NULL DEFAULT nextval('public.vendors_id_seq'::regclass),
    company_name character varying(255) COLLATE pg_catalog."default" NOT NULL,
    contact_name character varying(255) COLLATE pg_catalog."default" NOT NULL,
    email character varying(255) COLLATE pg_catalog."default" NOT NULL,
    phone_number character varying(255) COLLATE pg_catalog."default",
    industry_type character varying(255) COLLATE pg_catalog."default",
    password_hash character varying(255) COLLATE pg_catalog."default" NOT NULL,
    "Creator" integer NOT NULL DEFAULT 1,
    "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
    "Updater" integer,
    "UpdateTime" timestamp without time zone,
    userid integer DEFAULT nextval('public.vendors_userid_seq'::regclass),
    schema_name character varying(50) COLLATE pg_catalog."default",
    CONSTRAINT vendors_pkey PRIMARY KEY (id),
    CONSTRAINT "Vendors_userid_key" UNIQUE (userid),
    CONSTRAINT vendors_email_key UNIQUE (email),
    CONSTRAINT vendors_email_unique UNIQUE (email),
    CONSTRAINT vendors_userid_unique UNIQUE (userid)
);

ALTER TABLE IF EXISTS public."Vendors" OWNER to postgres;

CREATE UNIQUE INDEX IF NOT EXISTS vendors_schema_name_idx
    ON public."Vendors" USING btree
    (schema_name COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;

CREATE TABLE IF NOT EXISTS public.debug_log
(
    id integer NOT NULL DEFAULT nextval('public.debug_log_id_seq'::regclass),
    message text COLLATE pg_catalog."default",
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT debug_log_pkey PRIMARY KEY (id)
);

ALTER TABLE IF EXISTS public.debug_log OWNER to postgres;

CREATE TABLE IF NOT EXISTS public.error_log
(
    id integer NOT NULL DEFAULT nextval('public.error_log_id_seq'::regclass),
    error_message text COLLATE pg_catalog."default",
    error_context text COLLATE pg_catalog."default",
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT error_log_pkey PRIMARY KEY (id)
);

ALTER TABLE IF EXISTS public.error_log OWNER to postgres;

CREATE TABLE IF NOT EXISTS public.test_table
(
    id integer NOT NULL DEFAULT nextval('public.test_table_id_seq'::regclass),
    name text COLLATE pg_catalog."default",
    CONSTRAINT test_table_pkey PRIMARY KEY (id)
);

ALTER TABLE IF EXISTS public.test_table OWNER to postgres;

-- Create triggers
CREATE OR REPLACE FUNCTION public.update_knowledge_base_deleted()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
BEGIN
    IF NEW."Deleted" = 1 THEN
        UPDATE public."KnowledgeBase"
        SET "Deleted" = 1
        WHERE "MachineAddId" = NEW."MachineAddId";
    END IF;
    RETURN NEW;
END;
$BODY$;

ALTER FUNCTION public.update_knowledge_base_deleted()
    OWNER TO postgres;

CREATE TRIGGER trigger_update_knowledge_base_deleted
    AFTER UPDATE 
    ON public."MachineAdd"
    FOR EACH ROW
    EXECUTE FUNCTION public.update_knowledge_base_deleted();

CREATE OR REPLACE FUNCTION public.create_user_schema()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
BEGIN
    EXECUTE 'CREATE SCHEMA IF NOT EXISTS ' || quote_ident(NEW.schema_name);
    RETURN NEW;
END;
$BODY$;

ALTER FUNCTION public.create_user_schema()
    OWNER TO postgres;

CREATE TRIGGER create_user_schema_trigger
    AFTER INSERT
    ON public."Vendors"
    FOR EACH ROW
    EXECUTE FUNCTION public.create_user_schema();

CREATE OR REPLACE FUNCTION public.generate_schema_name()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
BEGIN
    NEW.schema_name := 'user_' || NEW.userid;
    RETURN NEW;
END;
$BODY$;

ALTER FUNCTION public.generate_schema_name()
    OWNER TO postgres;

CREATE TRIGGER set_schema_name
    BEFORE INSERT
    ON public."Vendors"
    FOR EACH ROW
    EXECUTE FUNCTION public.generate_schema_name();

-- Step 1: Ensure the necessary sequence exists
CREATE SEQUENCE IF NOT EXISTS vendors_userid_seq;

-- Step 2: Modify the Vendors table
DO $$
BEGIN
    -- Add userid column if not exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'Vendors' AND column_name = 'userid') THEN
        ALTER TABLE public."Vendors" ADD COLUMN userid INTEGER;
    END IF;

    -- Set default value for userid
    ALTER TABLE public."Vendors" ALTER COLUMN userid SET DEFAULT nextval('vendors_userid_seq'::regclass);

    -- Add unique constraints
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'vendors_email_unique') THEN
        ALTER TABLE public."Vendors" ADD CONSTRAINT vendors_email_unique UNIQUE (email);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'vendors_userid_unique') THEN
        ALTER TABLE public."Vendors" ADD CONSTRAINT vendors_userid_unique UNIQUE (userid);
    END IF;
END $$;

-- Step 3: Create or replace the function to generate user schemas
CREATE OR REPLACE FUNCTION create_user_schema() RETURNS TRIGGER AS $$
DECLARE
    schema_name TEXT;
	debug_info INTEGER;  -- Declare debug_info variable
BEGIN
    -- Generate schema name
    schema_name := 'user_' || NEW.userid;
    
    -- Create new schema
    EXECUTE 'CREATE SCHEMA IF NOT EXISTS ' || schema_name;
    
    -- Create tables in the new schema
    -- Apilog
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."Apilog" (
        "ApiLogId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "Apiurl" character varying(1000),
        "ApiMathod" character varying(10),
        "UserId" integer,
        "RequestData" text,
        "ResponseData" text,
        "IPAddress" character varying(15),
        "Line" integer,
        "ExceptionMsg" text,
        "CreatedTime" time without time zone DEFAULT now(),
        CONSTRAINT "Apilog_pkey" PRIMARY KEY ("ApiLogId")
    )';
    
    -- KnowledgeBase
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."KnowledgeBase" (
        "KnowledgeBaseId" integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "Deleted" smallint DEFAULT 0,
        "KnowledgeBaseDeviceType" character varying(100),
        "KnowledgeBaseDeviceParts" character varying(100),
        "KnowledgeBaseRepairItems" character varying(100),
        "KnowledgeBaseRepairType" character varying(100),
        "KnowledgeBaseFileNo" character varying(50),
        "KnowledgeBaseAlarmCode" character varying(50),
        "KnowledgeBaseSpec" character varying(100),
        "KnowledgeBaseSystem" character varying(100),
        "KnowledgeBaseProductName" character varying(100),
        "KnowledgeBaseAlarmCause" character varying(1000),
        "KnowledgeBaseAlarmDesc" character varying(1000),
        "KnowledgeBaseAlarmOccasion" character varying(1000),
        "KnowledgeBaseModelImage" character varying(1000),
        "KnowledgeBaseToolsImage" character varying(1000),
        "KnowledgeBasePositionImage" character varying(1000),
        "Creator" integer NOT NULL,
        "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        "MachineAddId" integer NOT NULL,
        "MachineName" character varying(100),
        "KnowledgeBaseModelImageNames" character varying(1000),
        "KnowledgeBaseToolsImageNames" character varying(1000),
        "KnowledgeBasePositionImageNames" character varying(1000),
        "KnowledgeBaseSOPName" character varying(100),
        "KnowledgeBase3DModelImage" character varying(1000),
        "KnowledgeBase3DModelFile" character varying(1000),
        "KnowledgeBase3DModelList" character varying(1000),
        "KnowledgeBaseLogoImage" character varying(1000),
        "KnowledgeBaseLogoImageNames" character varying(1000),
        CONSTRAINT "KnowledgeBase_pkey" PRIMARY KEY ("KnowledgeBaseId")
    )';
    
    -- Machine
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."Machine" (
        "MachineId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "Deleted" smallint DEFAULT 0,
        "CompanyId" integer NOT NULL,
        "MachineCode" character varying(100) NOT NULL,
        "MachineName" character varying(100) NOT NULL,
        "MachineSpec" character varying(100) NOT NULL,
        "MachineImage" character varying(45),
        "MachineFile" character varying(45),
        "Creator" integer NOT NULL,
        "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        CONSTRAINT "Machine_pkey" PRIMARY KEY ("MachineId")
    )';
    
    -- MachineAdd
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."MachineAdd" (
        "MachineAddId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "Deleted" smallint NOT NULL DEFAULT 0,
        "MachineType" character varying(100) NOT NULL,
        "ModelSeries" character varying(100) NOT NULL,
        "MachineModel" character varying(100),
        "Creator" integer NOT NULL,
        "CreatedTime" timestamp without time zone DEFAULT now(),
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        "CompanyId" integer,
        "MachineAddCode" character varying(100),
        "MachineName" character varying(100) NOT NULL,
        "MachineImage" character varying(100),
        CONSTRAINT "MachineAdd_pkey" PRIMARY KEY ("MachineAddId")
    )';
    
    -- MachineAlarm
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."MachineAlarm" (
        "MachineAlarmId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "Deleted" smallint DEFAULT 0,
        "MachineId" integer NOT NULL,
        "MachineAlarmCode" character varying(50) NOT NULL,
        "MachineAlarmAbstract" character varying(1000) NOT NULL,
        "Creator" integer NOT NULL,
        "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        CONSTRAINT "MachineAlarm_pkey" PRIMARY KEY ("MachineAlarmId")
    )';
    
    -- MachineDevice
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."MachineDevice" (
        "MachineDeviceId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "Deleted" smallint DEFAULT 0,
        "MachineId" integer NOT NULL,
        "MachineDeviceControlerModel" character varying(100),
        "MachineDeviceServerIP" character varying(20),
        "MachineDeviceServerPort" integer,
        "MachineDeviceMachineIP" character varying(20),
        "Creator" integer NOT NULL,
        "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        CONSTRAINT "MachineDeviceId_pkey" PRIMARY KEY ("MachineDeviceId")
    )';
    
    -- MachineIOT
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."MachineIOT" (
        "MachineIOTId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "Deleted" smallint DEFAULT 0,
        "MachineId" integer NOT NULL,
        "MachineIOTDeviceName" character varying(1000) NOT NULL,
        "MachineIOTMQTTBroker" character varying(1000) NOT NULL,
        "MachineIOTClientId" character varying(1000) NOT NULL,
        "MachineIOTUserName" character varying(50) NOT NULL,
        "MachineIOTPassword" character varying(1000) NOT NULL,
        "Creator" integer NOT NULL,
        "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        CONSTRAINT "MachineIOT_pkey" PRIMARY KEY ("MachineIOTId")
    )';
    
    -- MachineIOTTopic
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."MachineIOTTopic" (
        "TopicId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "Deleted" smallint DEFAULT 0,
        "MachineIOTId" integer NOT NULL,
        "TopicValue" character varying(1000) NOT NULL,
        "Creator" integer NOT NULL,
        "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        CONSTRAINT "MachineIOTTopic_pkey" PRIMARY KEY ("TopicId")
    )';
    
    -- SOP
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."SOP" (
        "SOPId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "Deleted" smallint DEFAULT 0,
        "MachineAlarmId" integer NOT NULL,
        "SOPStep" integer NOT NULL,
        "SOPMessage" character varying(1000),
        "SOPImage" character varying(45),
        "SOPVideo" character varying(45),
        "SOPPLC1" character varying(10),
        "SOPPLC2" character varying(10),
        "SOPPLC3" character varying(10),
        "SOPPLC4" character varying(10),
        "Creator" integer NOT NULL,
        "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        CONSTRAINT "SOP_pkey" PRIMARY KEY ("SOPId")
    )';
    
    -- SOP2
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."SOP2" (
        "Deleted" smallint DEFAULT 0,
        "SOP2Step" integer,
        "SOP2Message" character varying(1000),
        "SOP2Image" character varying(50) DEFAULT ''image_value''::character varying,
        "SOP2Remark" character varying(1000),
        "SOP2RemarkImage" character varying(50),
        "SOP2Name" character varying(100),
        "Creator" integer,
        "CreatedTime" timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        "SOP2Id" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "MachineAddId" integer,
        "KnowledgeBaseId" integer,
        "PLC1" character varying(100),
        "PLC2" character varying(100),
        "PLC3" character varying(100),
        "PLC4" character varying(100),
        "T3DModels" text DEFAULT ''[]''::jsonb,
        "SOPVideo" character varying(45),
        CONSTRAINT "SOP2_pkey" PRIMARY KEY ("SOP2Id")
    )';
    
    -- SOPModel
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."SOPModel" (
        "Deleted" smallint DEFAULT 0,
        "SOPId" integer,
        "SOPModelImage" character varying(45),
        "SOPModelFile" character varying(45),
        "SOPModelPX" double precision DEFAULT 0.0,
        "SOPModelPY" double precision DEFAULT 0.0,
        "SOPModelPZ" double precision DEFAULT 0.0,
        "SOPModelRX" double precision DEFAULT 0.0,
        "SOPModelRY" double precision DEFAULT 0.0,
        "SOPModelRZ" double precision DEFAULT 0.0,
        "SOPModelSX" double precision DEFAULT 0.0,
        "SOPModelSY" double precision DEFAULT 0.0,
        "SOPModelSZ" double precision DEFAULT 0.0,
        "IsCommon" smallint DEFAULT 0,
        "Creator" integer,
        "CreatedTime" timestamp without time zone DEFAULT now(),
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        "SOPModelId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        CONSTRAINT "SOPModel_pkey" PRIMARY KEY ("SOPModelId")
    )';
    
    -- Userinfo
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."Userinfo" (
        "UserId" integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE ********** CACHE 1 ),
        "Deleted" smallint DEFAULT 0,
        "CompanyId" integer NOT NULL,
        "UserName" character varying(50) NOT NULL,
        "UserAccount" character varying(50) NOT NULL,
        "UserPassword" character varying(256) NOT NULL,
        "UserLevel" smallint DEFAULT 0,
        "Creator" integer NOT NULL,
        "CreatedTime" timestamp without time zone NOT NULL DEFAULT now(),
        "Updater" integer,
        "UpdateTime" timestamp without time zone,
        CONSTRAINT "Userinfo_pkey" PRIMARY KEY ("UserId"),
        CONSTRAINT "Userinfo_UserAccount_key" UNIQUE ("UserAccount")
    )';
    
    -- EmailVerifications
    EXECUTE 'CREATE TABLE IF NOT EXISTS ' || schema_name || '."EmailVerifications" (
        "id" SERIAL PRIMARY KEY,
        "email" VARCHAR(255) NOT NULL,
        "verification_code" VARCHAR(6) NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        "expires_at" TIMESTAMP WITH TIME ZONE NOT NULL,
        "is_verified" BOOLEAN DEFAULT FALSE
    )';
	
	-- Insert default admin account
   BEGIN
        EXECUTE format('
            INSERT INTO %I."Userinfo" ("CompanyId", "UserName", "UserAccount", "UserPassword", "UserLevel", "Creator")
            VALUES (1, ''Admin'', ''admin'', ''8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92'', 1, 1)
            ON CONFLICT ("UserAccount") DO NOTHING
        ', schema_name);
    EXCEPTION WHEN OTHERS THEN
        -- Log the error
        INSERT INTO public.error_log (error_message, error_context)
        VALUES (SQLERRM, format('Error inserting default admin into schema %s', schema_name));
    END;
    
    -- Verify if the insert was successful
    EXECUTE format('
        SELECT COUNT(*) FROM %I."Userinfo" WHERE "UserAccount" = ''admin''
    ', schema_name) INTO debug_info;
    
    -- Log the result
    INSERT INTO public.debug_log (message)
    VALUES (format('Schema %s created. Admin count: %s', schema_name, debug_info));

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create error_log and debug_log tables if they don't exist
CREATE TABLE IF NOT EXISTS public.error_log (
    id SERIAL PRIMARY KEY,
    error_message TEXT,
    error_context TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS public.debug_log (
    id SERIAL PRIMARY KEY,
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Step 4: Create trigger
DROP TRIGGER IF EXISTS create_user_schema_trigger ON public."Vendors";
CREATE TRIGGER create_user_schema_trigger
AFTER INSERT ON public."Vendors"
FOR EACH ROW
EXECUTE FUNCTION create_user_schema();

-- 插入預設admin帳號 (密碼: 123456)
-- SHA256("123456") = ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f
INSERT INTO public."Userinfo" ("Deleted", "CompanyId", "UserName", "UserAccount", "UserPassword", "UserLevel", "Creator", "CreatedTime")
VALUES (0, 1, 'Administrator', 'admin', '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92', 1, 1, NOW())
ON CONFLICT ("UserAccount") DO NOTHING;

-- 插入預設admin帳號 (密碼: 123456)
-- SHA256("123456") = 8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92
INSERT INTO public."Userinfo" ("Deleted", "CompanyId", "UserName", "UserAccount", "UserPassword", "UserLevel", "Creator", "CreatedTime")
VALUES (0, 1, 'Administrator', 'admin', '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92', 1, 1, NOW())
ON CONFLICT ("UserAccount") DO NOTHING;

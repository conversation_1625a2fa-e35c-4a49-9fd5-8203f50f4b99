.leftBoxCondition {
  width: 40%;
  float: left; /* 左浮動，使其位於左邊 */
  margin-left: 5%;
  min-height: 100%; /* 確保至少與容器同高 */
}

.leftBoxCondition p {
  font-size: 18px;
  margin: 0;
}

.rightBoxCondition {
  width: 48%;
  float: right; /* 右浮動，使其位於右邊 */
  margin-right: 5%;
}

.leftBoxCondition,
.rightBoxCondition {
  margin-top: 2vh;
  margin-bottom: 2vh;
}

.boxCondition1,
.boxCondition2 {
  width: 100%;
  border-radius: 5px;
  border: 1px solid rgb(219, 219, 219);
  overflow: hidden;
  box-shadow: 2px 2px 5px 5px rgba(117, 117, 117, 0.1);
}

.boxCondition2 {
  padding-top: 15px;
  margin-top: 25px;
  border: 1px solid rgb(219, 219, 219);
}

.conditionItem {
  border: 1px solid #b9b9b9;
  padding: 5px;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
  color: black;
  margin-bottom: 5px; /* 添加底部間隙 */
}

/* 設定 scrollBox 內的 conditionItem 樣式 */
.scrollBox .conditionItem {
  height: 40px; /* 統一高度 */
  margin: 5px 0; /* 提供一些垂直間距 */
  padding: 10px;
  background-color: #f9f9f9; /* 輕淺背景色 */
  border: 1px solid #ddd; /* 邊框 */
  display: flex;
  align-items: center;
  justify-content: space-between; /* 內容分散對齊 */
  transition:
    background-color 0.3s,
    border-color 0.3s; /* 添加延遲 */
}

.scrollBox {
  overflow-y: scroll;
  max-height: 356px; /* 設定一個最大高度，依據需求調整 */
  min-height: 356px;
  background-color: #ffffff;
  border: 1px solid #a3a3a3;
  border-radius: 5px;
  padding: 10px;
}

.conditionItem:hover {
  background-color: #eeeeee;
  border-color: #ccc;
}

.search {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px; /* 增加底部間隙 */
  border: 1px solid #a3a3a3;
  background-color: #f0f0f0;
  border-radius: 5px;
  outline: none;
  font-size: 16px;
}

.formGroupCondition {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 添加此屬性以在項目間平均分配空間 */
  margin-bottom: 18px;
  padding: 0 15px; /* 增加內邊距以避免內容擠壓 */
}

.customSelectCondition {
  flex-grow: 1; /* 讓選擇框區域能夠填滿除了標籤之外的剩餘空間 */
  position: relative;
  margin-left: 10px;
}

@import './styles/UserPermissions.css';

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 767px) {
  .visible-xs {
    display: block !important;
  }

  .hidden-xs {
    display: none !important;
  }
}

@media (min-width: 768px) {
  .visible-md {
    display: block !important;
  }

  .hidden-md {
    display: none !important;
  }
}

.btn-circle.btn-sm {
  width: 40px;
  height: 40px;
  padding: 6px 0px;
  border-radius: 20px;
  font-size: 16px;
  text-align: center;
}

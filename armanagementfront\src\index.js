import React from 'react';
import ReactDOM from 'react-dom/client';
import i18n from './i18n';
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from 'react-router-dom';
import PrivateRoute from './utils/PrivateRoute'; //Private Route
import Login from './pages/Login';
import Home from './pages/Home';
import Machine from './pages/Machine';
import UserManage from './pages/UserManage';
import MachineAlarm from './pages/MachineAlarm';
import SOP from './pages/SOP';
import SOP2 from './pages/SOP2';
import MachineIOTList from './pages/MachineIOTList';
import MachineIOT from './pages/MachineIOT';
import Knowledge from './pages/Knowledge';
import Database from './pages/Database';
import Alarm from './pages/Alarm';
import PageMindMap from './pages/PageMindMap';
import GPT from './pages/GPT';
import VendorsAccount from './components/VendorsAccount'; // 確保路徑正確
import PermissionTest from './pages/PermissionTest';

import './App.css';
import './index.css';
import './styles/UserPermissions.css'; // 權限管理樣式
import { RepairDocument } from './components/RepairDocument';
import { DocumentEditor } from './components/DocumentEditor';
import MachineKnowledge from './pages/MachineKnowledge';
import Assistant from './components/Assistant';
import MenuTest from './components/MenuTest';
import PDFDemo from './pages/PDFDemo';
import { useDatabase } from './components/useDatabse';
import { Preview } from './components/Preview';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <Router>
    <Routes>
      {/* 註釋Vendor路由，改用User登入系統 */}
      {/* <Route path="/" element={<VendorsAccount />} /> */}
      {/* <Route path="/vendorsAccount" element={<VendorsAccount />} /> */}
      
      {/* 使用User登入系統作為預設路由 */}
      <Route path="/" element={<Login />} />
      <Route path="/login" element={<Login />} />
      {/* <Route element={<PrivateRoute />}> */}
      <Route
        path="/pdfDemo"
        element={
          <PrivateRoute>
            <Home>
              <PDFDemo />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/menuTest"
        element={
          <PrivateRoute>
            <Home>
              <MenuTest />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/permissionTest"
        element={
          <PrivateRoute>
            <Home>
              <PermissionTest />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/machine"
        element={
          <PrivateRoute>
            <Home>
              <Machine />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/machineKnowledge"
        element={
          <PrivateRoute>
            <Home>
              <MachineKnowledge />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/machine/:machineId/machineAlarm"
        element={
          <PrivateRoute>
            <Home>
              <MachineAlarm />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/machine/:machineId/machineAlarm/:machineAlarmId/SOP"
        element={
          <PrivateRoute>
            <Home>
              <SOP />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/machine/:machineId/machineIOTList"
        element={
          <PrivateRoute>
            <Home>
              <MachineIOTList />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/machine/:machineId/machineIOTList/:machineIOTId"
        element={
          <PrivateRoute>
            <Home>
              <MachineIOT />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/userManage"
        element={
          <PrivateRoute>
            <Home>
              <UserManage />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/knowledge"
        element={
          <PrivateRoute>
            <Home>
              <Knowledge />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/sop2"
        element={
          <PrivateRoute>
            <Home>
              <SOP2 />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/database"
        element={
          <PrivateRoute>
            <Home>
              <Database />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/repairDocument"
        element={
          <PrivateRoute>
            <Home>
              <RepairDocument />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/preview"
        element={
          <PrivateRoute>
            <Home>
              <Preview />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/document-editor"
        element={
          <PrivateRoute>
            <Home>
              <DocumentEditor />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/alarm"
        element={
          <PrivateRoute>
            <Home>
              <Alarm />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/gpt"
        element={
          <PrivateRoute>
            <Home>
              <GPT />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/pageMindMap"
        element={
          <PrivateRoute>
            <Home>
              <PageMindMap />
            </Home>
          </PrivateRoute>
        }
      />
      <Route
        path="/repairDocument"
        element={
          <PrivateRoute>
            <Home>
              <RepairDocument />
            </Home>
          </PrivateRoute>
        }
      />
      {/* </Route> */}
    </Routes>
    {/* 小助手 */}
    {/* <Assistant /> */}
  </Router>
);

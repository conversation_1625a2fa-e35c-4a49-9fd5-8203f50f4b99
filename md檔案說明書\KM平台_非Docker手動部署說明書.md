# AR 管理系統 - 便攜式部署說明書 (Windows版本)

**版本：** v2.1  
**更新日期：** 2025年01月15日  
**適用系統：** Windows 10/11  
**框架版本：** .NET 8.0 + React  
**部署包名稱：** KM_deploy_noDocker (Windows)  

---

## 🚀 快速執行流程

### 開發環境打包步驟：

1. **建立部署資料夾**
   ```batch
   cd C:\Users\<USER>\Desktop
   mkdir "KM_deploy_noDocker (Windows)"
   cd "KM_deploy_noDocker (Windows)"
   mkdir backend, frontend, database, config, uploads
   ```

2. **複製資料庫檔案**
   ```batch
   cd C:\Users\<USER>\Desktop\WebAR_removeEmail\交接文件\程式碼\slnARManagement
   copy init.sql "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\database\"
   ```

3. **後端打包（解決套件降級問題）**
   ```batch
   dotnet publish ARManagement/ARManagement.csproj -c Release -o "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend" --self-contained true -r win-x64 -p:NoWarn=NU1605
   ```

4. **前端打包與複製**
   ```batch
   cd armanagementfront
   npm install
   npm run build
   
   # 複製到 frontend 資料夾（備份）
   xcopy /E /I /Y build\* "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\frontend\"
   
   # 複製到 backend/wwwroot（實際運行）
   xcopy /E /I /Y build\* "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend\wwwroot\"
   ```

5. **建立啟動腳本**
   - 執行第 2.5 和 2.6 節的腳本建立命令

### 目標主機部署步驟：

1. **安裝 PostgreSQL**
   - 下載並安裝 PostgreSQL 12+ 版本

2. **建立資料庫**
   ```sql
   CREATE DATABASE "AREditor";
   ```

3. **執行資料庫腳本**
   - 在 pgAdmin 中執行 `database\init.sql`

4. **啟動系統**
   ```batch
   # 雙擊執行根目錄的啟動檔案
   start.bat
   ```

5. **瀏覽器訪問**
   - 網址：http://localhost:8098
   - 帳號：admin / 密碼：123456

---

## 📋 目錄

1. [部署包準備](#1-部署包準備)
2. [手動打包流程](#2-手動打包流程)
3. [常見問題解決](#3-常見問題解決)
4. [部署資料夾結構](#4-部署資料夾結構)
5. [目標主機部署步驟](#5-目標主機部署步驟)
6. [配置說明](#6-配置說明)
7. [測試驗證](#7-測試驗證)
8. [故障排除](#8-故障排除)
9. [注意事項](#9-注意事項)

---

## 1. 部署包準備

### 1.1 系統需求
- **開發環境：** Windows 10/11 + .NET 8.0 SDK + Node.js
- **目標環境：** Windows 10/11 + PostgreSQL **（僅需這兩項！）**

### 1.2 目標主機零依賴說明
🎯 **重要：** 本部署方案採用 .NET 自包含部署，目標主機**不需要**：
- ❌ 安裝 .NET SDK 或 Runtime
- ❌ 安裝 Node.js 或 npm
- ❌ 通過 cmd 下載任何套件
- ❌ 安裝任何開發環境

### 1.3 開始前檢查
確認以下檔案和功能正常：
- [ ] 專案可在開發環境正常運行
- [ ] `init.sql` 檔案存在且完整（756行）
- [ ] 前端檔案可正常建置
- [ ] 資料庫連線正常

---

## 2. 手動打包流程

### 2.1 建立桌面部署資料夾

在 CMD 或 PowerShell 中依序執行：

```batch
# 切換到桌面
cd C:\Users\<USER>\Desktop

# 建立主要部署資料夾
mkdir "KM_deploy_noDocker (Windows)"

# 進入部署資料夾
cd "KM_deploy_noDocker (Windows)"

# 建立子資料夾
mkdir backend
mkdir frontend
mkdir database
mkdir config
mkdir uploads
```

### 2.2 複製資料庫檔案

```batch
# 回到專案根目錄
cd C:\Users\<USER>\Desktop\WebAR_removeEmail\交接文件\程式碼\slnARManagement

# 複製 init.sql 到部署資料夾
copy init.sql "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\database\"
```

### 2.3 後端打包步驟

⚠️ **重要：** 如果遇到套件降級錯誤，請使用以下正確指令：

```batch
# 確保在專案根目錄
cd C:\Users\<USER>\Desktop\WebAR_removeEmail\交接文件\程式碼\slnARManagement

# 執行 .NET 發布（解決套件降級問題）
dotnet publish ARManagement/ARManagement.csproj -c Release -o "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend" --self-contained true -r win-x64 -p:NoWarn=NU1605
```

**成功指標：** 看到以下訊息表示打包成功
```
ARManagement -> C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend\
```

### 2.4 前端打包與檔案複製

```batch
# 切換到前端目錄
cd armanagementfront

# 安裝前端依賴
npm install

# 建置前端
npm run build

# 複製前端建置結果到部署資料夾（備份）
xcopy /E /I /Y build\* "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\frontend\"

# 複製前端檔案到後端 wwwroot 目錄（實際運行）
xcopy /E /I /Y build\* "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend\wwwroot\"
```

⚠️ **重要說明：**
- **frontend 資料夾**：作為前端檔案備份和參考
- **backend/wwwroot 資料夾**：.NET 應用程式實際讀取的前端檔案位置

**此步驟解決的問題：**
- ✅ 解決 HTTP 404 錯誤
- ✅ 確保前端 UI 正常載入
- ✅ 前後端整合部署
- ✅ 提供前端檔案備份

**檔案複製說明：**
- 前端的 `index.html` 會成為系統主頁面
- 前端的 `appsetting.js` 確保 API 端點正確
- 靜態資源（CSS/JS）確保前端功能正常

### 2.5 建立啟動腳本

**建立 start.bat 啟動腳本：**
```batch
(
echo @echo off
echo chcp 65001 ^>nul
echo title AR管理系統 - 啟動中...
echo.
echo =====================================
echo        AR 管理系統 - 便攜式部署
echo =====================================
echo.
echo [1/4] 檢查 PostgreSQL 服務...
echo sc query postgresql-x64-13 ^>nul 2^>^&1
echo if errorlevel 1 ^(
echo     echo ❌ PostgreSQL 13 服務未找到或未啟動！
echo     echo 🚀 嘗試啟動 PostgreSQL 13 服務...
echo     net start postgresql-x64-13 ^>nul 2^>^&1
echo     if errorlevel 1 ^(
echo         echo ❌ PostgreSQL 服務啟動失敗！
echo         echo 請以管理員身份執行此腳本
echo     ^) else ^(
echo         echo ✅ PostgreSQL 13 服務已啟動
echo     ^)
echo ^) else ^(
echo     sc query postgresql-x64-13 ^| findstr STATE ^| findstr RUNNING ^>nul
echo     if errorlevel 1 ^(
echo         echo 🚀 PostgreSQL 13 服務存在但未運行，正在啟動...
echo         net start postgresql-x64-13 ^>nul 2^>^&1
echo         if errorlevel 1 ^(
echo             echo ❌ PostgreSQL 服務啟動失敗！請以管理員身份執行
echo         ^) else ^(
echo             echo ✅ PostgreSQL 13 服務已啟動
echo         ^)
echo     ^) else ^(
echo         echo ✅ PostgreSQL 13 服務運行正常
echo     ^)
echo ^)

echo.
echo cd /d "%%~dp0backend"
echo if not exist "uploads" mkdir uploads
echo echo ✅ 檔案儲存目錄已建立
echo.
echo echo [2/4] 啟動後端服務...
echo echo 🚀 正在啟動 AR 管理系統...
echo start "" "ARManagement.exe"
echo.
echo echo [3/4] 等待服務啟動...
echo timeout /t 8 /nobreak ^>nul
echo.
echo echo [4/4] 開啟瀏覽器...
echo start http://localhost:8098
echo.
echo echo =====================================
echo echo ✅ 系統啟動完成！
echo echo 🌐 網址: http://localhost:8098
echo echo 👤 預設帳號: admin
echo echo 🔑 預設密碼: 123456
echo echo =====================================
echo pause
) > "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\start.bat"
```

**建立 stop.bat 停止腳本：**
```batch
(
echo @echo off
echo chcp 65001 ^>nul
echo title AR管理系統 - 停止服務
echo.
echo =====================================
echo        停止 AR 管理系統服務
echo =====================================
echo.
echo taskkill /f /im ARManagement.exe ^>nul 2^>^&1
echo if errorlevel 1 ^(
echo     echo ❌ 沒有找到正在運行的服務
echo ^) else ^(
echo     echo ✅ AR 管理系統服務已停止
echo ^)
echo.
echo pause
) > "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\stop.bat"
```

### 2.6 建立配置說明檔案

```batch
(
echo # 配置檔案說明
echo.
echo ## appsettings.json 重要參數
echo.
echo ### 資料庫連線
echo ```json
echo "DBConfig": {
echo   "ConnectionString": "Server=127.0.0.1;Port=5432;User Id=postgres;Password=*********;Database=AREditor;"
echo }
echo ```
echo.
echo ### 服務端口
echo ```json
echo "Kestrel": {
echo   "EndPoints": {
echo     "Http": {
echo       "Url": "http://localhost:8098"
echo     }
echo   }
echo }
echo ```
echo.
echo ## 常見修改場景
echo.
echo ### 修改資料庫連線
echo 如果目標主機的 PostgreSQL 設定不同，請修改：
echo - Server: 資料庫伺服器位址
echo - Port: 資料庫端口
echo - User Id: 資料庫使用者名稱  
echo - Password: 資料庫密碼
echo.
echo ### 修改服務端口
echo 如果 8098 端口被佔用，請同時修改：
echo 1. appsettings.json 中的 Kestrel 端口
echo 2. wwwroot/appsetting.js 中的 apiUrl
) > "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\config\配置說明.md"
```

### 2.7 建立部署說明檔案

```batch
(
echo # AR 管理系統 - 便攜式部署包
echo.
echo ## 🚀 快速啟動
echo.
echo 1. **確認目標主機已安裝 PostgreSQL**
echo 2. **雙擊執行 `start.bat`**
echo 3. **系統會自動開啟瀏覽器到 http://localhost:8098**
echo.
echo ## 👤 預設登入資訊
echo.
echo - **帳號：** admin
echo - **密碼：** 123456
echo.
echo ## 📁 資料夾說明
echo.
echo - `backend\` - 後端執行程式 (包含完整 .NET Runtime)
echo - `database\` - 資料庫初始化腳本
echo - `start.bat & stop.bat` - 啟動/停止腳本
echo - `config\` - 配置說明文件
echo - `uploads\` - PDF檔案儲存目錄
echo.
echo ## ⚠️ 重要提醒
echo.
echo 1. **目標主機只需要安裝 PostgreSQL，無需其他環境**
echo 2. **首次使用前請先建立 AREditor 資料庫並執行 init.sql**
echo 3. **如需修改端口或資料庫連線，請參考 config\配置說明.md**
echo.
echo ## 📞 技術支援
echo.
echo - 版本：v2.1
echo - 更新日期：2025年01月15日
echo - 適用系統：Windows 10/11
) > "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\README.md"
```

### 2.8 驗證打包結果

```batch
# 檢查後端核心檔案
dir "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend\ARManagement.exe"
dir "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend\appsettings.json"

# 檢查資料庫檔案
dir "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\database\init.sql"

# 檢查前端檔案
dir "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\frontend\index.html"

# 檢查腳本檔案  
dir "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\start.bat"
dir "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\stop.bat"

# 檢查配置說明
dir "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\config\配置說明.md"
```

---

## 3. 常見問題解決

### 3.1 套件降級錯誤 (NU1605)

**問題現象：**
```
error NU1605: Warning As Error: 偵測到套件降級: System.IO.FileSystem.Primitives 從 4.3.0 降到 4.0.1
```

**解決方案：**

#### 方案一：忽略警告（推薦）
```batch
dotnet publish ARManagement/ARManagement.csproj -c Release -o "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend" --self-contained true -r win-x64 -p:NoWarn=NU1605
```

#### 方案二：設置不把警告當錯誤
```batch
dotnet publish ARManagement/ARManagement.csproj -c Release -o "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend" --self-contained true -r win-x64 -p:TreatWarningsAsErrors=false
```

#### 方案三：清理重建
```batch
dotnet clean
dotnet publish ARManagement/ARManagement.csproj -c Release -o "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend" --self-contained true -r win-x64 -p:TreatWarningsAsErrors=false
```

### 3.2 MSBuild 參數錯誤

**問題現象：**
```
MSBUILD : error MSB1001: 未知的參數。
參數: --no-warn
```

**解決方案：**
使用正確的 MSBuild 屬性語法：
```batch
# 錯誤語法
--no-warn NU1605

# 正確語法
-p:NoWarn=NU1605
```

### 3.3 PowerShell 語法問題

**問題現象：**
```
在這個版本中 '&&' 語彙基元不是有效的陳述式分隔符號。
```

**解決方案：**
在 PowerShell 中要分別執行命令，不能用 `&&` 連接：
```batch
# 錯誤語法
cd C:\path && mkdir folder

# 正確語法
cd C:\path
mkdir folder
```

---

## 4. 部署資料夾結構

完成打包後，桌面上的 `KM_deploy_noDocker (Windows)` 資料夾結構如下：

```
KM_deploy_noDocker (Windows)/
├── 📁 backend/                         # 後端執行檔案 (~150MB)
│   ├── ARManagement.exe               # 🔥 主程式 (142KB)
│   ├── appsettings.json               # 🔧 配置檔案 (1.6KB)
│   ├── wwwroot/                       # 前端資源
│   │   ├── index.html                # 前端入口
│   │   ├── appsetting.js             # 前端配置
│   │   └── static/                   # 靜態資源
│   ├── uploads/                      # PDF儲存目錄
│   └── 其他運行時檔案...
├── 📁 database/                        # 資料庫檔案
│   └── init.sql                      # 🗄️ 建表腳本 (34KB)
├── 📁 frontend/                        # 前端備份檔案
│   └── index.html                    # 前端入口 (828B)
├── 📁 config/                          # 配置說明
│   └── 配置說明.md                    # 📝 配置指南 (3.8KB)
├── 📁 uploads/                         # 檔案儲存目錄
├── 🚀 start.bat                        # 啟動腳本
├── ⏹️ stop.bat                         # 停止腳本
└── 📄 README.md                        # 📖 部署說明
```

**打包結果驗證清單：**
- ✅ ARManagement.exe (142,848 bytes)
- ✅ appsettings.json (1,638 bytes)  
- ✅ init.sql (34,458 bytes)
- ✅ index.html (828 bytes)
- ✅ start.bat (1,394 bytes)
- ✅ stop.bat (383 bytes)
- ✅ 配置說明.md (3,803 bytes)

---

## 5. 目標主機部署步驟

### 5.1 準備目標主機

#### 步驟 1：安裝 PostgreSQL（僅此一項！）
1. 下載並安裝 PostgreSQL（建議版本 12 以上）
2. 記住安裝時設定的 postgres 使用者密碼
3. 確認 PostgreSQL 服務已啟動

🎯 **重要提醒：** 除了 PostgreSQL，目標主機不需要安裝任何其他軟體！

#### 步驟 2：複製部署包
將整個 `KM_deploy_noDocker (Windows)` 資料夾複製到目標主機桌面

✅ **驗證：** 複製完成後，目標主機即可直接執行，無需額外安裝步驟

### 5.2 初始化資料庫

#### 步驟 1：開啟 pgAdmin 4
1. 啟動 pgAdmin 4
2. 連線到本地 PostgreSQL 伺服器

#### 步驟 2：建立資料庫
```sql
CREATE DATABASE "AREditor";
```

#### 步驟 3：執行初始化腳本
1. 右鍵點擊 `AREditor` 資料庫
2. 選擇 `Query Tool`
3. 開啟 `KM_deploy_noDocker (Windows)\database\init.sql`
4. 點擊執行按鈕 (⚡)
5. 確認執行成功（應建立約15個資料表）

### 5.3 啟動系統

#### 方法1：使用啟動腳本（推薦）
1. 雙擊 `KM_deploy_noDocker (Windows)\start.bat`
2. 等待自動開啟瀏覽器
3. 使用預設帳號登入：
   - 帳號：`admin`
   - 密碼：`123456`

#### 方法2：手動啟動
1. 進入 `KM_deploy_noDocker (Windows)\backend` 目錄
2. 雙擊 `ARManagement.exe`
3. 手動開啟瀏覽器訪問 `http://localhost:8098`

---

## 6. 配置說明

### 6.1 資料庫連線配置

如果目標主機的 PostgreSQL 設定與預設不同，請修改 `backend\appsettings.json`：

```json
"DBConfig": {
  "ConnectionString": "Server=主機位址;Port=端口;User Id=使用者名稱;Password=密碼;Database=AREditor;"
}
```

**常見配置範例：**
```json
// 預設本地配置
"ConnectionString": "Server=127.0.0.1;Port=5432;User Id=postgres;Password=*********;Database=AREditor;"

// 自訂密碼
"ConnectionString": "Server=127.0.0.1;Port=5432;User Id=postgres;Password=************;Database=AREditor;"

// 自訂端口
"ConnectionString": "Server=127.0.0.1;Port=5433;User Id=postgres;Password=*********;Database=AREditor;"
```

### 6.2 服務端口配置

如果 8098 端口被佔用，需同時修改兩個檔案：

#### 檔案1：`backend\appsettings.json`
```json
"Kestrel": {
  "EndPoints": {
    "Http": {
      "Url": "http://localhost:新端口"
    }
  }
}
```

#### 檔案2：`backend\wwwroot\appsetting.js`
```javascript
var apiUrl = "http://127.0.0.1:新端口";
```

### 6.3 檔案儲存配置

PDF 檔案預設儲存在 `backend\uploads\` 目錄，如需修改：

```json
"FileStorageSettings": {
  "UploadsFolder": "您的自訂路徑"
}
```

---

## 7. 測試驗證

### 7.1 基本功能測試

#### 登入測試
1. 開啟 `http://localhost:8098`
2. 使用帳號：`admin`，密碼：`123456`
3. 確認能正常登入系統

#### 資料庫連線測試
1. 嘗試查看機台列表
2. 嘗試新增測試資料
3. 確認資料能正常儲存和讀取

#### API 連通性測試
1. 開啟瀏覽器開發者工具 (F12)
2. 查看 Network 標籤
3. 確認 API 請求回應正常（狀態碼200）

### 7.2 功能完整性檢查

- [ ] 使用者登入/登出
- [ ] 機台管理功能
- [ ] 知識庫管理
- [ ] SOP 管理
- [ ] PDF 生成功能
- [ ] 檔案上傳功能

---

## 8. 故障排除

### 8.1 常見問題

#### 問題1：無法啟動 ARManagement.exe
**可能原因：**
- 端口被佔用
- 防火牆阻擋
- 配置檔案錯誤

**解決方法：**
```batch
# 檢查端口佔用
netstat -ano | findstr :8098

# 終止佔用進程
taskkill /f /pid 進程ID
```

#### 問題2：資料庫連線失敗
**錯誤訊息：** "Connection to the database failed"

**檢查項目：**
1. PostgreSQL 服務是否啟動
2. 資料庫連線字串是否正確
3. 防火牆是否阻擋 5432 端口

**解決步驟：**
```batch
# 檢查PostgreSQL服務
sc query postgresql-x64-13

# 啟動PostgreSQL服務
net start postgresql-x64-13
```

#### 問題3：前端頁面無法載入（HTTP 404 錯誤）
**錯誤現象：**
- 瀏覽器顯示："找不到此 localhost 頁面"
- F12 控制台顯示：HTTP ERROR 404
- 後端正常運行但前端無法載入

**根本原因：**
- backend/wwwroot 目錄缺少前端靜態檔案
- 前端檔案只在 frontend 目錄，未複製到 wwwroot

**解決方法：**
```batch
# 1. 複製前端檔案到 wwwroot
xcopy /E /I /Y "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\frontend\*" "C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\backend\wwwroot\"

# 2. 重新啟動服務
C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\stop.bat
C:\Users\<USER>\Desktop\KM_deploy_noDocker (Windows)\start.bat
```

**驗證步驟：**
1. 檢查 `backend\wwwroot\index.html` 是否存在
2. 檢查 `backend\wwwroot\static\` 目錄是否有內容
3. 重新訪問 http://localhost:8098

#### 問題4：PDF 生成失敗
**可能原因：**
- uploads 目錄權限不足
- wkhtmltox.dll 檔案損壞

**解決方法：**
1. 檢查 uploads 目錄是否存在
2. 確認目錄寫入權限
3. 重新複製 wkhtmltox.dll

### 8.2 日誌檢查

系統運行日誌位置：
- 控制台輸出：直接顯示在 cmd 視窗
- 系統日誌：檢查 Windows 事件檢視器

---

## 9. 注意事項

### 9.1 安全性注意事項

1. **預設密碼**：部署後請立即修改預設管理員密碼
2. **資料庫安全**：建議修改 PostgreSQL 預設密碼
3. **網路安全**：僅在可信網路環境下使用
4. **檔案權限**：確保 uploads 目錄權限設定適當

### 9.2 效能優化建議

1. **記憶體配置**：建議目標主機至少 4GB RAM
2. **磁碟空間**：預留至少 5GB 用於系統和檔案儲存
3. **網路配置**：確保防火牆允許相關端口通訊

### 9.3 備份建議

1. **資料庫備份**：定期備份 AREditor 資料庫
2. **檔案備份**：定期備份 uploads 目錄
3. **配置備份**：保留 appsettings.json 副本

### 9.4 版本更新

當需要更新系統時：
1. 停止現有服務（執行 stop.bat）
2. 備份當前 uploads 目錄和資料庫
3. 替換 backend 目錄檔案
4. 保留現有 appsettings.json 配置
5. 重新啟動服務

---

## 📞 支援資訊

**系統管理員帳號：** admin / 123456  
**技術支援：** 請聯繫系統開發團隊  
**更新時間：** 2025年01月15日  

---

## 📄 附錄

### A. 檔案清單檢查表

打包完成後，請確認以下關鍵檔案存在：

**後端核心檔案：**
- [ ] ARManagement.exe (142,848 bytes)
- [ ] appsettings.json (1,638 bytes)
- [ ] ARManagement.dll
- [ ] libwkhtmltox.dll
- [ ] wkhtmltox.dll

**前端檔案：**
- [ ] backend/wwwroot/index.html (主頁面)
- [ ] backend/wwwroot/appsetting.js (API配置)
- [ ] backend/wwwroot/static/ (靜態資源目錄)
- [ ] frontend/index.html (前端備份)
- [ ] frontend/static/ (前端備份)

**資料庫檔案：**
- [ ] database/init.sql (34,458 bytes)

**腳本檔案：**
- [ ] start.bat (啟動腳本)
- [ ] stop.bat (停止腳本)

**配置檔案：**
- [ ] config/配置說明.md (3,803 bytes)
- [ ] README.md

### B. 端口使用說明

| 服務 | 預設端口 | 說明 |
|------|----------|------|
| 後端 API | 8098 | 可在 appsettings.json 修改 |
| PostgreSQL | 5432 | 資料庫服務端口 |
| 前端 | 8098 | 與後端共用端口（SPA模式） |

### C. 目錄權限要求

| 目錄 | 權限要求 | 說明 |
|------|----------|------|
| backend/ | 讀取+執行 | 系統程式目錄 |
| uploads/ | 讀取+寫入 | PDF 檔案儲存 |
| wwwroot/ | 讀取 | 前端靜態檔案 |

### D. 實際測試結果

**打包測試日期：** 2025年01月15日  
**測試環境：** Windows 10 build 22631.5472  
**打包狀態：** ✅ 成功  
**部署包大小：** 約 150-200MB  
**關鍵問題：** 套件降級錯誤已解決  

---

**© 2025 AR 管理系統開發團隊**
.content {
  width: 100%;
  height: 100%;
  background-color: #f4f6f9;
  flex: 1;
}
.content2 {
  padding: 0rem 1.5rem;
  margin-top: -3.9rem;
  background-color: #f7f7f7;
  /* width: 84em; */
}
.buttonFunction {
  display: flex;
  flex-direction: column; /* 將方向改為橫向 */
  align-items: flex-end; /* 使元素在垂直方向上居中對齊 */
  justify-content: right; /* 使元素對齊到右側 */
  gap: 0px; /* 設定元素之間的間距 */
  margin-top: 1em;
}
.knowledgeBtn {
  background-color: #3e98f1;
  color: white;
  font-size: 16px;
}

.knowledgeBtn:hover {
  background-color: #0266d1;
}

.button {
  padding: 8px 15px 8px 12px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px;
  transition: background-color 0.3s; /* Transition effect */
  text-decoration: none;
}

.conditionBtn {
  background-color: #1fa7af;
  color: white;
  font-size: 16px;
}

.conditionBtn:hover {
  background-color: #167479;
}

.contentWrapper {
  border: 2px solid #b7bfcf;
  border-radius: 20px;
  padding: 10px;
  margin: 1rem 0rem;
}
.listSearch {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}
.listSearch input {
  border-radius: 10px;
  border: 2px solid #b7bfcf; /* 設定邊框顏色 */
  padding-left: 6px;
}
.listSearch ::placeholder {
  font-size: 14px;
  padding-left: 2px;
}
.row {
  background-color: #dddddd;
  border: 1px solid transparent;
  cursor: pointer;
}
.buttonPage {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.buttonPage1,
.buttonPage2 {
  background-color: #3e98f1;
  color: white;
  margin: 0px 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-bottom: 10px;
  margin-top: 20px;
  padding: 10px 15px;
}

.buttonPage1:hover,
.buttonPage2:hover {
  background-color: #0266d1;
  transition: background-color 0.3s; /* Transition effect */
}

main {
  h2 {
    text-align: center;
    color: #1672ad;
    font-size: 35px;
    padding-top: 20px;
    font-size: 1.8rem;
  }
}

main p {
  font-size: 24px;
  color: #797878;
  text-align: right;
}

/* 針對整個表格的圓角 */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #dadada; /* 若需要邊框 */
}

/* 針對表格的上方左右兩個角落圓角 */
table th:first-child {
  border-top-left-radius: 10px;
}

table th:last-child {
  border-top-right-radius: 10px;
}

/* 針對表格的下方左右兩個角落圓角 */
table tr:last-child td:first-child {
  border-bottom-left-radius: 10px;
}

table tr:last-child td:last-child {
  border-bottom-right-radius: 10px;
}

.row {
  background-color: #dddddd;
  border: 1px solid transparent;
  cursor: pointer;
}

/* 定義鼠標懸停的樣式 */
.row:hover {
  background-color: #c3c3c3;
  border: 1px solid #000000;
}

.row:hover td {
  border: 1px solid #9c9c9c;
}

.row.selected td {
  border: 1px solid #9c9c9c;
}

/* 定義已被點擊的行的樣式 */
.row.selected {
  background-color: #c3c3c3;
  border: 1px solid #9c9c9c;
}

.row.clicked {
  background-color: #c3c3c3;
}

table th {
  background-color: #ffffff;
  color: #000000;
  padding: 10px;
  text-align: center;
}

table td {
  text-align: center;
  color: #000000;
  padding: 10px;
  border: 1px solid transparent;
}

table td,
table th {
  border: 1px solid #c3c3c3;
}

//{
//  "Logging": {
//    "LogLevel": {
//      "Default": "Information",
//      "Microsoft.AspNetCore": "Warning"
//    }
//  },
//  "AllowedHosts": "*",
//  "ConnectionStrings": {
//    "DefaultConnection": "Server=areditor.c7oa6eyws6yf.us-east-1.rds.amazonaws.com;Port=5432;User Id=postgres;Password=*********;Database=AREditor;"
//  },
//  "DBConfig": {
//    "ConnectionString": "Server=areditor.c7oa6eyws6yf.us-east-1.rds.amazonaws.com;Port=5432;User Id=postgres;Password=*********;Database=AREditor;"
//  },
//  "Kestrel": {
//    "EndPoints": {
//      "Http": {
//        "Url": "http://localhost:5052"
//      }
//    }
//  },
//  "JwtSettings": {
//    "Issuer": "UNTaiwanensis",
//    "SignKey": "U@N#Taiwanensis$@&^",
//    "LifeHour": 87600
//  }
//}

{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DBConfig": {
    "ConnectionString": "Server=127.0.0.1;Port=5432;User Id=postgres;Password=*********;Database=AREditor;"
    //"ConnectionString": "Server=*************;Port=10003;User Id=postgres;Password=*********;Database=AREditor;"
  },
  "JwtSettings": {
    "Issuer": "UNTaiwanensis",
    "SignKey": "U@N#Taiwanensis$@&^",
    "LifeHour": 8760
  },
  // 增加SMTP安全性，設置環境變數
  "SMTP": {
    "Server": "smtp.gmail.com",
    "Port": 587,
    "Username": "<EMAIL>",
    "Password": "{SMTP_PASSWORD}", // 設置環境變數
    "EnableSSL": true,
    "SenderEmail": "<EMAIL>"
  },
  "FileStorageSettings": {
    "UploadsFolder": "C:\\Users\\<USER>\\Desktop\\PDFSaveTest"
  }
}

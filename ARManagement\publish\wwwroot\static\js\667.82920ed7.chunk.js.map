{"version": 3, "file": "static/js/667.82920ed7.chunk.js", "mappings": "2GAAA,eAAAA,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAG,qBAAAC,aAAA,OAAAA,aAAiBA,YAAYC,IAC9BC,EAAOC,QAAU,W,OAAGH,YAAYC,KAAf,EACX,qBAAAG,SAAA,OAAAA,SAAaA,QAAQT,QAC3BO,EAAOC,QAAU,W,OAAIT,IAAmBI,GAAgB,GAAvC,EACjBH,EAASS,QAAQT,OAIjBE,GAHAH,EAAiB,WACf,IAAAW,E,OACQ,KADRA,EAAKV,KACF,GAAWU,EAAG,EAFF,KAIjBN,EAA4B,IAAnBK,QAAQE,SACjBR,EAAeD,EAAiBE,GAC1BQ,KAAKN,KACXC,EAAOC,QAAU,W,OAAGI,KAAKN,MAAQL,CAAhB,EACjBA,EAAWW,KAAKN,QAEhBC,EAAOC,QAAU,W,OAAO,IAAAI,MAAOC,UAAYZ,CAA1B,EACjBA,GAAe,IAAAW,MAAOC,U,8BCTxB,IAPA,IAAIP,EAAMQ,EAAQ,MACdC,EAAyB,qBAAXC,OAAyBC,EAAAA,EAASD,OAChDE,EAAU,CAAC,MAAO,UAClBC,EAAS,iBACTC,EAAML,EAAK,UAAYI,GACvBE,EAAMN,EAAK,SAAWI,IAAWJ,EAAK,gBAAkBI,GAEpDG,EAAI,GAAIF,GAAOE,EAAIJ,EAAQK,OAAQD,IACzCF,EAAML,EAAKG,EAAQI,GAAK,UAAYH,GACpCE,EAAMN,EAAKG,EAAQI,GAAK,SAAWH,IAC5BJ,EAAKG,EAAQI,GAAK,gBAAkBH,GAI7C,IAAIC,IAAQC,EAAK,CACf,IAAIG,EAAO,EACPC,EAAK,EACLC,EAAQ,GACRC,EAAgB,IAAO,GAE3BP,EAAM,SAASQ,GACb,GAAoB,IAAjBF,EAAMH,OAAc,CACrB,IAAIM,EAAOvB,IACPwB,EAAOC,KAAKC,IAAI,EAAGL,GAAiBE,EAAOL,IAC/CA,EAAOM,EAAOD,EACdI,YAAW,WACT,IAAIC,EAAKR,EAAMS,MAAM,GAIrBT,EAAMH,OAAS,EACf,IAAI,IAAID,EAAI,EAAGA,EAAIY,EAAGX,OAAQD,IAC5B,IAAIY,EAAGZ,GAAGc,UACR,IACEF,EAAGZ,GAAGM,SAASJ,EACjB,CAAE,MAAMa,GACNJ,YAAW,WAAa,MAAMI,CAAE,GAAG,EACrC,CAGN,GAAGN,KAAKO,MAAMR,GAChB,CAMA,OALAJ,EAAMa,KAAK,CACTC,SAAUf,EACVG,SAAUA,EACVQ,WAAW,IAENX,CACT,EAEAJ,EAAM,SAASmB,GACb,IAAI,IAAIlB,EAAI,EAAGA,EAAII,EAAMH,OAAQD,IAC5BI,EAAMJ,GAAGkB,SAAWA,IACrBd,EAAMJ,GAAGc,WAAY,EAG3B,CACF,CAEA7B,EAAOC,QAAU,SAASiC,GAIxB,OAAOrB,EAAIsB,KAAK3B,EAAM0B,EACxB,EACAlC,EAAOC,QAAQmC,OAAS,WACtBtB,EAAIuB,MAAM7B,EAAM8B,UAClB,EACAtC,EAAOC,QAAQsC,SAAW,SAASC,GAC5BA,IACHA,EAAShC,GAEXgC,EAAOC,sBAAwB5B,EAC/B2B,EAAOE,qBAAuB5B,CAChC,C,WCrEAd,EAAOC,QAAU,SAAS0C,GACtBC,KAAKC,IAAK,EACVD,KAAKE,MAAQ,EAGiB,KAA1BH,EAAaI,OAAO,KACpBJ,EAAeA,EAAaK,OAAO,EAAE,IAIzCL,GADAA,EAAeA,EAAaM,QAAQ,KAAK,KACbC,cAI5B,IAAIC,EAAgB,CAChBC,UAAW,SACXC,aAAc,SACdC,KAAM,SACNC,WAAY,SACZC,MAAO,SACPC,MAAO,SACPC,OAAQ,SACRC,MAAO,SACPC,eAAgB,SAChBC,KAAM,SACNC,WAAY,SACZC,MAAO,SACPC,UAAW,SACXC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,MAAO,SACPC,eAAgB,SAChBC,SAAU,SACVC,QAAS,SACTC,KAAM,SACNC,SAAU,SACVC,SAAU,SACVC,cAAe,SACfC,SAAU,SACVC,UAAW,SACXC,UAAW,SACXC,YAAa,SACbC,eAAgB,SAChBC,WAAY,SACZC,WAAY,SACZC,QAAS,SACTC,WAAY,SACZC,aAAc,SACdC,cAAe,SACfC,cAAe,SACfC,cAAe,SACfC,WAAY,SACZC,SAAU,SACVC,YAAa,SACbC,QAAS,SACTC,WAAY,SACZC,SAAU,SACVC,UAAW,SACXC,YAAa,SACbC,YAAa,SACbC,QAAS,SACTC,UAAW,SACXC,WAAY,SACZC,KAAM,SACNC,UAAW,SACXC,KAAM,SACNC,MAAO,SACPC,YAAa,SACbC,SAAU,SACVC,QAAS,SACTC,UAAY,SACZC,OAAS,SACTC,MAAO,SACPC,MAAO,SACPC,SAAU,SACVC,cAAe,SACfC,UAAW,SACXC,aAAc,SACdC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,qBAAsB,SACtBC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,YAAa,SACbC,cAAe,SACfC,aAAc,SACdC,eAAgB,SAChBC,eAAgB,SAChBC,eAAgB,SAChBC,YAAa,SACbC,KAAM,SACNC,UAAW,SACXC,MAAO,SACPC,QAAS,SACTC,OAAQ,SACRC,iBAAkB,SAClBC,WAAY,SACZC,aAAc,SACdC,aAAc,SACdC,eAAgB,SAChBC,gBAAiB,SACjBC,kBAAmB,SACnBC,gBAAiB,SACjBC,gBAAiB,SACjBC,aAAc,SACdC,UAAW,SACXC,UAAW,SACXC,SAAU,SACVC,YAAa,SACbC,KAAM,SACNC,QAAS,SACTC,MAAO,SACPC,UAAW,SACXC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,cAAe,SACfC,UAAW,SACXC,cAAe,SACfC,cAAe,SACfC,WAAY,SACZC,UAAW,SACXC,KAAM,SACNC,KAAM,SACNC,KAAM,SACNC,WAAY,SACZC,OAAQ,SACRC,cAAe,SACfC,IAAK,SACLC,UAAW,SACXC,UAAW,SACXC,YAAa,SACbC,OAAQ,SACRC,WAAY,SACZC,SAAU,SACVC,SAAU,SACVC,OAAQ,SACRC,OAAQ,SACRC,QAAS,SACTC,UAAW,SACXC,UAAW,SACXC,KAAM,SACNC,YAAa,SACbC,UAAW,SACXC,IAAK,SACLC,KAAM,SACNC,QAAS,SACTC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,UAAW,SACXC,MAAO,SACPC,MAAO,SACPC,WAAY,SACZC,OAAQ,SACRC,YAAa,UAEjBxJ,EAAeQ,EAAcR,IAAiBA,EAqD9C,IAjDA,IAAIyJ,EAAa,CACb,CACIC,GAAI,kEACJC,QAAS,CAAC,0BAA2B,yBACrCpM,QAAS,SAAUqM,GACf,MAAO,CACHC,SAASD,EAAK,IACdC,SAASD,EAAK,IACdC,SAASD,EAAK,IACdE,WAAWF,EAAK,IAExB,GAEJ,CACIF,GAAI,+CACJC,QAAS,CAAC,oBAAqB,oBAC/BpM,QAAS,SAAUqM,GACf,MAAO,CACHC,SAASD,EAAK,IACdC,SAASD,EAAK,IACdC,SAASD,EAAK,IAEtB,GAEJ,CACIF,GAAI,qDACJC,QAAS,CAAC,UAAW,UACrBpM,QAAS,SAAUqM,GACf,MAAO,CACHC,SAASD,EAAK,GAAI,IAClBC,SAASD,EAAK,GAAI,IAClBC,SAASD,EAAK,GAAI,IAE1B,GAEJ,CACIF,GAAI,qDACJC,QAAS,CAAC,OAAQ,OAClBpM,QAAS,SAAUqM,GACf,MAAO,CACHC,SAASD,EAAK,GAAKA,EAAK,GAAI,IAC5BC,SAASD,EAAK,GAAKA,EAAK,GAAI,IAC5BC,SAASD,EAAK,GAAKA,EAAK,GAAI,IAEpC,IAKCxL,EAAI,EAAGA,EAAIqL,EAAWpL,OAAQD,IAAK,CACxC,IAAIsL,EAAKD,EAAWrL,GAAGsL,GACnBK,EAAYN,EAAWrL,GAAGb,QAC1BqM,EAAOF,EAAGM,KAAKhK,GACnB,GAAI4J,EAAM,CACN,IAAIK,EAAWF,EAAUH,GACzB3J,KAAKiK,EAAID,EAAS,GAClBhK,KAAKkK,EAAIF,EAAS,GAClBhK,KAAKmK,EAAIH,EAAS,GACdA,EAAS5L,OAAS,IAClB4B,KAAKE,MAAQ8J,EAAS,IAE1BhK,KAAKC,IAAK,CACd,CAEJ,CAGAD,KAAKiK,EAAKjK,KAAKiK,EAAI,GAAKG,MAAMpK,KAAKiK,GAAM,EAAMjK,KAAKiK,EAAI,IAAO,IAAMjK,KAAKiK,EAC1EjK,KAAKkK,EAAKlK,KAAKkK,EAAI,GAAKE,MAAMpK,KAAKkK,GAAM,EAAMlK,KAAKkK,EAAI,IAAO,IAAMlK,KAAKkK,EAC1ElK,KAAKmK,EAAKnK,KAAKmK,EAAI,GAAKC,MAAMpK,KAAKmK,GAAM,EAAMnK,KAAKmK,EAAI,IAAO,IAAMnK,KAAKmK,EAC1EnK,KAAKE,MAASF,KAAKE,MAAQ,EAAK,EAAMF,KAAKE,MAAQ,GAAOkK,MAAMpK,KAAKE,OAAU,EAAMF,KAAKE,MAG1FF,KAAKqK,MAAQ,WACT,MAAO,OAASrK,KAAKiK,EAAI,KAAOjK,KAAKkK,EAAI,KAAOlK,KAAKmK,EAAI,GAC7D,EACAnK,KAAKsK,OAAS,WACV,MAAO,QAAUtK,KAAKiK,EAAI,KAAOjK,KAAKkK,EAAI,KAAOlK,KAAKmK,EAAI,KAAOnK,KAAKE,MAAQ,GAClF,EACAF,KAAKuK,MAAQ,WACT,IAAIN,EAAIjK,KAAKiK,EAAEO,SAAS,IACpBN,EAAIlK,KAAKkK,EAAEM,SAAS,IACpBL,EAAInK,KAAKmK,EAAEK,SAAS,IAIxB,OAHgB,GAAZP,EAAE7L,SAAa6L,EAAI,IAAMA,GACb,GAAZC,EAAE9L,SAAa8L,EAAI,IAAMA,GACb,GAAZC,EAAE/L,SAAa+L,EAAI,IAAMA,GACtB,IAAMF,EAAIC,EAAIC,CACzB,EAGAnK,KAAKyK,WAAa,WAId,IAFA,IAAIC,EAAW,IAAIC,MAEVxM,EAAI,EAAGA,EAAIqL,EAAWpL,OAAQD,IAEnC,IADA,IAAIuL,EAAUF,EAAWrL,GAAGuL,QACnBkB,EAAI,EAAGA,EAAIlB,EAAQtL,OAAQwM,IAChCF,EAASA,EAAStM,QAAUsL,EAAQkB,GAI5C,IAAK,IAAIC,KAAMtK,EACXmK,EAASA,EAAStM,QAAUyM,EAGhC,IAAIC,EAAMC,SAASC,cAAc,MACjCF,EAAIG,aAAa,KAAM,qBACvB,IAAS9M,EAAI,EAAGA,EAAIuM,EAAStM,OAAQD,IACjC,IACI,IAAI+M,EAAYH,SAASC,cAAc,MACnCG,EAAa,IAAIC,SAASV,EAASvM,IACnCkN,EAAcN,SAASC,cAAc,OACzCK,EAAYC,MAAMC,QACV,oDAEkBJ,EAAWZ,QAF7B,WAGaY,EAAWZ,QAEhCc,EAAYG,YAAYT,SAASU,eAAe,SAChD,IAAIC,EAAkBX,SAASU,eAC3B,IAAMf,EAASvM,GAAK,OAASgN,EAAWd,QAAU,OAASc,EAAWZ,SAE1EW,EAAUM,YAAYH,GACtBH,EAAUM,YAAYE,GACtBZ,EAAIU,YAAYN,EAEpB,CAAE,MAAMhM,GAAG,CAEf,OAAO4L,CAEX,CAEJ,C,+rDC7RIa,EAAgB,SAAS1B,EAAG/K,GAI5B,OAHAyM,EAAgBC,OAAOC,gBAClB,CAAEC,UAAW,cAAgBnB,OAAS,SAAUgB,EAAG1B,GAAK0B,EAAEG,UAAY7B,CAAA,GACvE,SAAU0B,EAAG1B,GAAK,IAAK,IAAI/K,KAAK+K,EAAO2B,OAAOG,UAAUC,eAAezM,KAAK0K,EAAG/K,KAAIyM,EAAEzM,GAAK+K,EAAE/K,GAAA,GAC3E+K,EAAG/K,EAAA,EAGrB,SAAS+K,EAAUA,EAAG/K,GACzB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAI+M,UAAU,uBAAyBC,OAAOhN,GAAK,iCAE7D,SAASf,IAAO6B,KAAKmM,YAAclC,CAAA,CADnC0B,EAAc1B,EAAG/K,GAEjB+K,EAAE8B,UAAkB,OAAN7M,EAAa0M,OAAOQ,OAAOlN,IAAMf,EAAG4N,UAAY7M,EAAE6M,UAAW,IAAI5N,EAAA,CCgC1E,SCzDOA,EAAOwN,EAA0B1B,GAAA,IAAzB/K,EAAAyM,EAAA,GAAGxN,EAAAwN,EAAA,GACzB,MAAO,CACLzM,EAAIN,KAAKyN,IAAIpC,GAAO9L,EAAIS,KAAK0N,IAAIrC,GACjC/K,EAAIN,KAAK0N,IAAIrC,GAAO9L,EAAIS,KAAKyN,IAAIpC,GAAA,UAKrBsC,IAAA,IAAc,IAAAZ,EAAA,GAAA1B,EAAA,EAAAA,EAAAvK,UAAAtB,OAAA6L,IAAA0B,EAAA1B,GAAAvK,UAAAuK,GAE1B,IAAK,IAAI/K,EAAI,EAAGA,EAAIyM,EAAQvN,OAAQc,IAClC,GAAI,iBAAoByM,EAAQzM,GAC9B,MAAM,IAAIsN,MACR,2BAA2BtN,EAAA,6BAA8ByM,EAAQzM,GAAA,cAAgByM,EAAQzM,IAIjG,OAAO,EAGT,IAAMuN,EAAK7N,KAAK8N,GAAA,SASAC,EAAmBhB,EAAa1B,EAAY/K,GAC1DyM,EAAEiB,SAAY,IAAMjB,EAAEiB,SAAY,EAAI,EACtCjB,EAAEkB,UAAa,IAAMlB,EAAEkB,UAAa,EAAI,EAEnC,IAAAN,EAAgBZ,EAAAmB,GAAZH,EAAYhB,EAAAoB,GAARC,EAAQrB,EAAAsB,EAALC,EAAKvB,EAAAwB,EAErBZ,EAAK3N,KAAKwO,IAAIzB,EAAEmB,IAChBH,EAAK/N,KAAKwO,IAAIzB,EAAEoB,IACV,IAAAM,EAAalP,EAAO,EAAE8L,EAAK+C,GAAK,GAAI9N,EAAKgO,GAAK,IAAKvB,EAAE2B,KAAO,IAAMb,GAAjEc,EAAAF,EAAA,GAAKF,EAAAE,EAAA,GACNG,EAAY5O,KAAK6O,IAAIF,EAAK,GAAK3O,KAAK6O,IAAIlB,EAAI,GAAK3N,KAAK6O,IAAIN,EAAK,GAAKvO,KAAK6O,IAAId,EAAI,GAEnF,EAAIa,IACNjB,GAAM3N,KAAK8O,KAAKF,GAChBb,GAAM/N,KAAK8O,KAAKF,IAElB7B,EAAEmB,GAAKP,EACPZ,EAAEoB,GAAKJ,EACP,IAAMgB,EAAe/O,KAAK6O,IAAIlB,EAAI,GAAK3N,KAAK6O,IAAIN,EAAK,GAAKvO,KAAK6O,IAAId,EAAI,GAAK/N,KAAK6O,IAAIF,EAAK,GACpFK,GAAWjC,EAAEiB,WAAajB,EAAEkB,UAAY,GAAK,GACjDjO,KAAK8O,KAAK9O,KAAKC,IAAI,GAAID,KAAK6O,IAAIlB,EAAI,GAAK3N,KAAK6O,IAAId,EAAI,GAAKgB,GAAeA,IACtEE,EAAMtB,EAAKY,EAAMR,EAAKiB,EACtBE,GAAOnB,EAAKY,EAAMhB,EAAKqB,EACvBG,EAAO5P,EAAO,CAAC0P,EAAKC,GAAMnC,EAAE2B,KAAO,IAAMb,GAE/Cd,EAAEqC,GAAKD,EAAK,IAAM9D,EAAK+C,GAAK,EAC5BrB,EAAEsC,GAAKF,EAAK,IAAM7O,EAAKgO,GAAK,EAC5BvB,EAAEuC,KAAOtP,KAAKuP,OAAOhB,EAAMW,GAAOnB,GAAKY,EAAMM,GAAOtB,GACpDZ,EAAEyC,KAAOxP,KAAKuP,QAAQhB,EAAMW,GAAOnB,IAAMY,EAAMM,GAAOtB,GAClD,IAAMZ,EAAEkB,WAAalB,EAAEyC,KAAOzC,EAAEuC,OAClCvC,EAAEyC,MAAQ,EAAI3B,GAEZ,IAAMd,EAAEkB,WAAalB,EAAEyC,KAAOzC,EAAEuC,OAClCvC,EAAEyC,MAAQ,EAAI3B,GAEhBd,EAAEuC,MAAQ,IAAMzB,EAChBd,EAAEyC,MAAQ,IAAM3B,CAAA,UAaFO,EAA2BrB,EAAW1B,EAAW/K,GAC/DqN,EAAcZ,EAAG1B,EAAG/K,GAEpB,IAAMf,EAAUwN,EAAIA,EAAI1B,EAAIA,EAAI/K,EAAIA,EAEpC,GAAI,EAAIf,EACN,MAAO,GACF,GAAI,IAAMA,EACf,MAAO,CACL,CACGwN,EAAIzM,GAAMyM,EAAIA,EAAI1B,EAAIA,GACtBA,EAAI/K,GAAMyM,EAAIA,EAAI1B,EAAIA,KAE7B,IAAMwC,EAAO7N,KAAK8O,KAAKvP,GAEvB,MAAO,CACL,EACGwN,EAAIzM,EAAI+K,EAAIwC,IAASd,EAAIA,EAAI1B,EAAIA,IACjCA,EAAI/K,EAAIyM,EAAIc,IAASd,EAAIA,EAAI1B,EAAIA,IACpC,EACG0B,EAAIzM,EAAI+K,EAAIwC,IAASd,EAAIA,EAAI1B,EAAIA,IACjCA,EAAI/K,EAAIyM,EAAIc,IAASd,EAAIA,EAAI1B,EAAIA,IAAA,CAIjC,ICjGUiD,EDiGJG,EAAMzO,KAAK8N,GAAK,aAEba,EAAK5B,EAAW1B,EAAW/K,GACzC,OAAQ,EAAIA,GAAKyM,EAAIzM,EAAI+K,CAAA,UAGXkD,EAAMxB,EAAW1B,EAAY/K,EAAYf,GACvD,OAAOwN,EAAI/M,KAAKyN,IAAIlO,EAAS,IAAMsO,GAAMxC,EAAKrL,KAAK0N,IAAInO,EAAS,IAAMsO,GAAMvN,CAAA,UAG9DsO,EAAW7B,EAAY1B,EAAY/K,EAAYf,GAC7D,IAAMoO,EAAM,KACNE,EAAMxC,EAAK0B,EACXgB,EAAMzN,EAAK+K,EAEX+C,EAAI,EAAIP,EAAM,GADRtO,EAAKe,GACa,EAAIyN,EAC5BO,EAAkB,GAAbP,EAAMF,GACXY,EAAI,EAAIZ,EAGd,OAAI7N,KAAKwO,IAAIJ,GAAKT,EAET,EAAEc,EAAIH,GAiBjB,SAAmBvB,EAAW1B,EAAW/K,QAAA,IAAAA,IAAAA,EAAA,MAEvC,IAAMf,EAAiBwN,EAAIA,EAAI,EAAI1B,EAEnC,GAAI9L,GAAkBe,EACpB,MAAO,GACF,GAAIf,GAAkBe,EAC3B,MAAO,EAAEyM,EAAI,GAEf,IAAMY,EAAO3N,KAAK8O,KAAKvP,GAEvB,MAAO,EAAGwN,EAAI,EAAKY,GAAQZ,EAAI,EAAKY,EAAA,CAXtC,CAfmBW,EAAIF,EAAGK,EAAIL,EAAGT,EAAA,UAIjBoB,EAAShC,EAAY1B,EAAY/K,EAAYf,EAAYoO,GAEvE,IAAME,EAAI,EAAIF,EAMd,OAAOZ,GALIc,EAAIA,EAAIA,GAKFxC,GAJN,EAAIwC,EAAIA,EAAIF,GAIIrN,GAHhB,EAAIuN,EAAIF,EAAIA,GAGcpO,GAF1BoO,EAAIA,EAAIA,EAAA,ECnIrB,SAAiBZ,GAuCf,SAAgB1B,IACd,OAAOiD,GAAK,SAACvB,EAAS1B,EAAO/K,GAyB3B,OAxBIyM,EAAQ0C,gBAAA,IAEiB1C,EAAQ2C,KACjC3C,EAAQ2C,IAAMrE,QAAA,IAEW0B,EAAQ4C,KACjC5C,EAAQ4C,IAAMrP,QAAA,IAGWyM,EAAQ6C,KACjC7C,EAAQ6C,IAAMvE,QAAA,IAEW0B,EAAQ8C,KACjC9C,EAAQ8C,IAAMvP,QAAA,IAGWyM,EAAQsB,IACjCtB,EAAQsB,GAAKhD,QAAA,IAEY0B,EAAQwB,IACjCxB,EAAQwB,GAAKjO,GAEfyM,EAAQ0C,UAAA,GAEH1C,CAAA,IAkEX,SAAgBzM,IACd,IAAIyM,EAAe+C,IACfzE,EAAeyE,IACfxP,EAAawP,IACbvQ,EAAauQ,IAEjB,OAAOxB,GAAK,SAACX,EAASE,EAAOE,GA8B3B,OA7BIJ,EAAQoC,KAAOC,EAAYC,kBAC7BtC,EAAQoC,KAAOC,EAAYE,SAC3BnD,EAAevB,MAAMuB,GAAgBc,EAAQd,EAC7C1B,EAAeG,MAAMH,GAAgB0C,EAAQ1C,EAC7CsC,EAAQ+B,GAAK/B,EAAQ8B,SAAW5B,EAAQd,EAAe,EAAIc,EAAQd,EACnEY,EAAQgC,GAAKhC,EAAQ8B,SAAW1B,EAAQ1C,EAAe,EAAI0C,EAAQ1C,GAEjEsC,EAAQoC,KAAOC,EAAYE,UAC7BnD,EAAeY,EAAQ8B,SAAW5B,EAAQF,EAAQiC,GAAKjC,EAAQiC,GAC/DvE,EAAesC,EAAQ8B,SAAW1B,EAAQJ,EAAQkC,GAAKlC,EAAQkC,KAE/D9C,EAAe+C,IACfzE,EAAeyE,KAEbnC,EAAQoC,KAAOC,EAAYG,iBAC7BxC,EAAQoC,KAAOC,EAAYI,QAC3B9P,EAAakL,MAAMlL,GAAcuN,EAAQvN,EACzCf,EAAaiM,MAAMjM,GAAcwO,EAAQxO,EACzCoO,EAAQ+B,GAAK/B,EAAQ8B,SAAW5B,EAAQvN,EAAa,EAAIuN,EAAQvN,EACjEqN,EAAQgC,GAAKhC,EAAQ8B,SAAW1B,EAAQxO,EAAa,EAAIwO,EAAQxO,GAE/DoO,EAAQoC,KAAOC,EAAYI,SAC7B9P,EAAaqN,EAAQ8B,SAAW5B,EAAQF,EAAQ+B,GAAK/B,EAAQ+B,GAC7DnQ,EAAaoO,EAAQ8B,SAAW1B,EAAQJ,EAAQgC,GAAKhC,EAAQgC,KAE7DrP,EAAawP,IACbvQ,EAAauQ,KAGRnC,CAAA,IAYX,SAAgBE,IACd,IAAId,EAAa+C,IACbzE,EAAayE,IAEjB,OAAOxB,GAAK,SAAChO,EAASf,EAAOoO,GAQ3B,GAPIrN,EAAQyP,KAAOC,EAAYG,iBAC7B7P,EAAQyP,KAAOC,EAAYI,QAC3BrD,EAAavB,MAAMuB,GAAcxN,EAAQwN,EACzC1B,EAAaG,MAAMH,GAAcsC,EAAQtC,EACzC/K,EAAQoP,GAAKpP,EAAQmP,SAAWlQ,EAAQwN,EAAa,EAAIxN,EAAQwN,EACjEzM,EAAQqP,GAAKrP,EAAQmP,SAAW9B,EAAQtC,EAAa,EAAIsC,EAAQtC,GAE/D/K,EAAQyP,KAAOC,EAAYI,QAAS,CACtCrD,EAAazM,EAAQmP,SAAWlQ,EAAQe,EAAQoP,GAAKpP,EAAQoP,GAC7DrE,EAAa/K,EAAQmP,SAAW9B,EAAQrN,EAAQqP,GAAKrP,EAAQqP,GAC7D,IAAM9B,EAAKvN,EAAQoP,GACb3B,EAAKzN,EAAQqP,GAEnBrP,EAAQyP,KAAOC,EAAYE,SAC3B5P,EAAQoP,KAAOpP,EAAQmP,SAAW,EAAIlQ,GAAc,EAALsO,GAAU,EACzDvN,EAAQqP,KAAOrP,EAAQmP,SAAW,EAAI9B,GAAc,EAALI,GAAU,EACzDzN,EAAQsP,IAAMtP,EAAQ+N,EAAS,EAALR,GAAU,EACpCvN,EAAQuP,IAAMvP,EAAQiO,EAAS,EAALR,GAAU,OAEpChB,EAAa+C,IACbzE,EAAayE,IAGf,OAAOxP,CAAA,IAGX,SAAgBgO,EACdvB,GAEA,IAAI1B,EAAW,EACX/K,EAAW,EACXf,EAAgBuQ,IAChBnC,EAAgBmC,IAEpB,OAAO,SAAmBjC,GACxB,GAAIrC,MAAMjM,MAAoBsO,EAAQkC,KAAOC,EAAYK,SACvD,MAAM,IAAIzC,MAAM,+BAGlB,IAAMG,EAAShB,EAAEc,EAASxC,EAAU/K,EAAUf,EAAeoO,GAmB7D,OAjBIE,EAAQkC,KAAOC,EAAYM,aAC7BjF,EAAW9L,EACXe,EAAWqN,QAAA,IAGcE,EAAQQ,IACjChD,EAAYwC,EAAQ4B,SAAWpE,EAAWwC,EAAQQ,EAAIR,EAAQQ,QAAA,IAErCR,EAAQU,IACjCjO,EAAYuN,EAAQ4B,SAAWnP,EAAWuN,EAAQU,EAAIV,EAAQU,GAG5DV,EAAQkC,KAAOC,EAAYK,UAC7B9Q,EAAgB8L,EAChBsC,EAAgBrN,GAGXyN,CAAA,EAoFX,SAAgBiB,EAAOjC,EAAW1B,EAAW/K,EAAWf,EAAWsO,EAAWE,GAG5E,OAFAJ,EAAcZ,EAAG1B,EAAG/K,EAAGf,EAAGsO,EAAGE,GAEtBO,GAAK,SAACX,EAASS,EAAOE,EAAOG,GAClC,IAAME,EAAShB,EAAQ+B,GACjBnB,EAASZ,EAAQiC,GAGjBhB,EAASjB,EAAQ8B,WAAajE,MAAMiD,GACpCM,OAAA,IAA2BpB,EAAQU,EAAIV,EAAQU,EAAKO,EAAS,EAAIR,EACjEY,OAAA,IAA2BrB,EAAQY,EAAIZ,EAAQY,EAAKK,EAAS,EAAIN,EA6BvE,SAASW,EAAIlC,GAAa,OAAOA,EAAIA,CAAA,CA3BjCY,EAAQoC,KAAOC,EAAYO,eAAiB,IAAMlF,IACpDsC,EAAQoC,KAAOC,EAAYQ,QAC3B7C,EAAQY,EAAIZ,EAAQ8B,SAAW,EAAInB,GAEjCX,EAAQoC,KAAOC,EAAYS,cAAgB,IAAMnQ,IACnDqN,EAAQoC,KAAOC,EAAYQ,QAC3B7C,EAAQU,EAAIV,EAAQ8B,SAAW,EAAIrB,QAAA,IAGVT,EAAQU,IACjCV,EAAQU,EAAKV,EAAQU,EAAItB,EAAMiC,EAAI1O,GAAMsO,EAAS,EAAIf,SAAA,IAE7BF,EAAQY,IACjCZ,EAAQY,EAAKQ,EAAI1D,EAAKsC,EAAQY,EAAIhP,GAAKqP,EAAS,EAAIb,SAAA,IAE3BJ,EAAQ+B,KACjC/B,EAAQ+B,GAAK/B,EAAQ+B,GAAK3C,EAAIY,EAAQgC,GAAKrP,GAAKsO,EAAS,EAAIf,SAAA,IAEpCF,EAAQgC,KACjChC,EAAQgC,GAAKhB,EAAStD,EAAIsC,EAAQgC,GAAKpQ,GAAKqP,EAAS,EAAIb,SAAA,IAEhCJ,EAAQiC,KACjCjC,EAAQiC,GAAKjC,EAAQiC,GAAK7C,EAAIY,EAAQkC,GAAKvP,GAAKsO,EAAS,EAAIf,SAAA,IAEpCF,EAAQkC,KACjClC,EAAQkC,GAAKtB,EAASlD,EAAIsC,EAAQkC,GAAKtQ,GAAKqP,EAAS,EAAIb,IAG3D,IAAMmB,EAAMnC,EAAIxN,EAAI8L,EAAI/K,EAExB,QAAI,IAAuBqN,EAAQe,OAE7B,IAAM3B,GAAK,IAAM1B,GAAK,IAAM/K,GAAK,IAAMf,GAEzC,GAAI,IAAM2P,SAIDvB,EAAQO,UACRP,EAAQQ,UACRR,EAAQe,YACRf,EAAQK,gBACRL,EAAQM,UACfN,EAAQoC,KAAOC,EAAYQ,YACtB,CAEL,IAAMrB,EAAOxB,EAAQe,KAAO1O,KAAK8N,GAAK,IAOhC4C,EAAS1Q,KAAK0N,IAAIyB,GAClBwB,EAAS3Q,KAAKyN,IAAI0B,GAClBd,EAAS,EAAIY,EAAItB,EAAQO,IACzB0C,EAAS,EAAI3B,EAAItB,EAAQQ,IACzB0C,EAAI5B,EAAI0B,GAAUtC,EAASY,EAAIyB,GAAUE,EACzCE,EAAI,EAAIJ,EAASC,GAAUtC,EAASuC,GACpCG,EAAI9B,EAAIyB,GAAUrC,EAASY,EAAI0B,GAAUC,EAOzCI,EAAKH,EAAItR,EAAIA,EAAIuR,EAAIzF,EAAI9L,EAAIwR,EAAI1F,EAAIA,EACrC4F,EAAKH,GAAK/D,EAAIxN,EAAI8L,EAAI/K,GAAK,GAAKuQ,EAAIvQ,EAAIf,EAAIwR,EAAIhE,EAAI1B,GACpDC,EAAKuF,EAAIvQ,EAAIA,EAAIwQ,EAAI/D,EAAIzM,EAAIyQ,EAAIhE,EAAIA,EAerCmE,GAAYlR,KAAKuP,MAAM0B,EAAID,EAAK1F,GAAMtL,KAAK8N,IAAM9N,KAAK8N,GAAM,EAM5DqD,EAAYnR,KAAK0N,IAAIwD,GACrBE,EAAYpR,KAAKyN,IAAIyD,GAE3BvD,EAAQO,GAAKlO,KAAKwO,IAAIU,GACpBlP,KAAK8O,KAAKkC,EAAK/B,EAAImC,GAAaH,EAAKE,EAAYC,EAAY9F,EAAK2D,EAAIkC,IACxExD,EAAQQ,GAAKnO,KAAKwO,IAAIU,GACpBlP,KAAK8O,KAAKkC,EAAK/B,EAAIkC,GAAaF,EAAKE,EAAYC,EAAY9F,EAAK2D,EAAImC,IACxEzD,EAAQe,KAAiB,IAAVwC,EAAgBlR,KAAK8N,EAAA,CAW1C,YAAO,IAHoBH,EAAQM,WAAa,EAAIiB,IAClDvB,EAAQM,YAAcN,EAAQM,WAEzBN,CAAA,IA1bKZ,EAAAsE,MAAhB,SAAsBtE,GAEpB,SAAS1B,EAAGA,GAAe,OAAOrL,KAAKO,MAAM8K,EAAM0B,GAAYA,CAAA,CAC/D,YAAO,IAAPA,IAHoBA,EAAA,MACpBY,EAAcZ,GAEP,SAAeA,GA6BpB,YAAO,IA5BoBA,EAAQ2C,KACjC3C,EAAQ2C,GAAKrE,EAAG0B,EAAQ2C,UAAA,IAEC3C,EAAQ4C,KACjC5C,EAAQ4C,GAAKtE,EAAG0B,EAAQ4C,UAAA,IAGC5C,EAAQ6C,KACjC7C,EAAQ6C,GAAKvE,EAAG0B,EAAQ6C,UAAA,IAEC7C,EAAQ8C,KACjC9C,EAAQ8C,GAAKxE,EAAG0B,EAAQ8C,UAAA,IAGC9C,EAAQsB,IACjCtB,EAAQsB,EAAIhD,EAAG0B,EAAQsB,SAAA,IAEEtB,EAAQwB,IACjCxB,EAAQwB,EAAIlD,EAAG0B,EAAQwB,SAAA,IAGExB,EAAQmB,KACjCnB,EAAQmB,GAAK7C,EAAG0B,EAAQmB,UAAA,IAECnB,EAAQoB,KACjCpB,EAAQoB,GAAK9C,EAAG0B,EAAQoB,KAGnBpB,CAAA,GAIKA,EAAAuE,OAAAjG,EA8BA0B,EAAAwE,OAAhB,WACE,OAAOjD,GAAK,SAACvB,EAAS1B,EAAO/K,GAyB3B,OAxBKyM,EAAQ0C,gBAAA,IAEgB1C,EAAQ2C,KACjC3C,EAAQ2C,IAAMrE,QAAA,IAEW0B,EAAQ4C,KACjC5C,EAAQ4C,IAAMrP,QAAA,IAGWyM,EAAQ6C,KACjC7C,EAAQ6C,IAAMvE,QAAA,IAEW0B,EAAQ8C,KACjC9C,EAAQ8C,IAAMvP,QAAA,IAGWyM,EAAQsB,IACjCtB,EAAQsB,GAAKhD,QAAA,IAEY0B,EAAQwB,IACjCxB,EAAQwB,GAAKjO,GAEfyM,EAAQ0C,UAAA,GAEH1C,CAAA,KAIKA,EAAAyE,cAAhB,SAA8BzE,EAAmB1B,EAAmB/K,GAClE,YAAO,IAAPyM,IAD4BA,GAAA,YAAA1B,IAAmBA,GAAA,YAAA/K,IAAmBA,GAAA,GAC3DgO,GAAK,SAAC/O,EAASoO,EAAOE,EAAOE,EAAYK,GAC9C,GAAI5C,MAAMuC,MAAiBxO,EAAQwQ,KAAOC,EAAYK,SACpD,MAAM,IAAIzC,MAAM,+BAuBlB,OArBIvC,GAAc9L,EAAQwQ,KAAOC,EAAYO,gBAC3ChR,EAAQwQ,KAAOC,EAAYQ,QAC3BjR,EAAQgP,EAAIhP,EAAQkQ,SAAW,EAAI5B,GAEjCvN,GAAcf,EAAQwQ,KAAOC,EAAYS,eAC3ClR,EAAQwQ,KAAOC,EAAYQ,QAC3BjR,EAAQ8O,EAAI9O,EAAQkQ,SAAW,EAAI9B,GAEjCZ,GAAcxN,EAAQwQ,KAAOC,EAAYM,aAC3C/Q,EAAQwQ,KAAOC,EAAYQ,QAC3BjR,EAAQ8O,EAAI9O,EAAQkQ,SAAW1B,EAAaJ,EAAQI,EACpDxO,EAAQgP,EAAIhP,EAAQkQ,SAAWrB,EAAaP,EAAQO,GAElD7O,EAAQwQ,KAAOC,EAAYyB,MAAQ,IAAMlS,EAAQ2O,IAAM,IAAM3O,EAAQ4O,MACvE5O,EAAQwQ,KAAOC,EAAYQ,eACpBjR,EAAQ2O,UACR3O,EAAQ4O,UACR5O,EAAQmP,YACRnP,EAAQyO,gBACRzO,EAAQ0O,WAEV1O,CAAA,KAMKwN,EAAA2E,aAAApR,EAgDAyM,EAAA4E,QAAA9D,EA+BAd,EAAA6E,KAAAtD,EAsCAvB,EAAA8E,SAAhB,SAAyB9E,QAAA,IAAAA,IAAAA,EAAA,GACvBY,EAAcZ,GACd,IAAI1B,EAAeyE,IACfxP,EAAewP,IACfvQ,EAAauQ,IACbjC,EAAaiC,IAEjB,OAAOxB,GAAK,SAACX,EAASI,EAAOK,EAAOE,EAAYG,GAC9C,IAAME,EAAM3O,KAAKwO,IACbD,GAAA,EACAK,EAAQ,EACRG,EAAQ,EAwBZ,GAtBIpB,EAAQoC,KAAOC,EAAYC,kBAC7BrB,EAAQpD,MAAMH,GAAgB,EAAI0C,EAAQ1C,EAC1C0D,EAAQvD,MAAMlL,GAAgB,EAAI8N,EAAQ9N,GAExCqN,EAAQoC,MAAQC,EAAYE,SAAWF,EAAYC,kBACrD5E,EAAesC,EAAQ8B,SAAW1B,EAAQJ,EAAQiC,GAAKjC,EAAQiC,GAC/DtP,EAAeqN,EAAQ8B,SAAWrB,EAAQT,EAAQkC,GAAKlC,EAAQkC,KAE/DxE,EAAeyE,IACfxP,EAAewP,KAEbnC,EAAQoC,KAAOC,EAAYG,gBAC7B5Q,EAAaiM,MAAMjM,GAAcwO,EAAQ,EAAIA,EAAQxO,EACrDsO,EAAarC,MAAMqC,GAAcO,EAAQ,EAAIA,EAAQP,GAC5CF,EAAQoC,KAAOC,EAAYI,SACpC7Q,EAAaoO,EAAQ8B,SAAW1B,EAAQJ,EAAQ+B,GAAK/B,EAAQ+B,GAC7D7B,EAAaF,EAAQ8B,SAAWrB,EAAQT,EAAQgC,GAAKhC,EAAQkC,KAE7DtQ,EAAauQ,IACbjC,EAAaiC,KAGXnC,EAAQoC,KAAOC,EAAY8B,eAC7BnE,EAAQoC,KAAOC,EAAYyB,MAAQ,IAAM9D,EAAQO,IAAM,IAAMP,EAAQQ,KAAOR,EAAQK,WACpFL,EAAQoC,KAAOC,EAAYE,UAAYvC,EAAQoC,KAAOC,EAAYC,iBAClEtC,EAAQoC,KAAOC,EAAYI,SAAWzC,EAAQoC,KAAOC,EAAYG,eAAgB,CACjF,IAAMnB,OAAA,IAA8BrB,EAAQU,EAAI,EAC7CV,EAAQ8B,SAAW9B,EAAQU,EAAIV,EAAQU,EAAIN,EACxCkB,OAAA,IAA8BtB,EAAQY,EAAI,EAC7CZ,EAAQ8B,SAAW9B,EAAQY,EAAIZ,EAAQY,EAAIH,EAE9CQ,EAASpD,MAAMjM,QAAA,IACUoO,EAAQ+B,GAAKd,EAClCjB,EAAQ8B,SAAW9B,EAAQU,EACzBV,EAAQ+B,GAAK3B,EAHUxO,EAAawO,EAI1CgB,EAASvD,MAAMqC,QAAA,IACUF,EAAQgC,GAAKZ,EAClCpB,EAAQ8B,SAAW9B,EAAQY,EACzBZ,EAAQgC,GAAKvB,EAHUP,EAAaO,EAK1C,IAAMc,OAAA,IAA+BvB,EAAQiC,GAAK,EAC/CjC,EAAQ8B,SAAW9B,EAAQU,EAAIV,EAAQiC,GAAK7B,EACzCoB,OAAA,IAA+BxB,EAAQkC,GAAK,EAC/ClC,EAAQ8B,SAAW9B,EAAQY,EAAIZ,EAAQkC,GAAKzB,EAE3CO,EAAIK,IAASjC,GAAO4B,EAAIM,IAASlC,GACnC4B,EAAIC,IAAU7B,GAAO4B,EAAII,IAAUhC,GACnC4B,EAAIO,IAAUnC,GAAO4B,EAAIQ,IAAUpC,IACnCwB,GAAA,EAAO,CAUX,OANIZ,EAAQoC,KAAOC,EAAYM,YACzB3B,EAAIZ,EAAQO,IAAevB,GAAO4B,EAAIP,EAAQK,IAAe1B,IAC/DwB,GAAA,GAIGA,EAAO,GAAKZ,CAAA,KAOPZ,EAAAgF,OAAA/C,EA0HAjC,EAAAiF,OAAhB,SAAuBjF,EAAW1B,EAAO/K,QAAA,IAAA+K,IAAPA,EAAA,YAAA/K,IAAOA,EAAA,GACvCqN,EAAcZ,EAAG1B,EAAG/K,GACpB,IAAMf,EAAMS,KAAK0N,IAAIX,GACfc,EAAM7N,KAAKyN,IAAIV,GAErB,OAAOiC,EAAOnB,EAAKtO,GAAMA,EAAKsO,EAAKxC,EAAIA,EAAIwC,EAAMvN,EAAIf,EAAKe,EAAI+K,EAAI9L,EAAMe,EAAIuN,EAAA,EAE9Dd,EAAAkF,UAAhB,SAA0BlF,EAAY1B,GAEpC,YAAO,IAAPA,IAFoCA,EAAA,GACpCsC,EAAcZ,EAAI1B,GACX2D,EAAO,EAAG,EAAG,EAAG,EAAGjC,EAAI1B,EAAA,EAEhB0B,EAAAmF,MAAhB,SAAsBnF,EAAY1B,GAEhC,YAAO,IAAPA,IAFgCA,EAAA0B,GAChCY,EAAcZ,EAAI1B,GACX2D,EAAOjC,EAAI,EAAG,EAAG1B,EAAI,EAAG,IAEjB0B,EAAAoF,OAAhB,SAAuBpF,GAErB,OADAY,EAAcZ,GACPiC,EAAO,EAAG,EAAGhP,KAAKoS,KAAKrF,GAAI,EAAG,EAAG,IAE1BA,EAAAsF,OAAhB,SAAuBtF,GAErB,OADAY,EAAcZ,GACPiC,EAAO,EAAGhP,KAAKoS,KAAKrF,GAAI,EAAG,EAAG,EAAG,IAE1BA,EAAAuF,gBAAhB,SAAgCvF,GAE9B,YAAO,IAAPA,IAF8BA,EAAA,GAC9BY,EAAcZ,GACPiC,GAAQ,EAAG,EAAG,EAAG,EAAGjC,EAAS,IAEtBA,EAAAwF,gBAAhB,SAAgCxF,GAE9B,YAAO,IAAPA,IAF8BA,EAAA,GAC9BY,EAAcZ,GACPiC,EAAO,EAAG,EAAG,GAAI,EAAG,EAAGjC,EAAA,EAGhBA,EAAAyF,OAAhB,WACE,OAAOlE,GAAK,SAACvB,EAAS1B,EAAO/K,GAC3B,OAAI0P,EAAYyB,MAAQ1E,EAAQgD,KAAA,SD3UlBhD,EAAe1B,EAAY/K,GAAA,IAAAqN,EAAAE,EAAAO,EAAAE,EACxCvB,EAAIqC,IACPrB,EAAmBhB,EAAK1B,EAAI/K,GAQ9B,IALA,IAAMiO,EAASvO,KAAKyS,IAAI1F,EAAIuC,KAAOvC,EAAIyC,MAAiDZ,EAAhC5O,KAAKC,IAAI8M,EAAIuC,KAAOvC,EAAIyC,MAA4BjB,EACtGQ,EAAY/O,KAAK0S,KAAK9D,EAAW,IAEjCI,EAAqB,IAAIjD,MAAMgD,GACjCE,EAAQ5D,EAAI6D,EAAQ5O,EACf6O,EAAI,EAAGA,EAAIJ,EAAWI,IAAK,CAClC,IAAMuB,EAAW/B,EAAK5B,EAAIuC,KAAOvC,EAAIyC,KAAOL,EAAIJ,GAC1C4B,EAAShC,EAAK5B,EAAIuC,KAAOvC,EAAIyC,MAAQL,EAAI,GAAKJ,GAC9CV,EAAWsC,EAASD,EACpBE,EAAI,EAAI,EAAI5Q,KAAKgK,IAAIqE,EAAWI,EAAM,GAEtCoC,EAAW,CACf7Q,KAAKyN,IAAIiD,EAAWjC,GAAOmC,EAAI5Q,KAAK0N,IAAIgD,EAAWjC,GACnDzO,KAAK0N,IAAIgD,EAAWjC,GAAOmC,EAAI5Q,KAAKyN,IAAIiD,EAAWjC,IAF9CqC,EAAAD,EAAA,GAAIE,EAAAF,EAAA,GAGLG,EAAS,CAAChR,KAAKyN,IAAIkD,EAASlC,GAAMzO,KAAK0N,IAAIiD,EAASlC,IAAnDwC,EAAAD,EAAA,GAAG1F,EAAA0F,EAAA,GACJE,EAAW,CAACD,EAAIL,EAAI5Q,KAAK0N,IAAIiD,EAASlC,GAAMnD,EAAIsF,EAAI5Q,KAAKyN,IAAIkD,EAASlC,IAArE0C,EAAAD,EAAA,GAAIE,EAAAF,EAAA,GACXlC,EAAOG,GAAK,CAACM,SAAU1C,EAAI0C,SAAUM,KAAMC,EAAYE,UACvD,IAAMyC,EAAY,SAACtH,EAAW/K,GACtB,IAAAqN,EAAiBpO,EAAO,CAAC8L,EAAI0B,EAAImB,GAAI5N,EAAIyM,EAAIoB,IAAKpB,EAAI2B,MAArDb,EAAAF,EAAA,GAAOI,EAAAJ,EAAA,GACd,MAAO,CAACZ,EAAIqC,GAAMvB,EAAOd,EAAIsC,GAAMtB,EAAA,EAErCJ,EAA+BgF,EAAU7B,EAAIC,GAA5C/B,EAAOG,GAAGO,GAAA/B,EAAA,GAAIqB,EAAOG,GAAGQ,GAAAhC,EAAA,GACzBE,EAA+B8E,EAAUxB,EAAIC,GAA5CpC,EAAOG,GAAGS,GAAA/B,EAAA,GAAImB,EAAOG,GAAGU,GAAAhC,EAAA,GACzBO,EAA6BuE,EAAU1B,EAAG3F,GAAzC0D,EAAOG,GAAGd,EAAAD,EAAA,GAAGY,EAAOG,GAAGZ,EAAAH,EAAA,GACpBrB,EAAI0C,WACNT,EAAOG,GAAGO,IAAMT,EAChBD,EAAOG,GAAGQ,IAAMT,EAChBF,EAAOG,GAAGS,IAAMX,EAChBD,EAAOG,GAAGU,IAAMX,EAChBF,EAAOG,GAAGd,GAAKY,EACfD,EAAOG,GAAGZ,GAAKW,GAEhBD,GAADX,EAAiB,CAACU,EAAOG,GAAGd,EAAGW,EAAOG,GAAGZ,IAAA,GAAjCW,EAAAZ,EAAA,GAEV,OAAOU,CAAA,CCoS6B,CACnBjC,EAASA,EAAQ0C,SAAW,EAAIpE,EAAO0B,EAAQ0C,SAAW,EAAInP,GAEpEyM,CAAA,KAIKA,EAAA6F,cAAhB,WACE,OAAOtE,GAAK,SAACvB,EAAG1B,EAAI/K,GAQlB,OAPIyM,EAAE0C,WACJpE,EAAK,EACL/K,EAAK,GAEH0P,EAAYyB,MAAQ1E,EAAEgD,MACxBhC,EAAmBhB,EAAG1B,EAAI/K,GAErByM,CAAA,KAGKA,EAAA8F,MAAhB,WACE,OAAO,SAAC9F,GACN,IAAM1B,EAAS,CAAC,EAEhB,IAAK,IAAM/K,KAAOyM,EAChB1B,EAAO/K,GAA2ByM,EAAEzM,GAEtC,OAAO+K,CAAA,GAIK0B,EAAA+F,iBAAhB,WACE,IACMvT,EAAQ8L,IACRsC,EAAQE,IACRY,EAASnO,IACTqO,EACFL,GAAK,SAACjD,EAAS/K,EAAUuN,GAC3B,IAAMS,EAAIG,EAAOd,EAAMpO,EAjBlB,SAACwN,GACN,IAAM1B,EAAS,CAAC,EAEhB,IAAK,IAAM/K,KAAOyM,EAChB1B,EAAO/K,GAA2ByM,EAAEzM,GAEtC,OAAO+K,CAAA,CAWsB0B,CAAM1B,MACnC,SAAS2D,EAAKjC,GACRA,EAAO4B,EAAEoE,OAAQpE,EAAEoE,KAAOhG,GAC1BA,EAAO4B,EAAEqE,OAAQrE,EAAEqE,KAAOjG,EAAA,CAEhC,SAASkC,EAAKlC,GACRA,EAAO4B,EAAEsE,OAAQtE,EAAEsE,KAAOlG,GAC1BA,EAAO4B,EAAEuE,OAAQvE,EAAEuE,KAAOnG,EAAA,CAgBhC,GAdIuB,EAAEyB,KAAOC,EAAYmD,mBACvBnE,EAAK1O,GACL2O,EAAKpB,IAEHS,EAAEyB,KAAOC,EAAYO,eACvBvB,EAAKV,EAAED,GAELC,EAAEyB,KAAOC,EAAYS,cACvBxB,EAAKX,EAAEC,GAELD,EAAEyB,KAAOC,EAAYQ,UACvBxB,EAAKV,EAAED,GACPY,EAAKX,EAAEC,IAELD,EAAEyB,KAAOC,EAAYE,SAAU,CAEjClB,EAAKV,EAAED,GACPY,EAAKX,EAAEC,GAGP,IAFA,IAAAW,EAAA,EAEwBC,EAFJP,EAAWtO,EAAUgO,EAAEoB,GAAIpB,EAAEsB,GAAItB,EAAED,GAE/Ba,EAAAC,EAAA3P,OAAA0P,IAClB,GADKkE,EAAAjE,EAAAD,KACY,EAAIkE,GACvBpE,EAAKD,EAASzO,EAAUgO,EAAEoB,GAAIpB,EAAEsB,GAAItB,EAAED,EAAG+E,IAK7C,IAFA,IAAA1C,EAAA,EAEwBC,EAFJ/B,EAAWf,EAAUS,EAAEqB,GAAIrB,EAAEuB,GAAIvB,EAAEC,GAE/BmC,EAAAC,EAAAnR,OAAAkR,IAClB,GADK0C,EAAAzC,EAAAD,KACY,EAAI0C,GACvBnE,EAAKF,EAASlB,EAAUS,EAAEqB,GAAIrB,EAAEuB,GAAIvB,EAAEC,EAAG6E,GAAA,CAI/C,GAAI9E,EAAEyB,KAAOC,EAAYyB,IAAK,CAE5BzC,EAAKV,EAAED,GACPY,EAAKX,EAAEC,GACPR,EAAmBO,EAAGhO,EAAUuN,GAwBhC,IArBA,IAAMQ,EAAUC,EAAEI,KAAO,IAAM1O,KAAK8N,GAE9B8C,EAAK5Q,KAAKyN,IAAIY,GAAWC,EAAEJ,GAC3B2C,EAAK7Q,KAAK0N,IAAIW,GAAWC,EAAEJ,GAC3B4C,GAAO9Q,KAAK0N,IAAIW,GAAWC,EAAEH,GAC7B4C,EAAM/Q,KAAKyN,IAAIY,GAAWC,EAAEH,GAI5B6C,EAAmB1C,EAAEgB,KAAOhB,EAAEkB,KAClC,CAAClB,EAAEgB,KAAMhB,EAAEkB,OACT,IAAMlB,EAAEkB,KAAO,CAAClB,EAAEkB,KAAO,IAAKlB,EAAEgB,KAAO,KAAO,CAAChB,EAAEkB,KAAMlB,EAAEgB,MAFtD2B,EAAAD,EAAA,GAAQ1F,EAAA0F,EAAA,GAGTE,EAAiB,SAACnE,GAAA,IAAC1B,EAAA0B,EAAA,GAAIzM,EAAAyM,EAAA,GAErBxN,EAAe,IADNS,KAAKuP,MAAMjP,EAAK+K,GACJrL,KAAK8N,GAEhC,OAAOvO,EAAM0R,EAAS1R,EAAM,IAAMA,CAAA,EAAA4R,EAAA,EAKZC,EADJhD,EAA2B0C,GAAMF,EAAI,GAAGyC,IAAInC,GACxCC,EAAAC,EAAA5R,OAAA2R,KAAbiC,EAAAhC,EAAAD,IACOF,GAAUmC,EAAY9H,GACpC0D,EAAKT,EAAMD,EAAEc,GAAIwB,EAAIE,EAAKsC,IAK9B,IADA,IAAAT,EAAA,EACwBW,EADJlF,EAA2B2C,GAAMF,EAAI,GAAGwC,IAAInC,GACxCyB,EAAAW,EAAA9T,OAAAmT,IAAa,CAAhC,IAAMS,GAAAA,EAAAE,EAAAX,IACO1B,GAAUmC,EAAY9H,GACpC2D,EAAKV,EAAMD,EAAEe,GAAIwB,EAAIE,EAAKqC,GAAA,EAIhC,OAAO/H,CAAA,IAOT,OAJAsD,EAAEqE,KAAO,IACTrE,EAAEoE,MAAA,IACFpE,EAAEuE,KAAO,IACTvE,EAAEsE,MAAA,IACKtE,CAAA,EAjmBX,CAAiBL,IAAAA,EAAA,KCLjB,IAAAU,EAAAC,EAAA,oBAAAlC,IAAA,CAsEA,OArEEA,EAAAI,UAAA5M,MAAA,SAAMwM,GACJ,OAAO3L,KAAKmS,UAAUjF,EAAuB+C,MAAMtE,GAAA,EAGrDA,EAAAI,UAAAqG,MAAA,WACE,OAAOpS,KAAKmS,UAAUjF,EAAuBgD,SAAA,EAG/CvE,EAAAI,UAAAsG,MAAA,WACE,OAAOrS,KAAKmS,UAAUjF,EAAuBiD,SAAA,EAG/CxE,EAAAI,UAAAuG,aAAA,SAAa3G,EAAa1B,EAAa/K,GACrC,OAAOc,KAAKmS,UAAUjF,EAAuBkD,cAAczE,EAAG1B,EAAG/K,GAAA,EAGnEyM,EAAAI,UAAAwG,YAAA,WACE,OAAOvS,KAAKmS,UAAUjF,EAAuBoD,eAAA,EAG/C3E,EAAAI,UAAAyG,MAAA,WACE,OAAOxS,KAAKmS,UAAUjF,EAAuBqD,UAAA,EAG/C5E,EAAAI,UAAA0G,KAAA,WACE,OAAOzS,KAAKmS,UAAUjF,EAAuBkE,SAAA,EAG/CzF,EAAAI,UAAA2G,SAAA,SAAS/G,GACP,OAAO3L,KAAKmS,UAAUjF,EAAuBuD,SAAS9E,GAAA,EAGxDA,EAAAI,UAAA4G,UAAA,SAAUhH,EAAW1B,GACnB,OAAOjK,KAAKmS,UAAUjF,EAAuB2D,UAAUlF,EAAG1B,GAAA,EAG5D0B,EAAAI,UAAA6G,MAAA,SAAMjH,EAAW1B,GACf,OAAOjK,KAAKmS,UAAUjF,EAAuB4D,MAAMnF,EAAG1B,GAAA,EAGxD0B,EAAAI,UAAA8G,OAAA,SAAOlH,EAAW1B,EAAY/K,GAC5B,OAAOc,KAAKmS,UAAUjF,EAAuB0D,OAAOjF,EAAG1B,EAAG/K,GAAA,EAG5DyM,EAAAI,UAAA+G,OAAA,SAAOnH,EAAW1B,EAAW/K,EAAWf,EAAWoO,EAAWE,GAC5D,OAAOzM,KAAKmS,UAAUjF,EAAuByD,OAAOhF,EAAG1B,EAAG/K,EAAGf,EAAGoO,EAAGE,GAAA,EAGrEd,EAAAI,UAAAgH,MAAA,SAAMpH,GACJ,OAAO3L,KAAKmS,UAAUjF,EAAuB6D,OAAOpF,GAAA,EAGtDA,EAAAI,UAAAiH,MAAA,SAAMrH,GACJ,OAAO3L,KAAKmS,UAAUjF,EAAuB+D,OAAOtF,GAAA,EAGtDA,EAAAI,UAAAkH,UAAA,SAAUtH,GACR,OAAO3L,KAAKmS,UAAUjF,EAAuBgE,gBAAgBvF,GAAA,EAG/DA,EAAAI,UAAAmH,UAAA,SAAUvH,GACR,OAAO3L,KAAKmS,UAAUjF,EAAuBiE,gBAAgBxF,GAAA,EAG/DA,EAAAI,UAAAoH,aAAA,WACE,OAAOnT,KAAKmS,UAAUjF,EAAuBsE,gBAAA,EAAA7F,CAAA,CAlEjD,GCGMmC,EAAe,SAACnC,GACpB,YAAQA,GAAK,OAASA,GAAK,OAASA,GAAK,OAASA,CAAA,EAC9CoC,EAAU,SAACpC,GACf,UAAIyH,WAAW,IAAMzH,EAAEyH,WAAW,IAAMzH,EAAEyH,WAAW,IAAM,IAAIA,WAAW,IAAA9D,EAAA,SAAA3D,GAa1E,SAAAzM,IAAA,IAAA+K,EACE0B,EAAApM,KAAA,mBAVM0K,EAAAoJ,UAAoB,GACpBpJ,EAAAqJ,gBAA2C,EAC3CrJ,EAAAsJ,oBAAA,EACAtJ,EAAAuJ,wBAAA,EACAvJ,EAAAwJ,iBAAA,EACAxJ,EAAAyJ,uBAAA,EACAzJ,EAAA0J,qBAAA,EACA1J,EAAA2J,QAAoB,GAAA3J,CAAA,CA6Q9B,OArRuCA,EAAA/K,EAAAyM,GAcrCzM,EAAA6M,UAAA8H,OAAA,SAAOlI,GAGL,QAAI,IAAJA,IAHKA,EAAA,IACL3L,KAAK8T,MAAM,IAAKnI,GAEZ,IAAM3L,KAAK4T,QAAQxV,SAAW4B,KAAKwT,uBACrC,MAAM,IAAIO,YAAY,yCAExB,OAAOpI,CAAA,EAGTzM,EAAA6M,UAAA+H,MAAA,SAAMnI,EAAa1B,GAAnB,IAAA/K,EAAA,cAAA+K,IAAmBA,EAAA,IAOjB,IANA,IAAM9L,EAAgB,SAACwN,GACrB1B,EAAS7K,KAAKuM,GACdzM,EAAK0U,QAAQxV,OAAS,EACtBc,EAAKsU,wBAAA,CAAyB,EAGvBjH,EAAI,EAAGA,EAAIZ,EAAIvN,OAAQmO,IAAK,CACnC,IAAME,EAAId,EAAIY,GAERI,IAAa3M,KAAKsT,iBAAmB1E,EAAYyB,KAC5B,IAAxBrQ,KAAK4T,QAAQxV,QAAwC,IAAxB4B,KAAK4T,QAAQxV,QACjB,IAA1B4B,KAAKqT,UAAUjV,QACK,MAAnB4B,KAAKqT,WAAwC,MAAnBrT,KAAKqT,WAC5BrG,EAAgBe,EAAQtB,KACR,MAAnBzM,KAAKqT,WAA2B,MAAN5G,GAC3BE,GAGF,IACEoB,EAAQtB,IACPO,EAMH,GAAI,MAAQP,GAAK,MAAQA,EAKzB,GACG,MAAQA,GAAK,MAAQA,IACtBzM,KAAKyT,iBACJzT,KAAK0T,sBAMR,GAAI,MAAQjH,GAAMzM,KAAKyT,iBAAoBzT,KAAK2T,qBAAwBhH,EAAxE,CAOA,GAAI3M,KAAKqT,YAAc,IAAMrT,KAAKsT,eAAgB,CAChD,IAAMpG,EAAM8G,OAAOhU,KAAKqT,WACxB,GAAIjJ,MAAM8C,GACR,MAAM,IAAI6G,YAAY,4BAA4BxH,GAEpD,GAAIvM,KAAKsT,iBAAmB1E,EAAYyB,IACtC,GAAI,IAAMrQ,KAAK4T,QAAQxV,QAAU,IAAM4B,KAAK4T,QAAQxV,QAClD,GAAI,EAAI8O,EACN,MAAM,IAAI6G,YACR,kCAAkC7G,EAAA,eAAkBX,EAAA,UAGnD,IAAI,IAAMvM,KAAK4T,QAAQxV,QAAU,IAAM4B,KAAK4T,QAAQxV,SACrD,MAAQ4B,KAAKqT,WAAa,MAAQrT,KAAKqT,UACzC,MAAM,IAAIU,YACR,yBAAyB/T,KAAKqT,UAAA,eAAwB9G,EAAA,KAK9DvM,KAAK4T,QAAQxU,KAAK8N,GACdlN,KAAK4T,QAAQxV,SAAWmR,EAAmBvP,KAAKsT,kBAC9C1E,EAAYO,gBAAkBnP,KAAKsT,eACrCnV,EAAc,CACZwQ,KAAMC,EAAYO,cAClBd,SAAUrO,KAAKuT,mBACftG,EAAGC,IAEI0B,EAAYS,eAAiBrP,KAAKsT,eAC3CnV,EAAc,CACZwQ,KAAMC,EAAYS,aAClBhB,SAAUrO,KAAKuT,mBACfpG,EAAGD,IAILlN,KAAKsT,iBAAmB1E,EAAYK,SACpCjP,KAAKsT,iBAAmB1E,EAAYQ,SACpCpP,KAAKsT,iBAAmB1E,EAAYG,gBAEpC5Q,EAAc,CACZwQ,KAAM3O,KAAKsT,eACXjF,SAAUrO,KAAKuT,mBACftG,EAAGjN,KAAK4T,QAAQ,GAChBzG,EAAGnN,KAAK4T,QAAQ,KAGdhF,EAAYK,UAAYjP,KAAKsT,iBAC/BtT,KAAKsT,eAAiB1E,EAAYQ,UAE3BpP,KAAKsT,iBAAmB1E,EAAYE,SAC7C3Q,EAAc,CACZwQ,KAAMC,EAAYE,SAClBT,SAAUrO,KAAKuT,mBACfjF,GAAItO,KAAK4T,QAAQ,GACjBrF,GAAIvO,KAAK4T,QAAQ,GACjBpF,GAAIxO,KAAK4T,QAAQ,GACjBnF,GAAIzO,KAAK4T,QAAQ,GACjB3G,EAAGjN,KAAK4T,QAAQ,GAChBzG,EAAGnN,KAAK4T,QAAQ,KAET5T,KAAKsT,iBAAmB1E,EAAYC,gBAC7C1Q,EAAc,CACZwQ,KAAMC,EAAYC,gBAClBR,SAAUrO,KAAKuT,mBACf/E,GAAIxO,KAAK4T,QAAQ,GACjBnF,GAAIzO,KAAK4T,QAAQ,GACjB3G,EAAGjN,KAAK4T,QAAQ,GAChBzG,EAAGnN,KAAK4T,QAAQ,KAET5T,KAAKsT,iBAAmB1E,EAAYI,QAC7C7Q,EAAc,CACZwQ,KAAMC,EAAYI,QAClBX,SAAUrO,KAAKuT,mBACfjF,GAAItO,KAAK4T,QAAQ,GACjBrF,GAAIvO,KAAK4T,QAAQ,GACjB3G,EAAGjN,KAAK4T,QAAQ,GAChBzG,EAAGnN,KAAK4T,QAAQ,KAET5T,KAAKsT,iBAAmB1E,EAAYyB,KAC7ClS,EAAc,CACZwQ,KAAMC,EAAYyB,IAClBhC,SAAUrO,KAAKuT,mBACfzG,GAAI9M,KAAK4T,QAAQ,GACjB7G,GAAI/M,KAAK4T,QAAQ,GACjBtG,KAAMtN,KAAK4T,QAAQ,GACnBhH,SAAU5M,KAAK4T,QAAQ,GACvB/G,UAAW7M,KAAK4T,QAAQ,GACxB3G,EAAGjN,KAAK4T,QAAQ,GAChBzG,EAAGnN,KAAK4T,QAAQ,MAItB5T,KAAKqT,UAAY,GACjBrT,KAAK0T,uBAAA,EACL1T,KAAKyT,iBAAA,EACLzT,KAAK2T,qBAAA,EACL3T,KAAKwT,wBAAA,CAAyB,CAGhC,IAAI1F,EAAarB,GAGjB,GAAI,MAAQA,GAAKzM,KAAKwT,uBAEpBxT,KAAKwT,wBAAA,OAIP,GAAI,MAAQ/G,GAAK,MAAQA,GAAK,MAAQA,EAMtC,GAAIO,EACFhN,KAAKqT,UAAY5G,EACjBzM,KAAK2T,qBAAA,MAFP,CAOA,GAAI,IAAM3T,KAAK4T,QAAQxV,OACrB,MAAM,IAAI2V,YAAY,iCAAiCxH,EAAA,KAEzD,IAAKvM,KAAKwT,uBACR,MAAM,IAAIO,YACR,yBAAyBtH,EAAA,cAAeF,EAAA,iCAK5C,GAFAvM,KAAKwT,wBAAA,EAED,MAAQ/G,GAAK,MAAQA,EAQlB,GAAI,MAAQA,GAAK,MAAQA,EAC9BzM,KAAKsT,eAAiB1E,EAAYO,cAClCnP,KAAKuT,mBAAqB,MAAQ9G,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BzM,KAAKsT,eAAiB1E,EAAYS,aAClCrP,KAAKuT,mBAAqB,MAAQ9G,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BzM,KAAKsT,eAAiB1E,EAAYK,QAClCjP,KAAKuT,mBAAqB,MAAQ9G,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BzM,KAAKsT,eAAiB1E,EAAYQ,QAClCpP,KAAKuT,mBAAqB,MAAQ9G,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BzM,KAAKsT,eAAiB1E,EAAYE,SAClC9O,KAAKuT,mBAAqB,MAAQ9G,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BzM,KAAKsT,eAAiB1E,EAAYC,gBAClC7O,KAAKuT,mBAAqB,MAAQ9G,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BzM,KAAKsT,eAAiB1E,EAAYI,QAClChP,KAAKuT,mBAAqB,MAAQ9G,OAE7B,GAAI,MAAQA,GAAK,MAAQA,EAC9BzM,KAAKsT,eAAiB1E,EAAYG,eAClC/O,KAAKuT,mBAAqB,MAAQ9G,MAE7B,IAAI,MAAQA,GAAK,MAAQA,EAI9B,MAAM,IAAIsH,YAAY,yBAAyBtH,EAAA,cAAeF,EAAA,KAH9DvM,KAAKsT,eAAiB1E,EAAYyB,IAClCrQ,KAAKuT,mBAAqB,MAAQ9G,CAAA,MAzClCxC,EAAS7K,KAAK,CACZuP,KAAMC,EAAYM,aAEpBlP,KAAKwT,wBAAA,EACLxT,KAAKsT,gBAAkB,OA3BvBtT,KAAKqT,UAAY5G,EACjBzM,KAAK2T,oBAAsB,MAAQlH,CAAA,MArHnCzM,KAAKqT,WAAa5G,EAClBzM,KAAK2T,qBAAA,OANL3T,KAAKqT,WAAa5G,OATlBzM,KAAKqT,WAAa5G,EAClBzM,KAAKyT,iBAAA,OANLzT,KAAKqT,WAAa5G,EAClBzM,KAAK0T,sBAAwB1T,KAAKyT,eAAA,CA2MtC,OAAOxJ,CAAA,EAKT/K,EAAA6M,UAAAoG,UAAA,SAAUxG,GAoBR,OAnBeC,OAAOQ,OAAOpM,KAAM,CACjC8T,MAAO,CACLG,MAAA,SAAMhK,EAAe/K,QAAA,IAAAA,IAAAA,EAAA,IAKnB,IAJA,IAAAf,EAAA,EAIgBoO,EAJOX,OAAOsI,eAAelU,MAAM8T,MAAMvU,KACvDS,KACAiK,GAEc9L,EAAAoO,EAAAnO,OAAAD,IAAgB,CAA3B,IAAMsO,EAAAF,EAAApO,GACHwO,EAAKhB,EAAUc,GACjB9B,MAAMwJ,QAAQxH,GAChBzN,EAASE,KAAAK,MAATP,EAAiByN,GAEjBzN,EAASE,KAAKuN,EAAA,CAGlB,OAAOzN,CAAA,MAAAA,CAAA,CAlR2D,CAGrC2O,GAAAe,EAAA,SAAAjD,GCJrC,SAAAxN,EAAY8L,GAAZ,IAAA/K,EACEyM,EAAApM,KAAA,mBAEEL,EAAKkV,SADH,iBAAoBnK,EACN9L,EAAY2V,MAAM7J,GAElBA,EAAA/K,CAAA,CA2DtB,OAlEiC+K,EAAA9L,EAAAwN,GAW/BxN,EAAA4N,UAAAsI,OAAA,WACE,OAAOlW,EAAYkW,OAAOrU,KAAKoU,SAAA,EAGjCjW,EAAA4N,UAAAuI,UAAA,WACE,IAAM3I,EAAkBuB,EAAuBwE,mBAG/C,OADA1R,KAAKmS,UAAUxG,GACRA,CAAA,EAGTxN,EAAA4N,UAAAoG,UAAA,SACExG,GAIA,IAFA,IAAM1B,EAAc,GAAA/K,EAAA,EAEEf,EAAA6B,KAAKoU,SAALlV,EAAAf,EAAAC,OAAAc,IAAe,CAAhC,IACGqN,EAAqBZ,EAAAxN,EAAAe,IAEvByL,MAAMwJ,QAAQ5H,GAChBtC,EAAY7K,KAAAK,MAAZwK,EAAoBsC,GAEpBtC,EAAY7K,KAAKmN,EAAA,CAIrB,OADAvM,KAAKoU,SAAWnK,EACTjK,IAAA,EAGF7B,EAAAkW,OAAP,SAAc1I,GACZ,ONnB+E,SCnBrDA,GAC5B,IAAI1B,EAAM,GAELU,MAAMwJ,QAAQxI,KACjBA,EAAW,CAACA,IAEd,IAAK,IAAIzM,EAAI,EAAGA,EAAIyM,EAASvN,OAAQc,IAAK,CACxC,IAAMf,EAAUwN,EAASzM,GACzB,GAAIf,EAAQwQ,OAASC,EAAYM,WAC/BjF,GAAO,SACF,GAAI9L,EAAQwQ,OAASC,EAAYO,cACtClF,IAAQ9L,EAAQkQ,SAAW,IAAM,KAC/BlQ,EAAQ8O,OACL,GAAI9O,EAAQwQ,OAASC,EAAYS,aACtCpF,IAAQ9L,EAAQkQ,SAAW,IAAM,KAC/BlQ,EAAQgP,OACL,GAAIhP,EAAQwQ,OAASC,EAAYK,QACtChF,IAAQ9L,EAAQkQ,SAAW,IAAM,KAC/BlQ,EAAQ8O,EApBJ,IAoBc9O,EAAQgP,OACvB,GAAIhP,EAAQwQ,OAASC,EAAYQ,QACtCnF,IAAQ9L,EAAQkQ,SAAW,IAAM,KAC/BlQ,EAAQ8O,EAvBJ,IAuBc9O,EAAQgP,OACvB,GAAIhP,EAAQwQ,OAASC,EAAYE,SACtC7E,IAAQ9L,EAAQkQ,SAAW,IAAM,KAC/BlQ,EAAQmQ,GA1BJ,IA0BenQ,EAAQoQ,GA1BvB,IA2BEpQ,EAAQqQ,GA3BV,IA2BqBrQ,EAAQsQ,GA3B7B,IA4BEtQ,EAAQ8O,EA5BV,IA4BoB9O,EAAQgP,OAC7B,GAAIhP,EAAQwQ,OAASC,EAAYC,gBACtC5E,IAAQ9L,EAAQkQ,SAAW,IAAM,KAC/BlQ,EAAQqQ,GA/BJ,IA+BerQ,EAAQsQ,GA/BvB,IAgCEtQ,EAAQ8O,EAhCV,IAgCoB9O,EAAQgP,OAC7B,GAAIhP,EAAQwQ,OAASC,EAAYI,QACtC/E,IAAQ9L,EAAQkQ,SAAW,IAAM,KAC/BlQ,EAAQmQ,GAnCJ,IAmCenQ,EAAQoQ,GAnCvB,IAoCEpQ,EAAQ8O,EApCV,IAoCoB9O,EAAQgP,OAC7B,GAAIhP,EAAQwQ,OAASC,EAAYG,eACtC9E,IAAQ9L,EAAQkQ,SAAW,IAAM,KAC/BlQ,EAAQ8O,EAvCJ,IAuCc9O,EAAQgP,MACvB,IAAIhP,EAAQwQ,OAASC,EAAYyB,IAQtC,MAAM,IAAI7D,MACR,4BAA8BrO,EAAgBwQ,KAAA,cAAkBzP,EAAA,KARlE+K,IAAQ9L,EAAQkQ,SAAW,IAAM,KAC/BlQ,EAAQ2O,GA1CJ,IA0Ce3O,EAAQ4O,GA1CvB,IA2CE5O,EAAQmP,KA3CV,MA4CInP,EAAQyO,SA5CZ,MA4CgCzO,EAAQ0O,UA5CxC,IA6CE1O,EAAQ8O,EA7CV,IA6CoB9O,EAAQgP,CAAA,EAQtC,OAAOlD,CAAA,CKbE/K,CAAcyM,EAAA,EAGhBxN,EAAA2V,MAAP,SAAanI,GACX,IAAM1B,EAAS,IAAIqF,EACbpQ,EAAyB,GAG/B,OAFA+K,EAAO6J,MAAMnI,EAAMzM,GACnB+K,EAAO4J,OAAO3U,GACPA,CAAA,EAGOf,EAAA+Q,WAAgB,EAChB/Q,EAAA8Q,QAAa,EACb9Q,EAAAgR,cAAmB,EACnBhR,EAAAkR,aAAkB,EAClBlR,EAAAiR,QAAc,GACdjR,EAAA2Q,SAAe,GACf3Q,EAAA0Q,gBAAsB,GACtB1Q,EAAA6Q,QAAe,IACf7Q,EAAA4Q,eAAsB,IACtB5Q,EAAAkS,IAAW,IACXlS,EAAAuS,cAAgBvS,EAAYiR,QAAUjR,EAAYgR,cAAgBhR,EAAYkR,aAC9ElR,EAAA4T,iBAAmB5T,EAAYgR,cAAgBhR,EAAYkR,aAAelR,EAAYiR,QACtGjR,EAAY2Q,SAAW3Q,EAAY0Q,gBAAkB1Q,EAAY6Q,QACjE7Q,EAAY4Q,eAAiB5Q,EAAYkS,IAAAlS,CAAA,CD3DJ,CCNN0P,GAoEpB0B,IAAA3B,EAAA,IACRgB,EAAYK,SAAU,EACvBrB,EAACgB,EAAYQ,SAAU,EACvBxB,EAACgB,EAAYO,eAAgB,EAC7BvB,EAACgB,EAAYS,cAAe,EAC5BzB,EAACgB,EAAYM,YAAa,EAC1BtB,EAACgB,EAAYI,SAAU,EACvBpB,EAACgB,EAAYG,gBAAiB,EAC9BnB,EAACgB,EAAYE,UAAW,EACxBlB,EAACgB,EAAYC,iBAAkB,EAC/BjB,EAACgB,EAAYyB,KAAM,EAAAzC,GCpFvB,SAAS2G,EAAQC,GAaf,OATED,EADoB,oBAAXE,QAAoD,kBAApBA,OAAOC,SACtC,SAAUF,GAClB,cAAcA,CAChB,EAEU,SAAUA,GAClB,OAAOA,GAAyB,oBAAXC,QAAyBD,EAAIrI,cAAgBsI,QAAUD,IAAQC,OAAO1I,UAAY,gBAAkByI,CAC3H,EAGKD,EAAQC,EACjB,CAoDA,IAAIG,EAAW,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAClwCC,EAAW,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAqEvgC,SAASC,EAAuBC,EAAQC,EAAMC,EAAMC,EAAOC,GAKzD,GAJsB,kBAAXJ,IACTA,EAAS/J,SAASoK,eAAeL,KAG9BA,GAA8B,WAApBP,EAAQO,MAA0B,eAAgBA,GAC/D,MAAM,IAAI7I,UAAU,2EAGtB,IAAImJ,EAAUN,EAAOO,WAAW,MAEhC,IACE,OAAOD,EAAQE,aAAaP,EAAMC,EAAMC,EAAOC,EACjD,CAAE,MAAOhW,GACP,MAAM,IAAIsN,MAAM,gCAAkCtN,EACpD,CACF,CAYA,SAASqW,EAAkBT,EAAQC,EAAMC,EAAMC,EAAOC,EAAQM,GAC5D,KAAIpL,MAAMoL,IAAWA,EAAS,GAA9B,CAIAA,GAAU,EACV,IAAIC,EAAYZ,EAAuBC,EAAQC,EAAMC,EAAMC,EAAOC,GAClEO,EAcF,SAA8BA,EAAWV,EAAMC,EAAMC,EAAOC,EAAQM,GAYlE,IAXA,IASIE,EATAC,EAASF,EAAUG,KACnBC,EAAM,EAAIL,EAAS,EAEnBM,EAAcb,EAAQ,EACtBc,EAAeb,EAAS,EACxBc,EAAcR,EAAS,EACvBS,EAAYD,GAAeA,EAAc,GAAK,EAC9CE,EAAa,IAAIC,EACjBC,EAAQF,EAGH/X,EAAI,EAAGA,EAAI0X,EAAK1X,IACvBiY,EAAQA,EAAMzX,KAAO,IAAIwX,EAErBhY,IAAM6X,IACRN,EAAWU,GAIfA,EAAMzX,KAAOuX,EAQb,IAPA,IAAIG,EAAU,KACVC,EAAW,KACXC,EAAK,EACLC,EAAK,EACLC,EAAS9B,EAASa,GAClBkB,EAAS9B,EAASY,GAEbrI,EAAI,EAAGA,EAAI+H,EAAQ/H,IAAK,CAC/BiJ,EAAQF,EAMR,IALA,IAAIS,EAAKhB,EAAOa,GACZI,EAAKjB,EAAOa,EAAK,GACjBK,EAAKlB,EAAOa,EAAK,GACjBM,EAAKnB,EAAOa,EAAK,GAEZO,EAAK,EAAGA,EAAKf,EAAae,IACjCX,EAAMnM,EAAI0M,EACVP,EAAMlM,EAAI0M,EACVR,EAAMjM,EAAI0M,EACVT,EAAM7J,EAAIuK,EACVV,EAAQA,EAAMzX,KAgBhB,IAbA,IAAIqY,EAAS,EACTC,EAAS,EACTC,EAAS,EACTC,EAAS,EACTC,EAAUpB,EAAcW,EACxBU,EAAUrB,EAAcY,EACxBU,EAAUtB,EAAca,EACxBU,EAAUvB,EAAcc,EACxBU,EAAOvB,EAAYU,EACnBc,EAAOxB,EAAYW,EACnBc,EAAOzB,EAAYY,EACnBc,EAAO1B,EAAYa,EAEdc,EAAM,EAAGA,EAAM5B,EAAa4B,IAAO,CAC1C,IAAIpK,EAAIgJ,IAAOV,EAAc8B,EAAM9B,EAAc8B,IAAQ,GACrD3N,EAAI0L,EAAOnI,GACXtD,EAAIyL,EAAOnI,EAAI,GACfrD,EAAIwL,EAAOnI,EAAI,GACfjB,EAAIoJ,EAAOnI,EAAI,GACfqK,EAAM7B,EAAc4B,EACxBJ,IAASpB,EAAMnM,EAAIA,GAAK4N,EACxBJ,IAASrB,EAAMlM,EAAIA,GAAK2N,EACxBH,IAAStB,EAAMjM,EAAIA,GAAK0N,EACxBF,IAASvB,EAAM7J,EAAIA,GAAKsL,EACxBb,GAAU/M,EACVgN,GAAU/M,EACVgN,GAAU/M,EACVgN,GAAU5K,EACV6J,EAAQA,EAAMzX,IAChB,CAEA0X,EAAUH,EACVI,EAAWZ,EAEX,IAAK,IAAIzI,EAAI,EAAGA,EAAIgI,EAAOhI,IAAK,CAC9B,IAAI6K,EAAYH,EAAOlB,IAAWC,EAGlC,GAFAf,EAAOa,EAAK,GAAKsB,EAEC,IAAdA,EAAiB,CACnB,IAAIC,EAAM,IAAMD,EAEhBnC,EAAOa,IAAOgB,EAAOf,IAAWC,GAAUqB,EAC1CpC,EAAOa,EAAK,IAAMiB,EAAOhB,IAAWC,GAAUqB,EAC9CpC,EAAOa,EAAK,IAAMkB,EAAOjB,IAAWC,GAAUqB,CAChD,MACEpC,EAAOa,GAAMb,EAAOa,EAAK,GAAKb,EAAOa,EAAK,GAAK,EAGjDgB,GAAQJ,EACRK,GAAQJ,EACRK,GAAQJ,EACRK,GAAQJ,EACRH,GAAWf,EAAQpM,EACnBoN,GAAWhB,EAAQnM,EACnBoN,GAAWjB,EAAQlM,EACnBoN,GAAWlB,EAAQ9J,EAEnB,IAAIyL,EAAK/K,EAAIuI,EAAS,EAEtBwC,EAAKzB,GAAMyB,EAAKlC,EAAckC,EAAKlC,IAAgB,EAKnD0B,GAJAR,GAAUX,EAAQpM,EAAI0L,EAAOqC,GAK7BP,GAJAR,GAAUZ,EAAQnM,EAAIyL,EAAOqC,EAAK,GAKlCN,GAJAR,GAAUb,EAAQlM,EAAIwL,EAAOqC,EAAK,GAKlCL,GAJAR,GAAUd,EAAQ9J,EAAIoJ,EAAOqC,EAAK,GAKlC3B,EAAUA,EAAQ1X,KAClB,IAAIsZ,GAAY3B,EACZ4B,GAAKD,GAAUhO,EACfkO,GAAKF,GAAU/N,EACfkO,GAAKH,GAAU9N,EACfkO,GAAKJ,GAAU1L,EACnB6K,GAAWc,GACXb,GAAWc,GACXb,GAAWc,GACXb,GAAWc,GACXrB,GAAUkB,GACVjB,GAAUkB,GACVjB,GAAUkB,GACVjB,GAAUkB,GACV/B,EAAWA,EAAS3X,KACpB6X,GAAM,CACR,CAEAD,GAAMtB,CACR,CAEA,IAAK,IAAIqD,GAAK,EAAGA,GAAKrD,EAAOqD,KAAM,CAGjC,IAAIC,GAAM5C,EAFVa,EAAK8B,IAAM,GAGPE,GAAM7C,EAAOa,EAAK,GAClBiC,GAAM9C,EAAOa,EAAK,GAClBkC,GAAM/C,EAAOa,EAAK,GAClBmC,GAAW3C,EAAcuC,GACzBK,GAAW5C,EAAcwC,GACzBK,GAAW7C,EAAcyC,GACzBK,GAAW9C,EAAc0C,GACzBK,GAAQ9C,EAAYsC,GACpBS,GAAQ/C,EAAYuC,GACpBS,GAAQhD,EAAYwC,GACpBS,GAAQjD,EAAYyC,GAExBtC,EAAQF,EAER,IAAK,IAAIiD,GAAM,EAAGA,GAAMnD,EAAamD,KACnC/C,EAAMnM,EAAIsO,GACVnC,EAAMlM,EAAIsO,GACVpC,EAAMjM,EAAIsO,GACVrC,EAAM7J,EAAImM,GACVtC,EAAQA,EAAMzX,KAShB,IANA,IAAIya,GAAKnE,EACLoE,GAAU,EACVC,GAAU,EACVC,GAAU,EACVC,GAAU,EAELC,GAAM,EAAGA,IAAOjE,EAAQiE,KAAO,CACtCjD,EAAK4C,GAAKd,IAAM,EAEhB,IAAIoB,GAAO1D,EAAcyD,GAEzBV,KAAU3C,EAAMnM,EAAIsO,GAAM5C,EAAOa,IAAOkD,GACxCV,KAAU5C,EAAMlM,EAAIsO,GAAM7C,EAAOa,EAAK,IAAMkD,GAC5CT,KAAU7C,EAAMjM,EAAIsO,GAAM9C,EAAOa,EAAK,IAAMkD,GAC5CR,KAAU9C,EAAM7J,EAAImM,GAAM/C,EAAOa,EAAK,IAAMkD,GAC5CF,IAAWjB,GACXc,IAAWb,GACXc,IAAWb,GACXc,IAAWb,GACXtC,EAAQA,EAAMzX,KAEV8a,GAAM1D,IACRqD,IAAMnE,EAEV,CAEAuB,EAAK8B,GACLjC,EAAUH,EACVI,EAAWZ,EAEX,IAAK,IAAIiE,GAAK,EAAGA,GAAKzE,EAAQyE,KAAM,CAClC,IAAIC,GAAMpD,GAAM,EAEhBb,EAAOiE,GAAM,GAAKlB,GAAMQ,GAAQzC,IAAWC,EAEvCgC,GAAM,GACRA,GAAM,IAAMA,GACZ/C,EAAOiE,KAAQb,GAAQtC,IAAWC,GAAUgC,GAC5C/C,EAAOiE,GAAM,IAAMZ,GAAQvC,IAAWC,GAAUgC,GAChD/C,EAAOiE,GAAM,IAAMX,GAAQxC,IAAWC,GAAUgC,IAEhD/C,EAAOiE,IAAOjE,EAAOiE,GAAM,GAAKjE,EAAOiE,GAAM,GAAK,EAGpDb,IAASJ,GACTK,IAASJ,GACTK,IAASJ,GACTK,IAASJ,GACTH,IAAYtC,EAAQpM,EACpB2O,IAAYvC,EAAQnM,EACpB2O,IAAYxC,EAAQlM,EACpB2O,IAAYzC,EAAQ9J,EACpBqN,GAAMtB,KAAOsB,GAAMD,GAAK3D,GAAeD,EAAe6D,GAAM7D,GAAgBd,GAAS,EACrF8D,IAASS,IAAWnD,EAAQpM,EAAI0L,EAAOiE,IACvCZ,IAASK,IAAWhD,EAAQnM,EAAIyL,EAAOiE,GAAM,GAC7CX,IAASK,IAAWjD,EAAQlM,EAAIwL,EAAOiE,GAAM,GAC7CV,IAASK,IAAWlD,EAAQ9J,EAAIoJ,EAAOiE,GAAM,GAC7CvD,EAAUA,EAAQ1X,KAClBga,IAAYJ,GAAMjC,EAASrM,EAC3B2O,IAAYJ,GAAMlC,EAASpM,EAC3B2O,IAAYJ,GAAMnC,EAASnM,EAC3B2O,IAAYJ,GAAMpC,EAAS/J,EAC3BiN,IAAWjB,GACXc,IAAWb,GACXc,IAAWb,GACXc,IAAWb,GACXpC,EAAWA,EAAS3X,KACpB6X,GAAMvB,CACR,CACF,CAEA,OAAOQ,CACT,CApPcoE,CAAqBpE,EAAWV,EAAMC,EAAMC,EAAOC,EAAQM,GACvEV,EAAOO,WAAW,MAAMyE,aAAarE,EAAWV,EAAMC,EALtD,CAMF,CAmcA,IAAImB,EAIJ,SAASA,KApmBT,SAAyB4D,EAAUC,GACjC,KAAMD,aAAoBC,GACxB,MAAM,IAAI/N,UAAU,oCAExB,CAimBEgO,CAAgBja,KAAMmW,GAEtBnW,KAAKiK,EAAI,EACTjK,KAAKkK,EAAI,EACTlK,KAAKmK,EAAI,EACTnK,KAAKuM,EAAI,EACTvM,KAAKrB,KAAO,IACd,E", "sources": ["../node_modules/performance-now/src/performance-now.coffee", "../node_modules/raf/index.js", "../node_modules/rgbcolor/index.js", "../node_modules/svg-pathdata/node_modules/tslib/tslib.es6.js", "../node_modules/svg-pathdata/src/SVGPathDataEncoder.ts", "../node_modules/svg-pathdata/src/mathUtils.ts", "../node_modules/svg-pathdata/src/SVGPathDataTransformer.ts", "../node_modules/svg-pathdata/src/TransformableSVG.ts", "../node_modules/svg-pathdata/src/SVGPathDataParser.ts", "../node_modules/svg-pathdata/src/SVGPathData.ts", "../node_modules/stackblur-canvas/dist/stackblur-es.js"], "sourcesContent": ["if performance? and performance.now\n  module.exports = -> performance.now()\nelse if process? and process.hrtime\n  module.exports = -> (getNanoSeconds() - nodeLoadTime) / 1e6\n  hrtime = process.hrtime\n  getNanoSeconds = ->\n    hr = hrtime()\n    hr[0] * 1e9 + hr[1]\n  moduleLoadTime = getNanoSeconds()\n  upTime = process.uptime() * 1e9\n  nodeLoadTime = moduleLoadTime - upTime\nelse if Date.now\n  module.exports = -> Date.now() - loadTime\n  loadTime = Date.now()\nelse\n  module.exports = -> new Date().getTime() - loadTime\n  loadTime = new Date().getTime()\n", "var now = require('performance-now')\n  , root = typeof window === 'undefined' ? global : window\n  , vendors = ['moz', 'webkit']\n  , suffix = 'AnimationFrame'\n  , raf = root['request' + suffix]\n  , caf = root['cancel' + suffix] || root['cancelRequest' + suffix]\n\nfor(var i = 0; !raf && i < vendors.length; i++) {\n  raf = root[vendors[i] + 'Request' + suffix]\n  caf = root[vendors[i] + 'Cancel' + suffix]\n      || root[vendors[i] + 'CancelRequest' + suffix]\n}\n\n// Some versions of FF have rAF but not cAF\nif(!raf || !caf) {\n  var last = 0\n    , id = 0\n    , queue = []\n    , frameDuration = 1000 / 60\n\n  raf = function(callback) {\n    if(queue.length === 0) {\n      var _now = now()\n        , next = Math.max(0, frameDuration - (_now - last))\n      last = next + _now\n      setTimeout(function() {\n        var cp = queue.slice(0)\n        // Clear queue here to prevent\n        // callbacks from appending listeners\n        // to the current frame's queue\n        queue.length = 0\n        for(var i = 0; i < cp.length; i++) {\n          if(!cp[i].cancelled) {\n            try{\n              cp[i].callback(last)\n            } catch(e) {\n              setTimeout(function() { throw e }, 0)\n            }\n          }\n        }\n      }, Math.round(next))\n    }\n    queue.push({\n      handle: ++id,\n      callback: callback,\n      cancelled: false\n    })\n    return id\n  }\n\n  caf = function(handle) {\n    for(var i = 0; i < queue.length; i++) {\n      if(queue[i].handle === handle) {\n        queue[i].cancelled = true\n      }\n    }\n  }\n}\n\nmodule.exports = function(fn) {\n  // Wrap in a new function to prevent\n  // `cancel` potentially being assigned\n  // to the native rAF function\n  return raf.call(root, fn)\n}\nmodule.exports.cancel = function() {\n  caf.apply(root, arguments)\n}\nmodule.exports.polyfill = function(object) {\n  if (!object) {\n    object = root;\n  }\n  object.requestAnimationFrame = raf\n  object.cancelAnimationFrame = caf\n}\n", "/*\n\tBased on rgbcolor.js by <PERSON><PERSON><PERSON> <<EMAIL>>\n\thttp://www.phpied.com/rgb-color-parser-in-javascript/\n*/\n\nmodule.exports = function(color_string) {\n    this.ok = false;\n    this.alpha = 1.0;\n\n    // strip any leading #\n    if (color_string.charAt(0) == '#') { // remove # if any\n        color_string = color_string.substr(1,6);\n    }\n\n    color_string = color_string.replace(/ /g,'');\n    color_string = color_string.toLowerCase();\n\n    // before getting into regexps, try simple matches\n    // and overwrite the input\n    var simple_colors = {\n        aliceblue: 'f0f8ff',\n        antiquewhite: 'faebd7',\n        aqua: '00ffff',\n        aquamarine: '7fffd4',\n        azure: 'f0ffff',\n        beige: 'f5f5dc',\n        bisque: 'ffe4c4',\n        black: '000000',\n        blanchedalmond: 'ffebcd',\n        blue: '0000ff',\n        blueviolet: '8a2be2',\n        brown: 'a52a2a',\n        burlywood: 'deb887',\n        cadetblue: '5f9ea0',\n        chartreuse: '7fff00',\n        chocolate: 'd2691e',\n        coral: 'ff7f50',\n        cornflowerblue: '6495ed',\n        cornsilk: 'fff8dc',\n        crimson: 'dc143c',\n        cyan: '00ffff',\n        darkblue: '00008b',\n        darkcyan: '008b8b',\n        darkgoldenrod: 'b8860b',\n        darkgray: 'a9a9a9',\n        darkgreen: '006400',\n        darkkhaki: 'bdb76b',\n        darkmagenta: '8b008b',\n        darkolivegreen: '556b2f',\n        darkorange: 'ff8c00',\n        darkorchid: '9932cc',\n        darkred: '8b0000',\n        darksalmon: 'e9967a',\n        darkseagreen: '8fbc8f',\n        darkslateblue: '483d8b',\n        darkslategray: '2f4f4f',\n        darkturquoise: '00ced1',\n        darkviolet: '9400d3',\n        deeppink: 'ff1493',\n        deepskyblue: '00bfff',\n        dimgray: '696969',\n        dodgerblue: '1e90ff',\n        feldspar: 'd19275',\n        firebrick: 'b22222',\n        floralwhite: 'fffaf0',\n        forestgreen: '228b22',\n        fuchsia: 'ff00ff',\n        gainsboro: 'dcdcdc',\n        ghostwhite: 'f8f8ff',\n        gold: 'ffd700',\n        goldenrod: 'daa520',\n        gray: '808080',\n        green: '008000',\n        greenyellow: 'adff2f',\n        honeydew: 'f0fff0',\n        hotpink: 'ff69b4',\n        indianred : 'cd5c5c',\n        indigo : '4b0082',\n        ivory: 'fffff0',\n        khaki: 'f0e68c',\n        lavender: 'e6e6fa',\n        lavenderblush: 'fff0f5',\n        lawngreen: '7cfc00',\n        lemonchiffon: 'fffacd',\n        lightblue: 'add8e6',\n        lightcoral: 'f08080',\n        lightcyan: 'e0ffff',\n        lightgoldenrodyellow: 'fafad2',\n        lightgrey: 'd3d3d3',\n        lightgreen: '90ee90',\n        lightpink: 'ffb6c1',\n        lightsalmon: 'ffa07a',\n        lightseagreen: '20b2aa',\n        lightskyblue: '87cefa',\n        lightslateblue: '8470ff',\n        lightslategray: '778899',\n        lightsteelblue: 'b0c4de',\n        lightyellow: 'ffffe0',\n        lime: '00ff00',\n        limegreen: '32cd32',\n        linen: 'faf0e6',\n        magenta: 'ff00ff',\n        maroon: '800000',\n        mediumaquamarine: '66cdaa',\n        mediumblue: '0000cd',\n        mediumorchid: 'ba55d3',\n        mediumpurple: '9370d8',\n        mediumseagreen: '3cb371',\n        mediumslateblue: '7b68ee',\n        mediumspringgreen: '00fa9a',\n        mediumturquoise: '48d1cc',\n        mediumvioletred: 'c71585',\n        midnightblue: '191970',\n        mintcream: 'f5fffa',\n        mistyrose: 'ffe4e1',\n        moccasin: 'ffe4b5',\n        navajowhite: 'ffdead',\n        navy: '000080',\n        oldlace: 'fdf5e6',\n        olive: '808000',\n        olivedrab: '6b8e23',\n        orange: 'ffa500',\n        orangered: 'ff4500',\n        orchid: 'da70d6',\n        palegoldenrod: 'eee8aa',\n        palegreen: '98fb98',\n        paleturquoise: 'afeeee',\n        palevioletred: 'd87093',\n        papayawhip: 'ffefd5',\n        peachpuff: 'ffdab9',\n        peru: 'cd853f',\n        pink: 'ffc0cb',\n        plum: 'dda0dd',\n        powderblue: 'b0e0e6',\n        purple: '800080',\n        rebeccapurple: '663399',\n        red: 'ff0000',\n        rosybrown: 'bc8f8f',\n        royalblue: '4169e1',\n        saddlebrown: '8b4513',\n        salmon: 'fa8072',\n        sandybrown: 'f4a460',\n        seagreen: '2e8b57',\n        seashell: 'fff5ee',\n        sienna: 'a0522d',\n        silver: 'c0c0c0',\n        skyblue: '87ceeb',\n        slateblue: '6a5acd',\n        slategray: '708090',\n        snow: 'fffafa',\n        springgreen: '00ff7f',\n        steelblue: '4682b4',\n        tan: 'd2b48c',\n        teal: '008080',\n        thistle: 'd8bfd8',\n        tomato: 'ff6347',\n        turquoise: '40e0d0',\n        violet: 'ee82ee',\n        violetred: 'd02090',\n        wheat: 'f5deb3',\n        white: 'ffffff',\n        whitesmoke: 'f5f5f5',\n        yellow: 'ffff00',\n        yellowgreen: '9acd32'\n    };\n    color_string = simple_colors[color_string] || color_string;\n    // emd of simple type-in colors\n\n    // array of color definition objects\n    var color_defs = [\n        {\n            re: /^rgba\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3}),\\s*((?:\\d?\\.)?\\d)\\)$/,\n            example: ['rgba(123, 234, 45, 0.8)', 'rgba(255,234,245,1.0)'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1]),\n                    parseInt(bits[2]),\n                    parseInt(bits[3]),\n                    parseFloat(bits[4])\n                ];\n            }\n        },\n        {\n            re: /^rgb\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3})\\)$/,\n            example: ['rgb(123, 234, 45)', 'rgb(255,234,245)'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1]),\n                    parseInt(bits[2]),\n                    parseInt(bits[3])\n                ];\n            }\n        },\n        {\n            re: /^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n            example: ['#00ff00', '336699'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1], 16),\n                    parseInt(bits[2], 16),\n                    parseInt(bits[3], 16)\n                ];\n            }\n        },\n        {\n            re: /^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n            example: ['#fb0', 'f0f'],\n            process: function (bits){\n                return [\n                    parseInt(bits[1] + bits[1], 16),\n                    parseInt(bits[2] + bits[2], 16),\n                    parseInt(bits[3] + bits[3], 16)\n                ];\n            }\n        }\n    ];\n\n    // search through the definitions to find a match\n    for (var i = 0; i < color_defs.length; i++) {\n        var re = color_defs[i].re;\n        var processor = color_defs[i].process;\n        var bits = re.exec(color_string);\n        if (bits) {\n            var channels = processor(bits);\n            this.r = channels[0];\n            this.g = channels[1];\n            this.b = channels[2];\n            if (channels.length > 3) {\n                this.alpha = channels[3];\n            }\n            this.ok = true;\n        }\n\n    }\n\n    // validate/cleanup values\n    this.r = (this.r < 0 || isNaN(this.r)) ? 0 : ((this.r > 255) ? 255 : this.r);\n    this.g = (this.g < 0 || isNaN(this.g)) ? 0 : ((this.g > 255) ? 255 : this.g);\n    this.b = (this.b < 0 || isNaN(this.b)) ? 0 : ((this.b > 255) ? 255 : this.b);\n    this.alpha = (this.alpha < 0) ? 0 : ((this.alpha > 1.0 || isNaN(this.alpha)) ? 1.0 : this.alpha);\n\n    // some getters\n    this.toRGB = function () {\n        return 'rgb(' + this.r + ', ' + this.g + ', ' + this.b + ')';\n    }\n    this.toRGBA = function () {\n        return 'rgba(' + this.r + ', ' + this.g + ', ' + this.b + ', ' + this.alpha + ')';\n    }\n    this.toHex = function () {\n        var r = this.r.toString(16);\n        var g = this.g.toString(16);\n        var b = this.b.toString(16);\n        if (r.length == 1) r = '0' + r;\n        if (g.length == 1) g = '0' + g;\n        if (b.length == 1) b = '0' + b;\n        return '#' + r + g + b;\n    }\n\n    // help\n    this.getHelpXML = function () {\n\n        var examples = new Array();\n        // add regexps\n        for (var i = 0; i < color_defs.length; i++) {\n            var example = color_defs[i].example;\n            for (var j = 0; j < example.length; j++) {\n                examples[examples.length] = example[j];\n            }\n        }\n        // add type-in colors\n        for (var sc in simple_colors) {\n            examples[examples.length] = sc;\n        }\n\n        var xml = document.createElement('ul');\n        xml.setAttribute('id', 'rgbcolor-examples');\n        for (var i = 0; i < examples.length; i++) {\n            try {\n                var list_item = document.createElement('li');\n                var list_color = new RGBColor(examples[i]);\n                var example_div = document.createElement('div');\n                example_div.style.cssText =\n                        'margin: 3px; '\n                        + 'border: 1px solid black; '\n                        + 'background:' + list_color.toHex() + '; '\n                        + 'color:' + list_color.toHex()\n                ;\n                example_div.appendChild(document.createTextNode('test'));\n                var list_item_value = document.createTextNode(\n                    ' ' + examples[i] + ' -> ' + list_color.toRGB() + ' -> ' + list_color.toHex()\n                );\n                list_item.appendChild(example_div);\n                list_item.appendChild(list_item_value);\n                xml.appendChild(list_item);\n\n            } catch(e){}\n        }\n        return xml;\n\n    }\n\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import { SVGPathData } from \"./SVGPathData\";\nimport { SVGCommand } from \"./types\";\n\n// Encode SVG PathData\n// http://www.w3.org/TR/SVG/paths.html#PathDataBNF\n\n// Private consts : Char groups\nconst WSP = \" \";\n\nexport function encodeSVGPath(commands: SVGCommand | SVGCommand[]) {\n  let str = \"\";\n\n  if (!Array.isArray(commands)) {\n    commands = [commands];\n  }\n  for (let i = 0; i < commands.length; i++) {\n    const command = commands[i];\n    if (command.type === SVGPathData.CLOSE_PATH) {\n      str += \"z\";\n    } else if (command.type === SVGPathData.HORIZ_LINE_TO) {\n      str += (command.relative ? \"h\" : \"H\") +\n        command.x;\n    } else if (command.type === SVGPathData.VERT_LINE_TO) {\n      str += (command.relative ? \"v\" : \"V\") +\n        command.y;\n    } else if (command.type === SVGPathData.MOVE_TO) {\n      str += (command.relative ? \"m\" : \"M\") +\n        command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.LINE_TO) {\n      str += (command.relative ? \"l\" : \"L\") +\n        command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.CURVE_TO) {\n      str += (command.relative ? \"c\" : \"C\") +\n        command.x1 + WSP + command.y1 +\n        WSP + command.x2 + WSP + command.y2 +\n        WSP + command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.SMOOTH_CURVE_TO) {\n      str += (command.relative ? \"s\" : \"S\") +\n        command.x2 + WSP + command.y2 +\n        WSP + command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.QUAD_TO) {\n      str += (command.relative ? \"q\" : \"Q\") +\n        command.x1 + WSP + command.y1 +\n        WSP + command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.SMOOTH_QUAD_TO) {\n      str += (command.relative ? \"t\" : \"T\") +\n        command.x + WSP + command.y;\n    } else if (command.type === SVGPathData.ARC) {\n      str += (command.relative ? \"a\" : \"A\") +\n        command.rX + WSP + command.rY +\n        WSP + command.xRot +\n        WSP + (+command.lArcFlag) + WSP + (+command.sweepFlag) +\n        WSP + command.x + WSP + command.y;\n    } else {\n      // Unknown command\n      throw new Error(\n        `Unexpected command type \"${ (command as any).type}\" at index ${i}.`);\n    }\n  }\n\n  return str;\n}\n", "import { SVGPathData } from \"./SVGPathData\";\nimport { CommandA, CommandC } from \"./types\";\n\nexport function rotate([x, y]: [number, number], rad: number) {\n  return [\n    x * Math.cos(rad) - y * Math.sin(rad),\n    x * Math.sin(rad) + y * Math.cos(rad),\n  ];\n}\n\nconst DEBUG_CHECK_NUMBERS = true;\nexport function assertNumbers(...numbers: number[]) {\n  if (DEBUG_CHECK_NUMBERS) {\n    for (let i = 0; i < numbers.length; i++) {\n      if (\"number\" !== typeof numbers[i]) {\n        throw new Error(\n          `assertNumbers arguments[${i}] is not a number. ${typeof numbers[i]} == typeof ${numbers[i]}`);\n      }\n    }\n  }\n  return true;\n}\n\nconst PI = Math.PI;\n\n/**\n * https://www.w3.org/TR/SVG/implnote.html#ArcImplementationNotes\n * Fixes rX and rY.\n * Ensures lArcFlag and sweepFlag are 0 or 1\n * Adds center coordinates: command.cX, command.cY (relative or absolute, depending on command.relative)\n * Adds start and end arc parameters (in degrees): command.phi1, command.phi2; phi1 < phi2 iff. c.sweepFlag == true\n */\nexport function annotateArcCommand(c: CommandA, x1: number, y1: number) {\n  c.lArcFlag = (0 === c.lArcFlag) ? 0 : 1;\n  c.sweepFlag = (0 === c.sweepFlag) ? 0 : 1;\n  // tslint:disable-next-line\n  let {rX, rY, x, y} = c;\n\n  rX = Math.abs(c.rX);\n  rY = Math.abs(c.rY);\n  const [x1_, y1_] = rotate([(x1 - x) / 2, (y1 - y) / 2], -c.xRot / 180 * PI);\n  const testValue = Math.pow(x1_, 2) / Math.pow(rX, 2) + Math.pow(y1_, 2) / Math.pow(rY, 2);\n\n  if (1 < testValue) {\n    rX *= Math.sqrt(testValue);\n    rY *= Math.sqrt(testValue);\n  }\n  c.rX = rX;\n  c.rY = rY;\n  const c_ScaleTemp = (Math.pow(rX, 2) * Math.pow(y1_, 2) + Math.pow(rY, 2) * Math.pow(x1_, 2));\n  const c_Scale = (c.lArcFlag !== c.sweepFlag ? 1 : -1) *\n    Math.sqrt(Math.max(0, (Math.pow(rX, 2) * Math.pow(rY, 2) - c_ScaleTemp) / c_ScaleTemp));\n  const cx_ = rX * y1_ / rY * c_Scale;\n  const cy_ = -rY * x1_ / rX * c_Scale;\n  const cRot = rotate([cx_, cy_], c.xRot / 180 * PI);\n\n  c.cX = cRot[0] + (x1 + x) / 2;\n  c.cY = cRot[1] + (y1 + y) / 2;\n  c.phi1 = Math.atan2((y1_ - cy_) / rY, (x1_ - cx_) / rX);\n  c.phi2 = Math.atan2((-y1_ - cy_) / rY, (-x1_ - cx_) / rX);\n  if (0 === c.sweepFlag && c.phi2 > c.phi1) {\n    c.phi2 -= 2 * PI;\n  }\n  if (1 === c.sweepFlag && c.phi2 < c.phi1) {\n    c.phi2 += 2 * PI;\n  }\n  c.phi1 *= 180 / PI;\n  c.phi2 *= 180 / PI;\n}\n\n/**\n * Solves a quadratic system of equations of the form\n *      a * x + b * y = c\n *      x² + y² = 1\n * This can be understood as the intersection of the unit circle with a line.\n *      => y = (c - a x) / b\n *      => x² + (c - a x)² / b² = 1\n *      => x² b² + c² - 2 c a x + a² x² = b²\n *      => (a² + b²) x² - 2 a c x + (c² - b²) = 0\n */\nexport function intersectionUnitCircleLine(a: number, b: number, c: number): [number, number][] {\n  assertNumbers(a, b, c);\n  // cf. pqFormula\n  const termSqr = a * a + b * b - c * c;\n\n  if (0 > termSqr) {\n    return [];\n  } else if (0 === termSqr) {\n    return [\n      [\n        (a * c) / (a * a + b * b),\n        (b * c) / (a * a + b * b)]];\n  }\n  const term = Math.sqrt(termSqr);\n\n  return [\n    [\n      (a * c + b * term) / (a * a + b * b),\n      (b * c - a * term) / (a * a + b * b)],\n    [\n      (a * c - b * term) / (a * a + b * b),\n      (b * c + a * term) / (a * a + b * b)]];\n\n}\n\nexport const DEG = Math.PI / 180;\n\nexport function lerp(a: number, b: number, t: number) {\n  return (1 - t) * a + t * b;\n}\n\nexport function arcAt(c: number, x1: number, x2: number, phiDeg: number) {\n  return c + Math.cos(phiDeg / 180 * PI) * x1 + Math.sin(phiDeg / 180 * PI) * x2;\n}\n\nexport function bezierRoot(x0: number, x1: number, x2: number, x3: number) {\n  const EPS = 1e-6;\n  const x01 = x1 - x0;\n  const x12 = x2 - x1;\n  const x23 = x3 - x2;\n  const a = 3 * x01 + 3 * x23 - 6 * x12;\n  const b = (x12 - x01) * 6;\n  const c = 3 * x01;\n  // solve a * t² + b * t + c = 0\n\n  if (Math.abs(a) < EPS) {\n    // equivalent to b * t + c =>\n    return [-c / b];\n  }\n  return pqFormula(b / a, c / a, EPS);\n\n}\n\nexport function bezierAt(x0: number, x1: number, x2: number, x3: number, t: number) {\n  // console.log(x0, y0, x1, y1, x2, y2, x3, y3, t)\n  const s = 1 - t;\n  const c0 = s * s * s;\n  const c1 = 3 * s * s * t;\n  const c2 = 3 * s * t * t;\n  const c3 = t * t * t;\n\n  return x0 * c0 + x1 * c1 + x2 * c2 + x3 * c3;\n}\n\nfunction pqFormula(p: number, q: number, PRECISION = 1e-6) {\n  // 4 times the discriminant:in\n  const discriminantX4 = p * p / 4 - q;\n\n  if (discriminantX4 < -PRECISION) {\n    return [];\n  } else if (discriminantX4 <= PRECISION) {\n    return [-p / 2];\n  }\n  const root = Math.sqrt(discriminantX4);\n\n  return [-(p / 2) - root, -(p / 2) + root];\n\n}\n\nexport function a2c(arc: CommandA, x0: number, y0: number): CommandC[] {\n  if (!arc.cX) {\n    annotateArcCommand(arc, x0, y0);\n  }\n\n  const phiMin = Math.min(arc.phi1!, arc.phi2!), phiMax = Math.max(arc.phi1!, arc.phi2!), deltaPhi = phiMax - phiMin;\n  const partCount = Math.ceil(deltaPhi / 90 );\n\n  const result: CommandC[] = new Array(partCount);\n  let prevX = x0, prevY = y0;\n  for (let i = 0; i < partCount; i++) {\n    const phiStart = lerp(arc.phi1!, arc.phi2!, i / partCount);\n    const phiEnd = lerp(arc.phi1!, arc.phi2!, (i + 1) / partCount);\n    const deltaPhi = phiEnd - phiStart;\n    const f = 4 / 3 * Math.tan(deltaPhi * DEG / 4);\n    // x1/y1, x2/y2 and x/y coordinates on the unit circle for phiStart/phiEnd\n    const [x1, y1] = [\n      Math.cos(phiStart * DEG) - f * Math.sin(phiStart * DEG),\n      Math.sin(phiStart * DEG) + f * Math.cos(phiStart * DEG)];\n    const [x, y] = [Math.cos(phiEnd * DEG), Math.sin(phiEnd * DEG)];\n    const [x2, y2] = [x + f * Math.sin(phiEnd * DEG), y - f * Math.cos(phiEnd * DEG)];\n    result[i] = {relative: arc.relative, type: SVGPathData.CURVE_TO } as any;\n    const transform = (x: number, y: number) => {\n      const [xTemp, yTemp] = rotate([x * arc.rX, y * arc.rY], arc.xRot);\n      return [arc.cX! + xTemp, arc.cY! + yTemp];\n    };\n    [result[i].x1, result[i].y1] = transform(x1, y1);\n    [result[i].x2, result[i].y2] = transform(x2, y2);\n    [result[i].x, result[i].y] = transform(x, y);\n    if (arc.relative) {\n      result[i].x1 -= prevX;\n      result[i].y1 -= prevY;\n      result[i].x2 -= prevX;\n      result[i].y2 -= prevY;\n      result[i].x -= prevX;\n      result[i].y -= prevY;\n    }\n    [prevX, prevY] = [result[i].x, result[i].y];\n  }\n  return result;\n}\n", "// Transform SVG PathData\n// http://www.w3.org/TR/SVG/paths.html#PathDataBNF\n\nimport { a2c, annotateArcCommand, arcAt, assertNumbers, bezierAt, bezierRoot,\n  intersectionUnitCircleLine } from \"./mathUtils\";\nimport { SVGPathData } from \"./SVGPathData\";\nimport { SVGCommand, TransformFunction } from \"./types\";\n\nexport namespace SVGPathDataTransformer {\n  // Predefined transforming functions\n  // Rounds commands values\n  export function ROUND(roundVal = 1e13) {\n    assertNumbers(roundVal);\n    function rf(val: number) { return Math.round(val * roundVal) / roundVal; }\n    return function round(command: any) {\n      if (\"undefined\" !== typeof command.x1) {\n        command.x1 = rf(command.x1);\n      }\n      if (\"undefined\" !== typeof command.y1) {\n        command.y1 = rf(command.y1);\n      }\n\n      if (\"undefined\" !== typeof command.x2) {\n        command.x2 = rf(command.x2);\n      }\n      if (\"undefined\" !== typeof command.y2) {\n        command.y2 = rf(command.y2);\n      }\n\n      if (\"undefined\" !== typeof command.x) {\n        command.x = rf(command.x);\n      }\n      if (\"undefined\" !== typeof command.y) {\n        command.y = rf(command.y);\n      }\n\n      if (\"undefined\" !== typeof command.rX) {\n        command.rX = rf(command.rX);\n      }\n      if (\"undefined\" !== typeof command.rY) {\n        command.rY = rf(command.rY);\n      }\n\n      return command;\n    };\n  }\n  // Relative to absolute commands\n  export function TO_ABS() {\n    return INFO((command, prevX, prevY) => {\n      if (command.relative) {\n        // x1/y1 values\n        if (\"undefined\" !== typeof command.x1) {\n          command.x1 += prevX;\n        }\n        if (\"undefined\" !== typeof command.y1) {\n          command.y1 += prevY;\n        }\n        // x2/y2 values\n        if (\"undefined\" !== typeof command.x2) {\n          command.x2 += prevX;\n        }\n        if (\"undefined\" !== typeof command.y2) {\n          command.y2 += prevY;\n        }\n        // Finally x/y values\n        if (\"undefined\" !== typeof command.x) {\n          command.x += prevX;\n        }\n        if (\"undefined\" !== typeof command.y) {\n          command.y += prevY;\n        }\n        command.relative = false;\n      }\n      return command;\n    });\n  }\n  // Absolute to relative commands\n  export function TO_REL() {\n    return INFO((command, prevX, prevY) => {\n      if (!command.relative) {\n        // x1/y1 values\n        if (\"undefined\" !== typeof command.x1) {\n          command.x1 -= prevX;\n        }\n        if (\"undefined\" !== typeof command.y1) {\n          command.y1 -= prevY;\n        }\n        // x2/y2 values\n        if (\"undefined\" !== typeof command.x2) {\n          command.x2 -= prevX;\n        }\n        if (\"undefined\" !== typeof command.y2) {\n          command.y2 -= prevY;\n        }\n        // Finally x/y values\n        if (\"undefined\" !== typeof command.x) {\n          command.x -= prevX;\n        }\n        if (\"undefined\" !== typeof command.y) {\n          command.y -= prevY;\n        }\n        command.relative = true;\n      }\n      return command;\n    });\n  }\n  // Convert H, V, Z and A with rX = 0 to L\n  export function NORMALIZE_HVZ(normalizeZ = true, normalizeH = true, normalizeV = true) {\n    return INFO((command, prevX, prevY, pathStartX, pathStartY) => {\n      if (isNaN(pathStartX) && !(command.type & SVGPathData.MOVE_TO)) {\n        throw new Error(\"path must start with moveto\");\n      }\n      if (normalizeH && command.type & SVGPathData.HORIZ_LINE_TO) {\n        command.type = SVGPathData.LINE_TO;\n        command.y = command.relative ? 0 : prevY;\n      }\n      if (normalizeV && command.type & SVGPathData.VERT_LINE_TO) {\n        command.type = SVGPathData.LINE_TO;\n        command.x = command.relative ? 0 : prevX;\n      }\n      if (normalizeZ && command.type & SVGPathData.CLOSE_PATH) {\n        command.type = SVGPathData.LINE_TO;\n        command.x = command.relative ? pathStartX - prevX : pathStartX;\n        command.y = command.relative ? pathStartY - prevY : pathStartY;\n      }\n      if (command.type & SVGPathData.ARC && (0 === command.rX || 0 === command.rY)) {\n        command.type = SVGPathData.LINE_TO;\n        delete command.rX;\n        delete command.rY;\n        delete command.xRot;\n        delete command.lArcFlag;\n        delete command.sweepFlag;\n      }\n      return command;\n    });\n  }\n  /*\n   * Transforms smooth curves and quads to normal curves and quads (SsTt to CcQq)\n   */\n  export function NORMALIZE_ST() {\n    let prevCurveC2X = NaN;\n    let prevCurveC2Y = NaN;\n    let prevQuadCX = NaN;\n    let prevQuadCY = NaN;\n\n    return INFO((command, prevX, prevY) => {\n      if (command.type & SVGPathData.SMOOTH_CURVE_TO) {\n        command.type = SVGPathData.CURVE_TO;\n        prevCurveC2X = isNaN(prevCurveC2X) ? prevX : prevCurveC2X;\n        prevCurveC2Y = isNaN(prevCurveC2Y) ? prevY : prevCurveC2Y;\n        command.x1 = command.relative ? prevX - prevCurveC2X : 2 * prevX - prevCurveC2X;\n        command.y1 = command.relative ? prevY - prevCurveC2Y : 2 * prevY - prevCurveC2Y;\n      }\n      if (command.type & SVGPathData.CURVE_TO) {\n        prevCurveC2X = command.relative ? prevX + command.x2 : command.x2;\n        prevCurveC2Y = command.relative ? prevY + command.y2 : command.y2;\n      } else {\n        prevCurveC2X = NaN;\n        prevCurveC2Y = NaN;\n      }\n      if (command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        command.type = SVGPathData.QUAD_TO;\n        prevQuadCX = isNaN(prevQuadCX) ? prevX : prevQuadCX;\n        prevQuadCY = isNaN(prevQuadCY) ? prevY : prevQuadCY;\n        command.x1 = command.relative ? prevX - prevQuadCX : 2 * prevX - prevQuadCX;\n        command.y1 = command.relative ? prevY - prevQuadCY : 2 * prevY - prevQuadCY;\n      }\n      if (command.type & SVGPathData.QUAD_TO) {\n        prevQuadCX = command.relative ? prevX + command.x1 : command.x1;\n        prevQuadCY = command.relative ? prevY + command.y1 : command.y1;\n      } else {\n        prevQuadCX = NaN;\n        prevQuadCY = NaN;\n      }\n\n      return command;\n    });\n  }\n  /*\n   * A quadratic bézier curve can be represented by a cubic bézier curve which has\n   * the same end points as the quadratic and both control points in place of the\n   * quadratic\"s one.\n   *\n   * This transformer replaces QqTt commands with Cc commands respectively.\n   * This is useful for reading path data into a system which only has a\n   * representation for cubic curves.\n   */\n  export function QT_TO_C() {\n    let prevQuadX1 = NaN;\n    let prevQuadY1 = NaN;\n\n    return INFO((command, prevX, prevY) => {\n      if (command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        command.type = SVGPathData.QUAD_TO;\n        prevQuadX1 = isNaN(prevQuadX1) ? prevX : prevQuadX1;\n        prevQuadY1 = isNaN(prevQuadY1) ? prevY : prevQuadY1;\n        command.x1 = command.relative ? prevX - prevQuadX1 : 2 * prevX - prevQuadX1;\n        command.y1 = command.relative ? prevY - prevQuadY1 : 2 * prevY - prevQuadY1;\n      }\n      if (command.type & SVGPathData.QUAD_TO) {\n        prevQuadX1 = command.relative ? prevX + command.x1 : command.x1;\n        prevQuadY1 = command.relative ? prevY + command.y1 : command.y1;\n        const x1 = command.x1;\n        const y1 = command.y1;\n\n        command.type = SVGPathData.CURVE_TO;\n        command.x1 = ((command.relative ? 0 : prevX) + x1 * 2) / 3;\n        command.y1 = ((command.relative ? 0 : prevY) + y1 * 2) / 3;\n        command.x2 = (command.x + x1 * 2) / 3;\n        command.y2 = (command.y + y1 * 2) / 3;\n      } else {\n        prevQuadX1 = NaN;\n        prevQuadY1 = NaN;\n      }\n\n      return command;\n    });\n  }\n  export function INFO(\n    f: (command: any, prevXAbs: number, prevYAbs: number,\n        pathStartXAbs: number, pathStartYAbs: number) => any | any[]) {\n    let prevXAbs = 0;\n    let prevYAbs = 0;\n    let pathStartXAbs = NaN;\n    let pathStartYAbs = NaN;\n\n    return function transform(command: any) {\n      if (isNaN(pathStartXAbs) && !(command.type & SVGPathData.MOVE_TO)) {\n        throw new Error(\"path must start with moveto\");\n      }\n\n      const result = f(command, prevXAbs, prevYAbs, pathStartXAbs, pathStartYAbs);\n\n      if (command.type & SVGPathData.CLOSE_PATH) {\n        prevXAbs = pathStartXAbs;\n        prevYAbs = pathStartYAbs;\n      }\n\n      if (\"undefined\" !== typeof command.x) {\n        prevXAbs = (command.relative ? prevXAbs + command.x : command.x);\n      }\n      if (\"undefined\" !== typeof command.y) {\n        prevYAbs = (command.relative ? prevYAbs + command.y : command.y);\n      }\n\n      if (command.type & SVGPathData.MOVE_TO) {\n        pathStartXAbs = prevXAbs;\n        pathStartYAbs = prevYAbs;\n      }\n\n      return result;\n    };\n  }\n  /*\n   * remove 0-length segments\n   */\n  export function SANITIZE(EPS = 0) {\n    assertNumbers(EPS);\n    let prevCurveC2X = NaN;\n    let prevCurveC2Y = NaN;\n    let prevQuadCX = NaN;\n    let prevQuadCY = NaN;\n\n    return INFO((command, prevX, prevY, pathStartX, pathStartY) => {\n      const abs = Math.abs;\n      let skip = false;\n      let x1Rel = 0;\n      let y1Rel = 0;\n\n      if (command.type & SVGPathData.SMOOTH_CURVE_TO) {\n        x1Rel = isNaN(prevCurveC2X) ? 0 : prevX - prevCurveC2X;\n        y1Rel = isNaN(prevCurveC2Y) ? 0 : prevY - prevCurveC2Y;\n      }\n      if (command.type & (SVGPathData.CURVE_TO | SVGPathData.SMOOTH_CURVE_TO)) {\n        prevCurveC2X = command.relative ? prevX + command.x2 : command.x2;\n        prevCurveC2Y = command.relative ? prevY + command.y2 : command.y2;\n      } else {\n        prevCurveC2X = NaN;\n        prevCurveC2Y = NaN;\n      }\n      if (command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        prevQuadCX = isNaN(prevQuadCX) ? prevX : 2 * prevX - prevQuadCX;\n        prevQuadCY = isNaN(prevQuadCY) ? prevY : 2 * prevY - prevQuadCY;\n      } else if (command.type & SVGPathData.QUAD_TO) {\n        prevQuadCX = command.relative ? prevX + command.x1 : command.x1;\n        prevQuadCY = command.relative ? prevY + command.y1 : command.y2;\n      } else {\n        prevQuadCX = NaN;\n        prevQuadCY = NaN;\n      }\n\n      if (command.type & SVGPathData.LINE_COMMANDS ||\n        command.type & SVGPathData.ARC && (0 === command.rX || 0 === command.rY || !command.lArcFlag) ||\n        command.type & SVGPathData.CURVE_TO || command.type & SVGPathData.SMOOTH_CURVE_TO ||\n        command.type & SVGPathData.QUAD_TO || command.type & SVGPathData.SMOOTH_QUAD_TO) {\n        const xRel = \"undefined\" === typeof command.x ? 0 :\n          (command.relative ? command.x : command.x - prevX);\n        const yRel = \"undefined\" === typeof command.y ? 0 :\n          (command.relative ? command.y : command.y - prevY);\n\n        x1Rel = !isNaN(prevQuadCX) ? prevQuadCX - prevX :\n          \"undefined\" === typeof command.x1 ? x1Rel :\n            command.relative ? command.x :\n              command.x1 - prevX;\n        y1Rel = !isNaN(prevQuadCY) ? prevQuadCY - prevY :\n          \"undefined\" === typeof command.y1 ? y1Rel :\n            command.relative ? command.y :\n              command.y1 - prevY;\n\n        const x2Rel = \"undefined\" === typeof command.x2 ? 0 :\n          (command.relative ? command.x : command.x2 - prevX);\n        const y2Rel = \"undefined\" === typeof command.y2 ? 0 :\n          (command.relative ? command.y : command.y2 - prevY);\n\n        if (abs(xRel) <= EPS && abs(yRel) <= EPS &&\n          abs(x1Rel) <= EPS && abs(y1Rel) <= EPS &&\n          abs(x2Rel) <= EPS && abs(y2Rel) <= EPS) {\n          skip = true;\n        }\n      }\n\n      if (command.type & SVGPathData.CLOSE_PATH) {\n        if (abs(prevX - pathStartX) <= EPS && abs(prevY - pathStartY) <= EPS) {\n          skip = true;\n        }\n      }\n\n      return skip ? [] : command;\n    });\n  }\n  // SVG Transforms : http://www.w3.org/TR/SVGTiny12/coords.html#TransformList\n  // Matrix : http://apike.ca/prog_svg_transform.html\n  // a c e\n  // b d f\n  export function MATRIX(a: number, b: number, c: number, d: number, e: number, f: number) {\n    assertNumbers(a, b, c, d, e, f);\n\n    return INFO((command, prevX, prevY, pathStartX) => {\n      const origX1 = command.x1;\n      const origX2 = command.x2;\n      // if isNaN(pathStartX), then this is the first command, which is ALWAYS an\n      // absolute MOVE_TO, regardless what the relative flag says\n      const comRel = command.relative && !isNaN(pathStartX);\n      const x = \"undefined\" !== typeof command.x ? command.x : (comRel ? 0 : prevX);\n      const y = \"undefined\" !== typeof command.y ? command.y : (comRel ? 0 : prevY);\n\n      if (command.type & SVGPathData.HORIZ_LINE_TO && 0 !== b) {\n        command.type = SVGPathData.LINE_TO;\n        command.y = command.relative ? 0 : prevY;\n      }\n      if (command.type & SVGPathData.VERT_LINE_TO && 0 !== c) {\n        command.type = SVGPathData.LINE_TO;\n        command.x = command.relative ? 0 : prevX;\n      }\n\n      if (\"undefined\" !== typeof command.x) {\n        command.x = (command.x * a) + (y * c) + (comRel ? 0 : e);\n      }\n      if (\"undefined\" !== typeof command.y) {\n        command.y = (x * b) + command.y * d + (comRel ? 0 : f);\n      }\n      if (\"undefined\" !== typeof command.x1) {\n        command.x1 = command.x1 * a + command.y1 * c + (comRel ? 0 : e);\n      }\n      if (\"undefined\" !== typeof command.y1) {\n        command.y1 = origX1 * b + command.y1 * d + (comRel ? 0 : f);\n      }\n      if (\"undefined\" !== typeof command.x2) {\n        command.x2 = command.x2 * a + command.y2 * c + (comRel ? 0 : e);\n      }\n      if (\"undefined\" !== typeof command.y2) {\n        command.y2 = origX2 * b + command.y2 * d + (comRel ? 0 : f);\n      }\n      function sqr(x: number) { return x * x; }\n      const det = a * d - b * c;\n\n      if (\"undefined\" !== typeof command.xRot) {\n        // Skip if this is a pure translation\n        if (1 !== a || 0 !== b || 0 !== c || 1 !== d) {\n          // Special case for singular matrix\n          if (0 === det) {\n            // In the singular case, the arc is compressed to a line. The actual geometric image of the original\n            // curve under this transform possibly extends beyond the starting and/or ending points of the segment, but\n            // for simplicity we ignore this detail and just replace this command with a single line segment.\n            delete command.rX;\n            delete command.rY;\n            delete command.xRot;\n            delete command.lArcFlag;\n            delete command.sweepFlag;\n            command.type = SVGPathData.LINE_TO;\n          } else {\n            // Convert to radians\n            const xRot = command.xRot * Math.PI / 180;\n\n            // Convert rotated ellipse to general conic form\n            // x0^2/rX^2 + y0^2/rY^2 - 1 = 0\n            // x0 = x*cos(xRot) + y*sin(xRot)\n            // y0 = -x*sin(xRot) + y*cos(xRot)\n            // --> A*x^2 + B*x*y + C*y^2 - 1 = 0, where\n            const sinRot = Math.sin(xRot);\n            const cosRot = Math.cos(xRot);\n            const xCurve = 1 / sqr(command.rX);\n            const yCurve = 1 / sqr(command.rY);\n            const A = sqr(cosRot) * xCurve + sqr(sinRot) * yCurve;\n            const B = 2 * sinRot * cosRot * (xCurve - yCurve);\n            const C = sqr(sinRot) * xCurve + sqr(cosRot) * yCurve;\n\n            // Apply matrix to A*x^2 + B*x*y + C*y^2 - 1 = 0\n            // x1 = a*x + c*y\n            // y1 = b*x + d*y\n            //      (we can ignore e and f, since pure translations don\"t affect the shape of the ellipse)\n            // --> A1*x1^2 + B1*x1*y1 + C1*y1^2 - det^2 = 0, where\n            const A1 = A * d * d - B * b * d + C * b * b;\n            const B1 = B * (a * d + b * c) - 2 * (A * c * d + C * a * b);\n            const C1 = A * c * c - B * a * c + C * a * a;\n\n            // Unapply newXRot to get back to axis-aligned ellipse equation\n            // x1 = x2*cos(newXRot) - y2*sin(newXRot)\n            // y1 = x2*sin(newXRot) + y2*cos(newXRot)\n            // A1*x1^2 + B1*x1*y1 + C1*y1^2 - det^2 =\n            //   x2^2*(A1*cos(newXRot)^2 + B1*sin(newXRot)*cos(newXRot) + C1*sin(newXRot)^2)\n            //   + x2*y2*(2*(C1 - A1)*sin(newXRot)*cos(newXRot) + B1*(cos(newXRot)^2 - sin(newXRot)^2))\n            //   + y2^2*(A1*sin(newXRot)^2 - B1*sin(newXRot)*cos(newXRot) + C1*cos(newXRot)^2)\n            //   (which must have the same zeroes as)\n            // x2^2/newRX^2 + y2^2/newRY^2 - 1\n            //   (so we have)\n            // 2*(C1 - A1)*sin(newXRot)*cos(newXRot) + B1*(cos(newXRot)^2 - sin(newXRot)^2) = 0\n            // (A1 - C1)*sin(2*newXRot) = B1*cos(2*newXRot)\n            // 2*newXRot = atan2(B1, A1 - C1)\n            const newXRot = ((Math.atan2(B1, A1 - C1) + Math.PI) % Math.PI) / 2;\n            // For any integer n, (atan2(B1, A1 - C1) + n*pi)/2 is a solution to the above; incrementing n just swaps\n            // the x and y radii computed below (since that\"s what rotating an ellipse by pi/2 does).  Choosing the\n            // rotation between 0 and pi/2 eliminates the ambiguity and leads to more predictable output.\n\n            // Finally, we get newRX and newRY from the same-zeroes relationship that gave us newXRot\n            const newSinRot = Math.sin(newXRot);\n            const newCosRot = Math.cos(newXRot);\n\n            command.rX = Math.abs(det) /\n              Math.sqrt(A1 * sqr(newCosRot) + B1 * newSinRot * newCosRot + C1 * sqr(newSinRot));\n            command.rY = Math.abs(det) /\n              Math.sqrt(A1 * sqr(newSinRot) - B1 * newSinRot * newCosRot + C1 * sqr(newCosRot));\n            command.xRot = newXRot * 180 / Math.PI;\n          }\n        }\n      }\n      // sweepFlag needs to be inverted when mirroring shapes\n      // see http://www.itk.ilstu.edu/faculty/javila/SVG/SVG_drawing1/elliptical_curve.htm\n      // m 65,10 a 50,25 0 1 0 50,25\n      // M 65,60 A 50,25 0 1 1 115,35\n      if (\"undefined\" !== typeof command.sweepFlag && 0 > det) {\n        command.sweepFlag = +!command.sweepFlag;\n      }\n      return command;\n    });\n  }\n  export function ROTATE(a: number, x = 0, y = 0) {\n    assertNumbers(a, x, y);\n    const sin = Math.sin(a);\n    const cos = Math.cos(a);\n\n    return MATRIX(cos, sin, -sin, cos, x - x * cos + y * sin, y - x * sin - y * cos);\n  }\n  export function TRANSLATE(dX: number, dY = 0) {\n    assertNumbers(dX, dY);\n    return MATRIX(1, 0, 0, 1, dX, dY);\n  }\n  export function SCALE(dX: number, dY = dX) {\n    assertNumbers(dX, dY);\n    return MATRIX(dX, 0, 0, dY, 0, 0);\n  }\n  export function SKEW_X(a: number) {\n    assertNumbers(a);\n    return MATRIX(1, 0, Math.atan(a), 1, 0, 0);\n  }\n  export function SKEW_Y(a: number) {\n    assertNumbers(a);\n    return MATRIX(1, Math.atan(a), 0, 1, 0, 0);\n  }\n  export function X_AXIS_SYMMETRY(xOffset = 0) {\n    assertNumbers(xOffset);\n    return MATRIX(-1, 0, 0, 1, xOffset, 0);\n  }\n  export function Y_AXIS_SYMMETRY(yOffset = 0) {\n    assertNumbers(yOffset);\n    return MATRIX(1, 0, 0, -1, 0, yOffset);\n  }\n  // Convert arc commands to curve commands\n  export function A_TO_C() {\n    return INFO((command, prevX, prevY) => {\n      if (SVGPathData.ARC === command.type) {\n        return a2c(command, command.relative ? 0 : prevX, command.relative ? 0 : prevY);\n      }\n      return command;\n    });\n  }\n  // @see annotateArcCommand\n  export function ANNOTATE_ARCS() {\n    return INFO((c, x1, y1) => {\n      if (c.relative) {\n        x1 = 0;\n        y1 = 0;\n      }\n      if (SVGPathData.ARC === c.type) {\n        annotateArcCommand(c, x1, y1);\n      }\n      return c;\n    });\n  }\n  export function CLONE() {\n    return (c: SVGCommand) => {\n      const result = {} as SVGCommand;\n      // tslint:disable-next-line\n      for (const key in c) {\n        result[key as keyof SVGCommand] = c[key as keyof SVGCommand];\n      }\n      return result;\n    };\n  }\n  // @see annotateArcCommand\n  export function CALCULATE_BOUNDS() {\n    const clone = CLONE();\n    const toAbs = TO_ABS();\n    const qtToC = QT_TO_C();\n    const normST = NORMALIZE_ST();\n    const f: TransformFunction & {minX: number, maxX: number, minY: number, maxY: number} =\n        INFO((command, prevXAbs, prevYAbs) => {\n      const c = normST(qtToC(toAbs(clone(command))));\n      function fixX(absX: number) {\n        if (absX > f.maxX) { f.maxX = absX; }\n        if (absX < f.minX) { f.minX = absX; }\n      }\n      function fixY(absY: number) {\n        if (absY > f.maxY) { f.maxY = absY; }\n        if (absY < f.minY) { f.minY = absY; }\n      }\n      if (c.type & SVGPathData.DRAWING_COMMANDS) {\n        fixX(prevXAbs);\n        fixY(prevYAbs);\n      }\n      if (c.type & SVGPathData.HORIZ_LINE_TO) {\n        fixX(c.x);\n      }\n      if (c.type & SVGPathData.VERT_LINE_TO) {\n        fixY(c.y);\n      }\n      if (c.type & SVGPathData.LINE_TO) {\n        fixX(c.x);\n        fixY(c.y);\n      }\n      if (c.type & SVGPathData.CURVE_TO) {\n        // add start and end points\n        fixX(c.x);\n        fixY(c.y);\n        const xDerivRoots = bezierRoot(prevXAbs, c.x1, c.x2, c.x);\n\n        for (const derivRoot of xDerivRoots) {\n          if (0 < derivRoot && 1 > derivRoot) {\n            fixX(bezierAt(prevXAbs, c.x1, c.x2, c.x, derivRoot));\n          }\n        }\n        const yDerivRoots = bezierRoot(prevYAbs, c.y1, c.y2, c.y);\n\n        for (const derivRoot of yDerivRoots) {\n          if (0 < derivRoot && 1 > derivRoot) {\n            fixY(bezierAt(prevYAbs, c.y1, c.y2, c.y, derivRoot));\n          }\n        }\n      }\n      if (c.type & SVGPathData.ARC) {\n        // add start and end points\n        fixX(c.x);\n        fixY(c.y);\n        annotateArcCommand(c, prevXAbs, prevYAbs);\n        // p = cos(phi) * xv + sin(phi) * yv\n        // dp = -sin(phi) * xv + cos(phi) * yv = 0\n        const xRotRad = c.xRot / 180 * Math.PI;\n        // points on ellipse for phi = 0° and phi = 90°\n        const x0 = Math.cos(xRotRad) * c.rX;\n        const y0 = Math.sin(xRotRad) * c.rX;\n        const x90 = -Math.sin(xRotRad) * c.rY;\n        const y90 = Math.cos(xRotRad) * c.rY;\n\n        // annotateArcCommand returns phi1 and phi2 such that -180° < phi1 < 180° and phi2 is smaller or greater\n        // depending on the sweep flag. Calculate phiMin, phiMax such that -180° < phiMin < 180° and phiMin < phiMax\n        const [phiMin, phiMax] = c.phi1 < c.phi2 ?\n          [c.phi1, c.phi2] :\n          (-180 > c.phi2 ? [c.phi2 + 360, c.phi1 + 360] : [c.phi2, c.phi1]);\n        const normalizeXiEta = ([xi, eta]: [number, number]) => {\n          const phiRad = Math.atan2(eta, xi);\n          const phi = phiRad * 180 / Math.PI;\n\n          return phi < phiMin ? phi + 360 : phi;\n        };\n        // xi = cos(phi), eta = sin(phi)\n\n        const xDerivRoots = intersectionUnitCircleLine(x90, -x0, 0).map(normalizeXiEta);\n        for (const derivRoot of xDerivRoots) {\n          if (derivRoot > phiMin && derivRoot < phiMax) {\n            fixX(arcAt(c.cX, x0, x90, derivRoot));\n          }\n        }\n\n        const yDerivRoots = intersectionUnitCircleLine(y90, -y0, 0).map(normalizeXiEta);\n        for (const derivRoot of yDerivRoots) {\n          if (derivRoot > phiMin && derivRoot < phiMax) {\n            fixY(arcAt(c.cY, y0, y90, derivRoot));\n          }\n        }\n      }\n      return command;\n    }) as any;\n\n    f.minX = Infinity;\n    f.maxX = -Infinity;\n    f.minY = Infinity;\n    f.maxY = -Infinity;\n    return f;\n  }\n}\n", "import { SVGPathDataTransformer } from \"./SVGPathDataTransformer\";\nimport { TransformFunction } from \"./types\";\n\nexport abstract class TransformableSVG {\n  round(x?: number) {\n    return this.transform(SVGPathDataTransformer.ROUND(x));\n  }\n\n  toAbs() {\n    return this.transform(SVGPathDataTransformer.TO_ABS());\n  }\n\n  toRel() {\n    return this.transform(SVGPathDataTransformer.TO_REL());\n  }\n\n  normalizeHVZ(a?: boolean, b?: boolean, c?: boolean) {\n    return this.transform(SVGPathDataTransformer.NORMALIZE_HVZ(a, b, c));\n  }\n\n  normalizeST() {\n    return this.transform(SVGPathDataTransformer.NORMALIZE_ST());\n  }\n\n  qtToC() {\n    return this.transform(SVGPathDataTransformer.QT_TO_C());\n  }\n\n  aToC() {\n    return this.transform(SVGPathDataTransformer.A_TO_C());\n  }\n\n  sanitize(eps?: number) {\n    return this.transform(SVGPathDataTransformer.SANITIZE(eps));\n  }\n\n  translate(x: number, y?: number) {\n    return this.transform(SVGPathDataTransformer.TRANSLATE(x, y));\n  }\n\n  scale(x: number, y?: number) {\n    return this.transform(SVGPathDataTransformer.SCALE(x, y));\n  }\n\n  rotate(a: number, x?: number, y?: number) {\n    return this.transform(SVGPathDataTransformer.ROTATE(a, x, y));\n  }\n\n  matrix(a: number, b: number, c: number, d: number, e: number, f: number) {\n    return this.transform(SVGPathDataTransformer.MATRIX(a, b, c, d, e, f));\n  }\n\n  skewX(a: number) {\n    return this.transform(SVGPathDataTransformer.SKEW_X(a));\n  }\n\n  skewY(a: number) {\n    return this.transform(SVGPathDataTransformer.SKEW_Y(a));\n  }\n\n  xSymmetry(xOffset?: number) {\n    return this.transform(SVGPathDataTransformer.X_AXIS_SYMMETRY(xOffset));\n  }\n\n  ySymmetry(yOffset?: number) {\n    return this.transform(SVGPathDataTransformer.Y_AXIS_SYMMETRY(yOffset));\n  }\n\n  annotateArcs() {\n    return this.transform(SVGPathDataTransformer.ANNOTATE_ARCS());\n  }\n\n  abstract transform(transformFunction: TransformFunction): this;\n}\n", "// Parse SVG PathData\n// http://www.w3.org/TR/SVG/paths.html#PathDataBNF\nimport { COMMAND_ARG_COUNTS, SVGPathData } from \"./SVGPathData\";\nimport { TransformableSVG } from \"./TransformableSVG\";\nimport { SVGCommand, TransformFunction } from \"./types\";\n// Private consts : Char groups\nconst isWhiteSpace = (c: string) =>\n  \" \" === c || \"\\t\" === c || \"\\r\" === c || \"\\n\" === c;\nconst isDigit = (c: string) =>\n  \"0\".charCodeAt(0) <= c.charCodeAt(0) && c.charCodeAt(0) <= \"9\".charCodeAt(0);\nconst COMMANDS = \"mMzZlLhHvVcCsSqQtTaA\";\n\nexport class SVGPathDataParser extends TransformableSVG {\n  private curNumber: string = \"\";\n  private curCommandType: SVGCommand[\"type\"] | -1 = -1;\n  private curCommandRelative = false;\n  private canParseCommandOrComma = true;\n  private curNumberHasExp = false;\n  private curNumberHasExpDigits = false;\n  private curNumberHasDecimal = false;\n  private curArgs: number[] = [];\n\n  constructor() {\n    super();\n  }\n\n  finish(commands: SVGCommand[] = []) {\n    this.parse(\" \", commands);\n    // Adding residual command\n    if (0 !== this.curArgs.length || !this.canParseCommandOrComma) {\n      throw new SyntaxError(\"Unterminated command at the path end.\");\n    }\n    return commands;\n  }\n\n  parse(str: string, commands: SVGCommand[] = []) {\n    const finishCommand = (command: SVGCommand) => {\n      commands.push(command);\n      this.curArgs.length = 0;\n      this.canParseCommandOrComma = true;\n    };\n\n    for (let i = 0; i < str.length; i++) {\n      const c = str[i];\n      // White spaces parsing\n      const isAArcFlag = this.curCommandType === SVGPathData.ARC &&\n        (this.curArgs.length === 3 || this.curArgs.length === 4) &&\n        this.curNumber.length === 1 &&\n        (this.curNumber === \"0\" || this.curNumber === \"1\");\n      const isEndingDigit = isDigit(c) && (\n        (this.curNumber === \"0\" && c === \"0\") ||\n        isAArcFlag\n      );\n\n      if (\n        isDigit(c) &&\n        !isEndingDigit\n      ) {\n        this.curNumber += c;\n        this.curNumberHasExpDigits = this.curNumberHasExp;\n        continue;\n      }\n      if (\"e\" === c || \"E\" === c) {\n        this.curNumber += c;\n        this.curNumberHasExp = true;\n        continue;\n      }\n      if (\n        (\"-\" === c || \"+\" === c) &&\n        this.curNumberHasExp &&\n        !this.curNumberHasExpDigits\n      ) {\n        this.curNumber += c;\n        continue;\n      }\n      // if we already have a \".\", it means we are starting a new number\n      if (\".\" === c && !this.curNumberHasExp && !this.curNumberHasDecimal && !isAArcFlag) {\n        this.curNumber += c;\n        this.curNumberHasDecimal = true;\n        continue;\n      }\n\n      // New number\n      if (this.curNumber && -1 !== this.curCommandType) {\n        const val = Number(this.curNumber);\n        if (isNaN(val)) {\n          throw new SyntaxError(`Invalid number ending at ${i}`);\n        }\n        if (this.curCommandType === SVGPathData.ARC) {\n          if (0 === this.curArgs.length || 1 === this.curArgs.length) {\n            if (0 > val) {\n              throw new SyntaxError(\n                `Expected positive number, got \"${val}\" at index \"${i}\"`,\n              );\n            }\n          } else if (3 === this.curArgs.length || 4 === this.curArgs.length) {\n            if (\"0\" !== this.curNumber && \"1\" !== this.curNumber) {\n              throw new SyntaxError(\n                `Expected a flag, got \"${this.curNumber}\" at index \"${i}\"`,\n              );\n            }\n          }\n        }\n        this.curArgs.push(val);\n        if (this.curArgs.length === COMMAND_ARG_COUNTS[this.curCommandType]) {\n          if (SVGPathData.HORIZ_LINE_TO === this.curCommandType) {\n            finishCommand({\n              type: SVGPathData.HORIZ_LINE_TO,\n              relative: this.curCommandRelative,\n              x: val,\n            });\n          } else if (SVGPathData.VERT_LINE_TO === this.curCommandType) {\n            finishCommand({\n              type: SVGPathData.VERT_LINE_TO,\n              relative: this.curCommandRelative,\n              y: val,\n            });\n            // Move to / line to / smooth quadratic curve to commands (x, y)\n          } else if (\n            this.curCommandType === SVGPathData.MOVE_TO ||\n            this.curCommandType === SVGPathData.LINE_TO ||\n            this.curCommandType === SVGPathData.SMOOTH_QUAD_TO\n          ) {\n            finishCommand({\n              type: this.curCommandType,\n              relative: this.curCommandRelative,\n              x: this.curArgs[0],\n              y: this.curArgs[1],\n            } as SVGCommand);\n            // Switch to line to state\n            if (SVGPathData.MOVE_TO === this.curCommandType) {\n              this.curCommandType = SVGPathData.LINE_TO;\n            }\n          } else if (this.curCommandType === SVGPathData.CURVE_TO) {\n            finishCommand({\n              type: SVGPathData.CURVE_TO,\n              relative: this.curCommandRelative,\n              x1: this.curArgs[0],\n              y1: this.curArgs[1],\n              x2: this.curArgs[2],\n              y2: this.curArgs[3],\n              x: this.curArgs[4],\n              y: this.curArgs[5],\n            });\n          } else if (this.curCommandType === SVGPathData.SMOOTH_CURVE_TO) {\n            finishCommand({\n              type: SVGPathData.SMOOTH_CURVE_TO,\n              relative: this.curCommandRelative,\n              x2: this.curArgs[0],\n              y2: this.curArgs[1],\n              x: this.curArgs[2],\n              y: this.curArgs[3],\n            });\n          } else if (this.curCommandType === SVGPathData.QUAD_TO) {\n            finishCommand({\n              type: SVGPathData.QUAD_TO,\n              relative: this.curCommandRelative,\n              x1: this.curArgs[0],\n              y1: this.curArgs[1],\n              x: this.curArgs[2],\n              y: this.curArgs[3],\n            });\n          } else if (this.curCommandType === SVGPathData.ARC) {\n            finishCommand({\n              type: SVGPathData.ARC,\n              relative: this.curCommandRelative,\n              rX: this.curArgs[0],\n              rY: this.curArgs[1],\n              xRot: this.curArgs[2],\n              lArcFlag: this.curArgs[3] as 0 | 1,\n              sweepFlag: this.curArgs[4] as 0 | 1,\n              x: this.curArgs[5],\n              y: this.curArgs[6],\n            });\n          }\n        }\n        this.curNumber = \"\";\n        this.curNumberHasExpDigits = false;\n        this.curNumberHasExp = false;\n        this.curNumberHasDecimal = false;\n        this.canParseCommandOrComma = true;\n      }\n      // Continue if a white space or a comma was detected\n      if (isWhiteSpace(c)) {\n        continue;\n      }\n      if (\",\" === c && this.canParseCommandOrComma) {\n        // L 0,0, H is not valid:\n        this.canParseCommandOrComma = false;\n        continue;\n      }\n      // if a sign is detected, then parse the new number\n      if (\"+\" === c || \"-\" === c || \".\" === c) {\n        this.curNumber = c;\n        this.curNumberHasDecimal = \".\" === c;\n        continue;\n      }\n      // if a 0 is detected, then parse the new number\n      if (isEndingDigit) {\n        this.curNumber = c;\n        this.curNumberHasDecimal = false;\n        continue;\n      }\n\n      // Adding residual command\n      if (0 !== this.curArgs.length) {\n        throw new SyntaxError(`Unterminated command at index ${i}.`);\n      }\n      if (!this.canParseCommandOrComma) {\n        throw new SyntaxError(\n          `Unexpected character \"${c}\" at index ${i}. Command cannot follow comma`,\n        );\n      }\n      this.canParseCommandOrComma = false;\n      // Detecting the next command\n      if (\"z\" === c || \"Z\" === c) {\n        commands.push({\n          type: SVGPathData.CLOSE_PATH,\n        });\n        this.canParseCommandOrComma = true;\n        this.curCommandType = -1;\n        continue;\n        // Horizontal move to command\n      } else if (\"h\" === c || \"H\" === c) {\n        this.curCommandType = SVGPathData.HORIZ_LINE_TO;\n        this.curCommandRelative = \"h\" === c;\n        // Vertical move to command\n      } else if (\"v\" === c || \"V\" === c) {\n        this.curCommandType = SVGPathData.VERT_LINE_TO;\n        this.curCommandRelative = \"v\" === c;\n        // Move to command\n      } else if (\"m\" === c || \"M\" === c) {\n        this.curCommandType = SVGPathData.MOVE_TO;\n        this.curCommandRelative = \"m\" === c;\n        // Line to command\n      } else if (\"l\" === c || \"L\" === c) {\n        this.curCommandType = SVGPathData.LINE_TO;\n        this.curCommandRelative = \"l\" === c;\n        // Curve to command\n      } else if (\"c\" === c || \"C\" === c) {\n        this.curCommandType = SVGPathData.CURVE_TO;\n        this.curCommandRelative = \"c\" === c;\n        // Smooth curve to command\n      } else if (\"s\" === c || \"S\" === c) {\n        this.curCommandType = SVGPathData.SMOOTH_CURVE_TO;\n        this.curCommandRelative = \"s\" === c;\n        // Quadratic bezier curve to command\n      } else if (\"q\" === c || \"Q\" === c) {\n        this.curCommandType = SVGPathData.QUAD_TO;\n        this.curCommandRelative = \"q\" === c;\n        // Smooth quadratic bezier curve to command\n      } else if (\"t\" === c || \"T\" === c) {\n        this.curCommandType = SVGPathData.SMOOTH_QUAD_TO;\n        this.curCommandRelative = \"t\" === c;\n        // Elliptic arc command\n      } else if (\"a\" === c || \"A\" === c) {\n        this.curCommandType = SVGPathData.ARC;\n        this.curCommandRelative = \"a\" === c;\n      } else {\n        throw new SyntaxError(`Unexpected character \"${c}\" at index ${i}.`);\n      }\n    }\n    return commands;\n  }\n  /**\n   * Return a wrapper around this parser which applies the transformation on parsed commands.\n   */\n  transform(transform: TransformFunction) {\n    const result = Object.create(this, {\n      parse: {\n        value(chunk: string, commands: SVGCommand[] = []) {\n          const parsedCommands = Object.getPrototypeOf(this).parse.call(\n            this,\n            chunk,\n          );\n          for (const c of parsedCommands) {\n            const cT = transform(c);\n            if (Array.isArray(cT)) {\n              commands.push(...cT);\n            } else {\n              commands.push(cT);\n            }\n          }\n          return commands;\n        },\n      },\n    });\n    return result as this;\n  }\n}\n", "import { encodeSV<PERSON>ath } from \"./SVGPathDataEncoder\";\nimport { SVGPathDataParser } from \"./SVGPathDataParser\";\nimport { SVGPathDataTransformer } from \"./SVGPathDataTransformer\";\nimport { TransformableSVG } from \"./TransformableSVG\";\nimport { SVGCommand } from \"./types\";\n\nexport class SVGPathData extends TransformableSVG {\n  commands: SVGCommand[];\n  constructor(content: string | SVGCommand[]) {\n    super();\n    if (\"string\" === typeof content) {\n      this.commands = SVGPathData.parse(content);\n    } else {\n      this.commands = content;\n    }\n  }\n\n  encode() {\n    return SVGPathData.encode(this.commands);\n  }\n\n  getBounds() {\n    const boundsTransform = SVGPathDataTransformer.CALCULATE_BOUNDS();\n\n    this.transform(boundsTransform);\n    return boundsTransform;\n  }\n\n  transform(\n    transformFunction: (input: SVGCommand) => SVGCommand | SVGCommand[],\n  ) {\n    const newCommands = [];\n\n    for (const command of this.commands) {\n      const transformedCommand = transformFunction(command);\n\n      if (Array.isArray(transformedCommand)) {\n        newCommands.push(...transformedCommand);\n      } else {\n        newCommands.push(transformedCommand);\n      }\n    }\n    this.commands = newCommands;\n    return this;\n  }\n\n  static encode(commands: SVGCommand[]) {\n    return encodeSVGPath(commands);\n      }\n\n  static parse(path: string) {\n    const parser = new SVGPathDataParser();\n    const commands: SVGCommand[] = [];\n    parser.parse(path, commands);\n    parser.finish(commands);\n    return commands;\n  }\n\n  static readonly CLOSE_PATH: 1 = 1;\n  static readonly MOVE_TO: 2 = 2;\n  static readonly HORIZ_LINE_TO: 4 = 4;\n  static readonly VERT_LINE_TO: 8 = 8;\n  static readonly LINE_TO: 16 = 16;\n  static readonly CURVE_TO: 32 = 32;\n  static readonly SMOOTH_CURVE_TO: 64 = 64;\n  static readonly QUAD_TO: 128 = 128;\n  static readonly SMOOTH_QUAD_TO: 256 = 256;\n  static readonly ARC: 512 = 512;\n  static readonly LINE_COMMANDS = SVGPathData.LINE_TO | SVGPathData.HORIZ_LINE_TO | SVGPathData.VERT_LINE_TO;\n  static readonly DRAWING_COMMANDS = SVGPathData.HORIZ_LINE_TO | SVGPathData.VERT_LINE_TO | SVGPathData.LINE_TO |\n  SVGPathData.CURVE_TO | SVGPathData.SMOOTH_CURVE_TO | SVGPathData.QUAD_TO |\n  SVGPathData.SMOOTH_QUAD_TO | SVGPathData.ARC;\n}\n\nexport const COMMAND_ARG_COUNTS = {\n    [SVGPathData.MOVE_TO]: 2,\n    [SVGPathData.LINE_TO]: 2,\n    [SVGPathData.HORIZ_LINE_TO]: 1,\n    [SVGPathData.VERT_LINE_TO]: 1,\n    [SVGPathData.CLOSE_PATH]: 0,\n    [SVGPathData.QUAD_TO]: 4,\n    [SVGPathData.SMOOTH_QUAD_TO]: 2,\n    [SVGPathData.CURVE_TO]: 6,\n    [SVGPathData.SMOOTH_CURVE_TO]: 4,\n    [SVGPathData.ARC]: 7,\n};\n\nexport {encodeSVGPath} from \"./SVGPathDataEncoder\";\nexport {SVGPathDataParser} from \"./SVGPathDataParser\";\nexport {SVGPathDataTransformer} from \"./SVGPathDataTransformer\";\n", "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n/* eslint-disable no-bitwise -- used for calculations */\n\n/* eslint-disable unicorn/prefer-query-selector -- aiming at\n  backward-compatibility */\n\n/**\n* StackBlur - a fast almost Gaussian Blur For Canvas\n*\n* In case you find this class useful - especially in commercial projects -\n* I am not totally unhappy for a small donation to my PayPal account\n* <EMAIL>\n*\n* Or support me on flattr:\n* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}.\n*\n* @module StackBlur\n* <AUTHOR> Klingemann\n* Contact: <EMAIL>\n* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}\n* Twitter: @quasimondo\n*\n* @copyright (c) 2010 Mario Klingemann\n*\n* Permission is hereby granted, free of charge, to any person\n* obtaining a copy of this software and associated documentation\n* files (the \"Software\"), to deal in the Software without\n* restriction, including without limitation the rights to use,\n* copy, modify, merge, publish, distribute, sublicense, and/or sell\n* copies of the Software, and to permit persons to whom the\n* Software is furnished to do so, subject to the following\n* conditions:\n*\n* The above copyright notice and this permission notice shall be\n* included in all copies or substantial portions of the Software.\n*\n* THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n* OTHER DEALINGS IN THE SOFTWARE.\n*/\nvar mulTable = [512, 512, 456, 512, 328, 456, 335, 512, 405, 328, 271, 456, 388, 335, 292, 512, 454, 405, 364, 328, 298, 271, 496, 456, 420, 388, 360, 335, 312, 292, 273, 512, 482, 454, 428, 405, 383, 364, 345, 328, 312, 298, 284, 271, 259, 496, 475, 456, 437, 420, 404, 388, 374, 360, 347, 335, 323, 312, 302, 292, 282, 273, 265, 512, 497, 482, 468, 454, 441, 428, 417, 405, 394, 383, 373, 364, 354, 345, 337, 328, 320, 312, 305, 298, 291, 284, 278, 271, 265, 259, 507, 496, 485, 475, 465, 456, 446, 437, 428, 420, 412, 404, 396, 388, 381, 374, 367, 360, 354, 347, 341, 335, 329, 323, 318, 312, 307, 302, 297, 292, 287, 282, 278, 273, 269, 265, 261, 512, 505, 497, 489, 482, 475, 468, 461, 454, 447, 441, 435, 428, 422, 417, 411, 405, 399, 394, 389, 383, 378, 373, 368, 364, 359, 354, 350, 345, 341, 337, 332, 328, 324, 320, 316, 312, 309, 305, 301, 298, 294, 291, 287, 284, 281, 278, 274, 271, 268, 265, 262, 259, 257, 507, 501, 496, 491, 485, 480, 475, 470, 465, 460, 456, 451, 446, 442, 437, 433, 428, 424, 420, 416, 412, 408, 404, 400, 396, 392, 388, 385, 381, 377, 374, 370, 367, 363, 360, 357, 354, 350, 347, 344, 341, 338, 335, 332, 329, 326, 323, 320, 318, 315, 312, 310, 307, 304, 302, 299, 297, 294, 292, 289, 287, 285, 282, 280, 278, 275, 273, 271, 269, 267, 265, 263, 261, 259];\nvar shgTable = [9, 11, 12, 13, 13, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 17, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24];\n/**\n * @param {string|HTMLImageElement} img\n * @param {string|HTMLCanvasElement} canvas\n * @param {Float} radius\n * @param {boolean} blurAlphaChannel\n * @param {boolean} useOffset\n * @param {boolean} skipStyles\n * @returns {undefined}\n */\n\nfunction processImage(img, canvas, radius, blurAlphaChannel, useOffset, skipStyles) {\n  if (typeof img === 'string') {\n    img = document.getElementById(img);\n  }\n\n  if (!img || Object.prototype.toString.call(img).slice(8, -1) === 'HTMLImageElement' && !('naturalWidth' in img)) {\n    return;\n  }\n\n  var dimensionType = useOffset ? 'offset' : 'natural';\n  var w = img[dimensionType + 'Width'];\n  var h = img[dimensionType + 'Height']; // add ImageBitmap support,can blur texture source\n\n  if (Object.prototype.toString.call(img).slice(8, -1) === 'ImageBitmap') {\n    w = img.width;\n    h = img.height;\n  }\n\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || !('getContext' in canvas)) {\n    return;\n  }\n\n  if (!skipStyles) {\n    canvas.style.width = w + 'px';\n    canvas.style.height = h + 'px';\n  }\n\n  canvas.width = w;\n  canvas.height = h;\n  var context = canvas.getContext('2d');\n  context.clearRect(0, 0, w, h);\n  context.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, w, h);\n\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  if (blurAlphaChannel) {\n    processCanvasRGBA(canvas, 0, 0, w, h, radius);\n  } else {\n    processCanvasRGB(canvas, 0, 0, w, h, radius);\n  }\n}\n/**\n * @param {string|HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @throws {Error|TypeError}\n * @returns {ImageData} See {@link https://html.spec.whatwg.org/multipage/canvas.html#imagedata}\n */\n\n\nfunction getImageDataFromCanvas(canvas, topX, topY, width, height) {\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || _typeof(canvas) !== 'object' || !('getContext' in canvas)) {\n    throw new TypeError('Expecting canvas with `getContext` method ' + 'in processCanvasRGB(A) calls!');\n  }\n\n  var context = canvas.getContext('2d');\n\n  try {\n    return context.getImageData(topX, topY, width, height);\n  } catch (e) {\n    throw new Error('unable to access image data: ' + e);\n  }\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGBA(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGBA(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGBA(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null,\n      stackOut = null,\n      yw = 0,\n      yi = 0;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n\n  for (var y = 0; y < height; y++) {\n    stack = stackStart;\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        pa = pixels[yi + 3];\n\n    for (var _i = 0; _i < radiusPlus1; _i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0,\n        aInSum = 0,\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        aOutSum = radiusPlus1 * pa,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb,\n        aSum = sumFactor * pa;\n\n    for (var _i2 = 1; _i2 < radiusPlus1; _i2++) {\n      var p = yi + ((widthMinus1 < _i2 ? widthMinus1 : _i2) << 2);\n      var r = pixels[p],\n          g = pixels[p + 1],\n          b = pixels[p + 2],\n          a = pixels[p + 3];\n      var rbs = radiusPlus1 - _i2;\n      rSum += (stack.r = r) * rbs;\n      gSum += (stack.g = g) * rbs;\n      bSum += (stack.b = b) * rbs;\n      aSum += (stack.a = a) * rbs;\n      rInSum += r;\n      gInSum += g;\n      bInSum += b;\n      aInSum += a;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      var paInitial = aSum * mulSum >>> shgSum;\n      pixels[yi + 3] = paInitial;\n\n      if (paInitial !== 0) {\n        var _a2 = 255 / paInitial;\n\n        pixels[yi] = (rSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 1] = (gSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 2] = (bSum * mulSum >>> shgSum) * _a2;\n      } else {\n        pixels[yi] = pixels[yi + 1] = pixels[yi + 2] = 0;\n      }\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n\n      var _p = x + radius + 1;\n\n      _p = yw + (_p < widthMinus1 ? _p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[_p];\n      gInSum += stackIn.g = pixels[_p + 1];\n      bInSum += stackIn.b = pixels[_p + 2];\n      aInSum += stackIn.a = pixels[_p + 3];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      aSum += aInSum;\n      stackIn = stackIn.next;\n      var _stackOut = stackOut,\n          _r = _stackOut.r,\n          _g = _stackOut.g,\n          _b = _stackOut.b,\n          _a = _stackOut.a;\n      rOutSum += _r;\n      gOutSum += _g;\n      bOutSum += _b;\n      aOutSum += _a;\n      rInSum -= _r;\n      gInSum -= _g;\n      bInSum -= _b;\n      aInSum -= _a;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x = 0; _x < width; _x++) {\n    yi = _x << 2;\n\n    var _pr = pixels[yi],\n        _pg = pixels[yi + 1],\n        _pb = pixels[yi + 2],\n        _pa = pixels[yi + 3],\n        _rOutSum = radiusPlus1 * _pr,\n        _gOutSum = radiusPlus1 * _pg,\n        _bOutSum = radiusPlus1 * _pb,\n        _aOutSum = radiusPlus1 * _pa,\n        _rSum = sumFactor * _pr,\n        _gSum = sumFactor * _pg,\n        _bSum = sumFactor * _pb,\n        _aSum = sumFactor * _pa;\n\n    stack = stackStart;\n\n    for (var _i3 = 0; _i3 < radiusPlus1; _i3++) {\n      stack.r = _pr;\n      stack.g = _pg;\n      stack.b = _pb;\n      stack.a = _pa;\n      stack = stack.next;\n    }\n\n    var yp = width;\n    var _gInSum = 0,\n        _bInSum = 0,\n        _aInSum = 0,\n        _rInSum = 0;\n\n    for (var _i4 = 1; _i4 <= radius; _i4++) {\n      yi = yp + _x << 2;\n\n      var _rbs = radiusPlus1 - _i4;\n\n      _rSum += (stack.r = _pr = pixels[yi]) * _rbs;\n      _gSum += (stack.g = _pg = pixels[yi + 1]) * _rbs;\n      _bSum += (stack.b = _pb = pixels[yi + 2]) * _rbs;\n      _aSum += (stack.a = _pa = pixels[yi + 3]) * _rbs;\n      _rInSum += _pr;\n      _gInSum += _pg;\n      _bInSum += _pb;\n      _aInSum += _pa;\n      stack = stack.next;\n\n      if (_i4 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y = 0; _y < height; _y++) {\n      var _p2 = yi << 2;\n\n      pixels[_p2 + 3] = _pa = _aSum * mulSum >>> shgSum;\n\n      if (_pa > 0) {\n        _pa = 255 / _pa;\n        pixels[_p2] = (_rSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 1] = (_gSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 2] = (_bSum * mulSum >>> shgSum) * _pa;\n      } else {\n        pixels[_p2] = pixels[_p2 + 1] = pixels[_p2 + 2] = 0;\n      }\n\n      _rSum -= _rOutSum;\n      _gSum -= _gOutSum;\n      _bSum -= _bOutSum;\n      _aSum -= _aOutSum;\n      _rOutSum -= stackIn.r;\n      _gOutSum -= stackIn.g;\n      _bOutSum -= stackIn.b;\n      _aOutSum -= stackIn.a;\n      _p2 = _x + ((_p2 = _y + radiusPlus1) < heightMinus1 ? _p2 : heightMinus1) * width << 2;\n      _rSum += _rInSum += stackIn.r = pixels[_p2];\n      _gSum += _gInSum += stackIn.g = pixels[_p2 + 1];\n      _bSum += _bInSum += stackIn.b = pixels[_p2 + 2];\n      _aSum += _aInSum += stackIn.a = pixels[_p2 + 3];\n      stackIn = stackIn.next;\n      _rOutSum += _pr = stackOut.r;\n      _gOutSum += _pg = stackOut.g;\n      _bOutSum += _pb = stackOut.b;\n      _aOutSum += _pa = stackOut.a;\n      _rInSum -= _pr;\n      _gInSum -= _pg;\n      _bInSum -= _pb;\n      _aInSum -= _pa;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGB(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGB(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGB(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null;\n  var stackOut = null;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n  var p, rbs;\n  var yw = 0,\n      yi = 0;\n\n  for (var y = 0; y < height; y++) {\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb;\n    stack = stackStart;\n\n    for (var _i5 = 0; _i5 < radiusPlus1; _i5++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0;\n\n    for (var _i6 = 1; _i6 < radiusPlus1; _i6++) {\n      p = yi + ((widthMinus1 < _i6 ? widthMinus1 : _i6) << 2);\n      rSum += (stack.r = pr = pixels[p]) * (rbs = radiusPlus1 - _i6);\n      gSum += (stack.g = pg = pixels[p + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[p + 2]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      pixels[yi] = rSum * mulSum >>> shgSum;\n      pixels[yi + 1] = gSum * mulSum >>> shgSum;\n      pixels[yi + 2] = bSum * mulSum >>> shgSum;\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      p = yw + ((p = x + radius + 1) < widthMinus1 ? p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[p];\n      gInSum += stackIn.g = pixels[p + 1];\n      bInSum += stackIn.b = pixels[p + 2];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x2 = 0; _x2 < width; _x2++) {\n    yi = _x2 << 2;\n\n    var _pr2 = pixels[yi],\n        _pg2 = pixels[yi + 1],\n        _pb2 = pixels[yi + 2],\n        _rOutSum2 = radiusPlus1 * _pr2,\n        _gOutSum2 = radiusPlus1 * _pg2,\n        _bOutSum2 = radiusPlus1 * _pb2,\n        _rSum2 = sumFactor * _pr2,\n        _gSum2 = sumFactor * _pg2,\n        _bSum2 = sumFactor * _pb2;\n\n    stack = stackStart;\n\n    for (var _i7 = 0; _i7 < radiusPlus1; _i7++) {\n      stack.r = _pr2;\n      stack.g = _pg2;\n      stack.b = _pb2;\n      stack = stack.next;\n    }\n\n    var _rInSum2 = 0,\n        _gInSum2 = 0,\n        _bInSum2 = 0;\n\n    for (var _i8 = 1, yp = width; _i8 <= radius; _i8++) {\n      yi = yp + _x2 << 2;\n      _rSum2 += (stack.r = _pr2 = pixels[yi]) * (rbs = radiusPlus1 - _i8);\n      _gSum2 += (stack.g = _pg2 = pixels[yi + 1]) * rbs;\n      _bSum2 += (stack.b = _pb2 = pixels[yi + 2]) * rbs;\n      _rInSum2 += _pr2;\n      _gInSum2 += _pg2;\n      _bInSum2 += _pb2;\n      stack = stack.next;\n\n      if (_i8 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x2;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y2 = 0; _y2 < height; _y2++) {\n      p = yi << 2;\n      pixels[p] = _rSum2 * mulSum >>> shgSum;\n      pixels[p + 1] = _gSum2 * mulSum >>> shgSum;\n      pixels[p + 2] = _bSum2 * mulSum >>> shgSum;\n      _rSum2 -= _rOutSum2;\n      _gSum2 -= _gOutSum2;\n      _bSum2 -= _bOutSum2;\n      _rOutSum2 -= stackIn.r;\n      _gOutSum2 -= stackIn.g;\n      _bOutSum2 -= stackIn.b;\n      p = _x2 + ((p = _y2 + radiusPlus1) < heightMinus1 ? p : heightMinus1) * width << 2;\n      _rSum2 += _rInSum2 += stackIn.r = pixels[p];\n      _gSum2 += _gInSum2 += stackIn.g = pixels[p + 1];\n      _bSum2 += _bInSum2 += stackIn.b = pixels[p + 2];\n      stackIn = stackIn.next;\n      _rOutSum2 += _pr2 = stackOut.r;\n      _gOutSum2 += _pg2 = stackOut.g;\n      _bOutSum2 += _pb2 = stackOut.b;\n      _rInSum2 -= _pr2;\n      _gInSum2 -= _pg2;\n      _bInSum2 -= _pb2;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n *\n */\n\n\nvar BlurStack =\n/**\n * Set properties.\n */\nfunction BlurStack() {\n  _classCallCheck(this, BlurStack);\n\n  this.r = 0;\n  this.g = 0;\n  this.b = 0;\n  this.a = 0;\n  this.next = null;\n};\n\nexport { BlurStack, processCanvasRGB as canvasRGB, processCanvasRGBA as canvasRGBA, processImage as image, processImageDataRGB as imageDataRGB, processImageDataRGBA as imageDataRGBA };\n"], "names": ["getNanoSeconds", "hrtime", "loadTime", "moduleLoadTime", "nodeLoadTime", "upTime", "performance", "now", "module", "exports", "process", "hr", "uptime", "Date", "getTime", "require", "root", "window", "global", "vendors", "suffix", "raf", "caf", "i", "length", "last", "id", "queue", "frameDuration", "callback", "_now", "next", "Math", "max", "setTimeout", "cp", "slice", "cancelled", "e", "round", "push", "handle", "fn", "call", "cancel", "apply", "arguments", "polyfill", "object", "requestAnimationFrame", "cancelAnimationFrame", "color_string", "this", "ok", "alpha", "char<PERSON>t", "substr", "replace", "toLowerCase", "simple_colors", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "dodgerblue", "feldspar", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "<PERSON><PERSON>rey", "lightgreen", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslateblue", "lightslategray", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "violetred", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "color_defs", "re", "example", "bits", "parseInt", "parseFloat", "processor", "exec", "channels", "r", "g", "b", "isNaN", "toRGB", "toRGBA", "toHex", "toString", "getHelpXML", "examples", "Array", "j", "sc", "xml", "document", "createElement", "setAttribute", "list_item", "list_color", "RGBColor", "example_div", "style", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "list_item_value", "t", "Object", "setPrototypeOf", "__proto__", "prototype", "hasOwnProperty", "TypeError", "String", "constructor", "create", "cos", "sin", "a", "Error", "n", "PI", "o", "lArcFlag", "sweepFlag", "rX", "rY", "s", "x", "u", "y", "abs", "h", "xRot", "c", "p", "pow", "sqrt", "m", "O", "l", "T", "v", "cX", "cY", "phi1", "atan2", "phi2", "relative", "x1", "y1", "x2", "y2", "NaN", "type", "_", "SMOOTH_CURVE_TO", "CURVE_TO", "SMOOTH_QUAD_TO", "QUAD_TO", "MOVE_TO", "CLOSE_PATH", "HORIZ_LINE_TO", "LINE_TO", "VERT_LINE_TO", "f", "N", "d", "E", "A", "C", "M", "R", "I", "S", "L", "ROUND", "TO_ABS", "TO_REL", "NORMALIZE_HVZ", "ARC", "NORMALIZE_ST", "QT_TO_C", "INFO", "SANITIZE", "LINE_COMMANDS", "MATRIX", "ROTATE", "TRANSLATE", "SCALE", "SKEW_X", "atan", "SKEW_Y", "X_AXIS_SYMMETRY", "Y_AXIS_SYMMETRY", "A_TO_C", "min", "ceil", "H", "ANNOTATE_ARCS", "CLONE", "CALCULATE_BOUNDS", "maxX", "minX", "maxY", "minY", "DRAWING_COMMANDS", "w", "map", "U", "transform", "toAbs", "toRel", "normalizeHVZ", "normalizeST", "qtToC", "aToC", "sanitize", "translate", "scale", "rotate", "matrix", "skewX", "skewY", "xSymmetry", "ySymmetry", "annotateArcs", "charCodeAt", "curN<PERSON>ber", "curCommandType", "curCommandRelative", "canParseCommandOrComma", "curNumberHasExp", "curNumberHasExpDigits", "curNumberHasDecimal", "curArgs", "finish", "parse", "SyntaxError", "Number", "value", "getPrototypeOf", "isArray", "commands", "encode", "getBounds", "_typeof", "obj", "Symbol", "iterator", "mulTable", "shgTable", "getImageDataFromCanvas", "canvas", "topX", "topY", "width", "height", "getElementById", "context", "getContext", "getImageData", "processCanvasRGBA", "radius", "imageData", "stackEnd", "pixels", "data", "div", "widthMinus1", "heightMinus1", "radiusPlus1", "sumFactor", "stackStart", "BlurStack", "stack", "stackIn", "stackOut", "yw", "yi", "mulSum", "shgSum", "pr", "pg", "pb", "pa", "_i", "rInSum", "gInSum", "bInSum", "aInSum", "rOutSum", "gOutSum", "bOutSum", "aOutSum", "rSum", "gSum", "bSum", "aSum", "_i2", "rbs", "paInitial", "_a2", "_p", "_stackOut", "_r", "_g", "_b", "_a", "_x", "_pr", "_pg", "_pb", "_pa", "_rOutSum", "_gOutSum", "_bOutSum", "_aOutSum", "_rSum", "_gSum", "_bSum", "_aSum", "_i3", "yp", "_gInSum", "_bInSum", "_aInSum", "_rInSum", "_i4", "_rbs", "_y", "_p2", "processImageDataRGBA", "putImageData", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_classCallCheck"], "sourceRoot": ""}
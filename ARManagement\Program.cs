using ARManagement.BaseRepository.Implement;
using ARManagement.BaseRepository.Interface;
using ARManagement.Data;
using ARManagement.Helpers;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.OpenApi.Models;
using Models;
using System.Reflection;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "生成式售服平台 - 管理系統 API",
        Version = "v1",
        Description = "生成式售服平台 - 管理系統後端API文檔，提供完整的設備管理、知識庫、SOP操作程序等功能介面"
    });

    // 啟用 XML 註解
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }

    // API 分組標籤 - 對應您的圖片範例
    c.TagActionsBy(api =>
    {
        var controllerName = api.ActionDescriptor.RouteValues["controller"];
        return controllerName switch
        {
            "Login" => new[] { "用戶驗證 Authentication" },
            "Machine" => new[] { "機台管理 Machine Management" },
            "MachineAdd" => new[] { "機台資料 MachineAdd Data" },
            "Device" => new[] { "設備管理 Device Management" },
            "Knowledge" => new[] { "知識庫 Knowledge Base" },
            "SOP" => new[] { "標準作業程序 SOP" },
            "SOP2" => new[] { "SOP2管理 SOP2 Management" },
            "IOT" => new[] { "物聯網數據 IoT Data" },
            "Userinfo" => new[] { "用戶資訊 User Information" },
            "VendorRegistration" => new[] { "廠商註冊 Vendor Registration" },
            "Pdf" => new[] { "PDF文件 PDF Documents" },
            "Ping" => new[] { "系統監控 System Monitoring" },
            "MachineAlarm" => new[] { "機台警報 Machine Alarms" },
            _ => new[] { controllerName ?? "其他 Others" }
        };
    });

    // 確保每個標籤都有描述
    c.DocInclusionPredicate((name, api) => true);
});

#region DBHelper
builder.Services.Configure<PostgreSqlDBConfig>(builder.Configuration.GetSection("DBConfig"));
builder.Services.AddTransient<IDatabaseHelper, DatabaseHelper>();

//builder.Services.AddDbContext<ARManagementContext>(options =>
//        options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));
//builder.Services.AddDbContext<ARManagementContext>(options =>
//    options.UseNpgsql(builder.Configuration["DBConfig:ConnectionString"]));

// 下面這個是最後一個使用的版本，還原以此優先
//builder.Services.AddDbContext<ARManagementContext>(options =>
//    options.UseNpgsql(builder.Configuration["DBConfig:ConnectionString"]), ServiceLifetime.Scoped);
builder.Services.AddDbContext<ARManagementContext>((serviceProvider, options) =>
{
    var httpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();
    var configuration = serviceProvider.GetRequiredService<IConfiguration>();
    var schema = httpContextAccessor.HttpContext?.Items["SchemaName"] as string ?? "public";
    var connectionString = configuration["DBConfig:ConnectionString"] + $"SearchPath={schema};";
    options.UseNpgsql(connectionString);
}, ServiceLifetime.Scoped);


#endregion DBHelperss

#region Repository 注入
builder.Services.AddTransient<IBaseRepository, BaseRepository>();
#endregion

#region Localizer�h��y��
builder.Services.AddSingleton<ResponseCodeHelper>();
#endregion

#region CORS
// CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("corsapp",
        builder =>
        {
            builder.AllowAnyOrigin()
                .AllowAnyHeader()
                .AllowAnyMethod();
        });
});
#endregion

#region JWT
builder.Services.AddSingleton<JwtHelper>();
#endregion

#region SMTP Settings - 註釋多租戶Email功能
// 註釋SMTP服務，停用Email驗證功能
// builder.Services.Configure<SmtpSettings>(builder.Configuration.GetSection("SMTP"));

// 覆蓋 Password 配置，從環境變數中讀取 SMTP 密碼 ( 增加SMTP安全性，設置環境變數 )
// builder.Services.PostConfigure<SmtpSettings>(options =>
// {
//     options.Password = Environment.GetEnvironmentVariable("SMTP_PASSWORD");
// });
#endregion

builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<IBaseRepository, BaseRepository>();

builder.Logging.AddConsole();  // 確保控制台日誌被啟用

// 增加最大請求體大小限制 (PDF備份)
builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = int.MaxValue;
});

builder.Services.Configure<KestrelServerOptions>(options =>
{
    options.Limits.MaxRequestBodySize = int.MaxValue;
});

var app = builder.Build();

// 使用中間件  (PDF備份)
app.Use(async (context, next) =>
{
    context.Features.Get<IHttpMaxRequestBodySizeFeature>().MaxRequestBodySize = null;
    await next.Invoke();
});

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment() || app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

//app cors
app.UseCors("corsapp");

// Register Schema Middleware
app.UseMiddleware<SchemaMiddleware>();

//app.UseStaticFiles(new StaticFileOptions
//{
//    OnPrepareResponse = ctx =>
//    {
//        ctx.Context.Response.Headers.Append("Access-Control-Allow-Origin", "*");
//    }
//});
app.UseStaticFiles(new StaticFileOptions
{
    OnPrepareResponse = ctx =>
    {
        ctx.Context.Response.Headers.Append("Access-Control-Allow-Origin", "*"); // 允許所有來源跨域
        ctx.Context.Response.Headers.Append("Access-Control-Allow-Headers", "Content-Type, Authorization");
        ctx.Context.Response.Headers.Append("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    }
});

app.UseRouting();

app.UseAuthorization();

app.MapControllers();

// 配置SPA fallback - 當找不到API路由時，返回index.html
app.MapFallbackToFile("index.html");

app.Run();
